#!/usr/bin/env python3
"""
Validation Script for Refactoring

This script validates that the refactoring was successful by testing:
1. Module imports work correctly
2. Backward compatibility is maintained
3. Security validation works as expected

Run this script to verify the refactoring is working properly.
"""

import sys
import os
import traceback
from typing import List, Tuple

def test_import(module_name: str, description: str) -> Tuple[bool, str]:
    """Test if a module can be imported successfully"""
    try:
        __import__(module_name)
        return True, f"✅ {description}: Import successful"
    except Exception as e:
        return False, f"❌ {description}: Import failed - {str(e)}"

def test_function_import(module_name: str, function_name: str, description: str) -> Tuple[bool, str]:
    """Test if a specific function can be imported from a module"""
    try:
        module = __import__(module_name, fromlist=[function_name])
        getattr(module, function_name)
        return True, f"✅ {description}: Function import successful"
    except Exception as e:
        return False, f"❌ {description}: Function import failed - {str(e)}"

def main():
    """Run all validation tests"""
    print("🔧 VALIDATING SALESFORCE INTEGRATION REFACTORING")
    print("=" * 60)
    
    tests = []
    
    # Test 1: Core module imports
    print("\n📦 Testing Core Module Imports...")
    tests.append(test_import("salesforce_integration.orchestrators", "Orchestrators module"))
    tests.append(test_import("salesforce_integration.airflow_tasks", "Airflow tasks module"))
    tests.append(test_import("salesforce_integration.reports", "Reports module"))
    tests.append(test_import("salesforce_integration.monitoring", "Monitoring module"))
    
    # Test 2: Specific component imports
    print("\n🏗️ Testing Component Imports...")
    tests.append(test_function_import("salesforce_integration.airflow_tasks.extraction_tasks", 
                                     "airflow_extract_task", "Main extraction task"))
    tests.append(test_function_import("salesforce_integration.airflow_tasks.transformation_tasks", 
                                     "airflow_transform_task", "Main transformation task"))
    tests.append(test_function_import("salesforce_integration.airflow_tasks.loading_tasks", 
                                     "airflow_load_task", "Main loading task"))
    tests.append(test_function_import("salesforce_integration.reports.execution_reporter", 
                                     "generate_execution_report", "Execution reporter"))
    
    # Test 3: Backward compatibility
    print("\n🔄 Testing Backward Compatibility...")
    tests.append(test_function_import("salesforce_integration.airflow_tasks", 
                                     "airflow_extract_task", "Backward compatible extraction task"))
    tests.append(test_function_import("salesforce_integration.airflow_tasks", 
                                     "airflow_transform_task", "Backward compatible transformation task"))
    tests.append(test_function_import("salesforce_integration.airflow_tasks", 
                                     "airflow_load_task", "Backward compatible loading task"))
    
    # Test 4: Security validation
    print("\n🔒 Testing Security Validation...")
    try:
        from salesforce_integration.config.config import validate_configuration
        try:
            validate_configuration()
            tests.append((False, "❌ Security validation: Should fail without credentials"))
        except ValueError as e:
            if "Missing required environment variables" in str(e):
                tests.append((True, "✅ Security validation: Correctly fails without credentials"))
            else:
                tests.append((False, f"❌ Security validation: Unexpected error - {str(e)}"))
    except Exception as e:
        tests.append((False, f"❌ Security validation: Import failed - {str(e)}"))
    
    # Print all test results
    print("\n" + "=" * 60)
    print("📊 TEST RESULTS SUMMARY")
    print("=" * 60)
    
    passed = 0
    failed = 0
    
    for success, message in tests:
        print(message)
        if success:
            passed += 1
        else:
            failed += 1
    
    print("\n" + "=" * 60)
    print(f"📈 FINAL RESULTS: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 ALL TESTS PASSED! Refactoring is successful.")
        return 0
    else:
        print("⚠️ SOME TESTS FAILED! Please check the errors above.")
        return 1

if __name__ == "__main__":
    sys.exit(main())
