# Guia de Unidades de Negócio - Separação Consórcio vs Seguros

## Visão Geral

O sistema ETL foi reestruturado para suportar múltiplas unidades de negócio com separação completa entre **Consórcio** e **Seguros**. Cada unidade possui:

- **Fontes de dados específicas**
- **Data Extensions distintas**
- **Credenciais Salesforce separadas**
- **DAGs independentes**
- **Configurações isoladas**

## Arquitetura

```
salesforce_integration/
├── core/                           # Componentes compartilhados
│   ├── base_dag_factory.py        # Fábrica base de DAGs
│   └── __init__.py
├── business_units/                 # Unidades de negócio específicas
│   ├── consorcio/
│   │   ├── config.py              # Config específica do consórcio
│   │   ├── dag_consorcio.py       # DAG do consórcio
│   │   └── __init__.py
│   └── seguros/
│       ├── config.py              # Config específica de seguros
│       ├── dag_seguros.py         # DAG de seguros
│       └── __init__.py
├── business_unit_adapter.py       # Adaptador para compatibilidade
├── airflow_task_wrappers.py      # Wrappers das funções existentes
└── ... (arquivos existentes)
```

## Unidades de Negócio

### Consórcio
- **Fontes**: NewCon, RD Station, Orbbits
- **Schedule**: 8h da manhã (`0 8 * * *`)
- **DAG ID**: `SALESFORCE-CONSORCIO`
- **Salesforce Client ID**: `bneq1jhgjhuhsekg68zchecl`

### Seguros  
- **Fontes**: Quiver
- **Schedule**: 9h da manhã (`0 9 * * *`)
- **DAG ID**: `SALESFORCE-SEGUROS`
- **Salesforce Client ID**: `ljmwlofrhxbmnhdgvyivwpy1`

## Data Extensions

### Consórcio (Existentes)
```python
DATA_EXTENSIONS_CONSORCIO = {
    'tb_clientes': '6E536388-F5A4-416D-B0DE-AABE229F33C1',
    'tb_leads': '7C8AEDB6-64E8-41FC-965F-A4C37DF0ABE0',
    'tb_produtos': '19BEF707-C44F-4C3A-A9FB-53D2915258F1',
    'tb_propostas': '9FC20C5F-04EC-4B86-9491-D649ECFACAA5'
}
```

### Seguros (Novas)
```python
DATA_EXTENSIONS_SEGUROS = {
    'tb_clientes': 'B2B7ACFF-D5C8-4C86-B04A-DB4FBBA9198E',
    'tb_leads': 'EC0B7BFF-EC89-4A4D-914B-749F14B6F861',
    'tb_produtos': 'FCC1DCA7-D286-458D-BDCC-D050C1BA61A8',
    'tb_propostas': '36B6F6B2-ECC3-4550-828C-5BF3B12FCBCA'
}
```

## Como Usar

### Ativação das DAGs

```bash
# Ativar DAG do consórcio
airflow dags unpause SALESFORCE-CONSORCIO

# Ativar DAG de seguros  
airflow dags unpause SALESFORCE-SEGUROS
```

### Execução Manual

```bash
# Executar consórcio
airflow dags trigger SALESFORCE-CONSORCIO

# Executar seguros
airflow dags trigger SALESFORCE-SEGUROS
```

### Variáveis de Ambiente

Para execução standalone (fora do Airflow), você pode especificar a unidade:

```bash
# Para consórcio
export BUSINESS_UNIT=consorcio
python etl_main.py

# Para seguros
export BUSINESS_UNIT=seguros
python etl_main.py
```

## Logs e Monitoramento

Cada unidade de negócio possui contexto de log separado:
- **business_unit**: consorcio ou seguros
- **sources**: fontes específicas da unidade
- **data_extensions**: extensões específicas

## Compatibilidade

O sistema mantém **100% de compatibilidade** com:
- ✅ Código existente do `etl_main.py`
- ✅ Funções de extração, transformação e carregamento
- ✅ Configurações de banco de dados
- ✅ Modo CSV e Salesforce

## Vantagens

### Sustentabilidade
- Fácil adição de novas unidades de negócio
- Arquitetura baseada em configuração
- Reutilização máxima de código

### Isolamento
- Falhas em uma unidade não afetam a outra
- Configurações independentes
- Execução paralela ou sequencial

### Manutenibilidade
- Configurações centralizadas por unidade
- Logs específicos por contexto
- Troubleshooting facilitado

## Próximos Passos

1. **Testar DAGs**: Verificar criação das DAGs no Airflow
2. **Validar Configurações**: Confirmar credenciais e Data Extensions
3. **Executar Testes**: Rodar pipeline completo para cada unidade
4. **Monitoramento**: Configurar alertas específicos por unidade

## Suporte

Para adicionar uma nova unidade de negócio:

1. Criar diretório em `business_units/nova_unidade/`
2. Criar `config.py` com configurações específicas
3. Criar `dag_nova_unidade.py` usando a fábrica base
4. Atualizar wrappers se necessário

A arquitetura foi projetada para ser **extensível** e **sustentável** a longo prazo.