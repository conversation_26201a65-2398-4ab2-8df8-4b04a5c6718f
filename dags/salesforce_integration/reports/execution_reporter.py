#!/usr/bin/env python3
"""
Execution Reporter - Extracted from etl_main.py

Functions for generating execution reports and DAG templates.
This module was extracted from the monolithic etl_main.py file as part of the refactoring effort.
"""

from datetime import datetime
from typing import Dict, Any


def generate_execution_report(pipeline_result: Dict[str, Any]) -> str:
    """Gera relatório de execução com sistema de falhas integrado"""
    if not pipeline_result['success']:
        return f"❌ PIPELINE FALHOU: {pipeline_result.get('error', 'Erro desconhecido')}"
    
    # Prioriza o relatório de falhas se disponível
    if 'failure_report' in pipeline_result and pipeline_result['failure_report'].strip():
        return pipeline_result['failure_report']
    
    # Fallback para relatório simples se não houver falhas detectadas
    stats = pipeline_result['execution_stats']
    load_result = pipeline_result['load_result']
    
    report = f"""
===== RELATÓRIO DE EXECUÇÃO ETL =====
📅 {datetime.now().strftime('%Y-%m-%d %H:%M:%S')} | ⏱️ Duração: {stats['total_time']/60:.0f}min | 📊 Status: ✅ SUCESSO

📊 RESUMO EXECUTIVO
"""
    
    for table_name, table_result in load_result.get('processing_summary', {}).items():
        status_icon = "✅" if table_result.get('success', True) else "❌"
        report += f"├─ {table_name}: {status_icon} {table_result.get('total_records', 0):,} registros | {table_result.get('total_batches', 0)} lotes\n"
    
    report += f"\n🎯 RESULTADO: Pipeline executado com sucesso - {stats['total_records']:,} registros processados"
    
    return report


def create_dag_template() -> str:
    """Cria template de DAG para Airflow"""
    template = '''
from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from salesforce_integration.airflow_tasks import (
    airflow_extract_task, 
    airflow_transform_task, 
    airflow_load_task
)

default_args = {
    'owner': 'etl_team',
    'depends_on_past': False,
    'start_date': datetime(2025, 1, 1),
    'email_on_failure': True,
    'email_on_retry': False,
    'retries': 3,
    'retry_delay': timedelta(minutes=5),
}

dag = DAG(
    'etl_salesforce_marketing_cloud',
    default_args=default_args,
    description='ETL Pipeline para Salesforce Marketing Cloud',
    schedule_interval='0 8 * * *',  # Diário às 8h
    catchup=False,
    max_active_runs=1,
)

# Task de extração
extract_task = PythonOperator(
    task_id='extract_data',
    python_callable=airflow_extract_task,
    dag=dag,
)

# Task de transformação
transform_task = PythonOperator(
    task_id='transform_data',
    python_callable=airflow_transform_task,
    dag=dag,
)

# Tasks de carregamento por tabela
load_products_task = PythonOperator(
    task_id='load_products',
    python_callable=airflow_load_task,
    op_args=['tb_produtos'],
    dag=dag,
)

load_clients_task = PythonOperator(
    task_id='load_clients',
    python_callable=airflow_load_task,
    op_args=['tb_clientes'],
    dag=dag,
)

load_leads_task = PythonOperator(
    task_id='load_leads',
    python_callable=airflow_load_task,
    op_args=['tb_leads'],
    dag=dag,
)

load_proposals_task = PythonOperator(
    task_id='load_proposals',
    python_callable=airflow_load_task,
    op_args=['tb_propostas'],
    dag=dag,
)

# Sequência de dependências
extract_task >> transform_task >> [
    load_products_task,
    load_clients_task,
    load_leads_task,
    load_proposals_task
]
'''
    return template
