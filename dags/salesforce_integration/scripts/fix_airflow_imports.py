#!/usr/bin/env python3
"""
Script para corrigir imports do airflow_task_wrappers nos arquivos de configuração
"""

import os
import re
from pathlib import Path

def fix_config_file(file_path):
    """Corrige as referências do airflow_task_wrappers em um arquivo de configuração"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Corrigir referências do airflow_task_wrappers
        content = content.replace(
            'salesforce_integration.airflow_task_wrappers.',
            'salesforce_integration.airflow_tasks.airflow_task_wrappers.'
        )
        
        # Se houve mudanças, salvar o arquivo
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Corrigido: {file_path}")
            return True
        else:
            print(f"⏭️ Sem mudanças: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao processar {file_path}: {e}")
        return False

def main():
    """Função principal"""
    print("🔄 Corrigindo referências do airflow_task_wrappers...")
    
    # Diretório base
    base_dir = Path(__file__).parent.parent
    
    # Arquivos de configuração das business units
    config_files = [
        base_dir / "data/business_units/consorcio/config.py",
        base_dir / "data/business_units/seguros/config.py"
    ]
    
    updated_count = 0
    
    # Corrigir cada arquivo
    for config_file in config_files:
        if config_file.exists():
            if fix_config_file(config_file):
                updated_count += 1
        else:
            print(f"⚠️ Arquivo não encontrado: {config_file}")
    
    print(f"\n🎉 Correção concluída!")
    print(f"📊 {updated_count} arquivos atualizados de {len(config_files)} total")

if __name__ == "__main__":
    main()
