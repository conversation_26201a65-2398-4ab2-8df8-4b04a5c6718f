#!/usr/bin/env python3
"""
Script para testar se a correção de configuração funcionou
"""

import sys
import os

# Adicionar ao path
sys.path.append('/opt/airflow/dags')
sys.path.append('/home/<USER>/projects/airflow-v1/dags')

def test_config_fix():
    """Testa se a correção de configuração funcionou"""
    print("🧪 TESTE DA CORREÇÃO DE CONFIGURAÇÃO")
    print("=" * 45)
    
    # 1. Verificar configuração global original
    print("\n1️⃣ CONFIGURAÇÃO GLOBAL ORIGINAL:")
    from salesforce_integration.config.config import DATA_EXTENSIONS
    
    tb_propostas_global = DATA_EXTENSIONS.get('tb_propostas', {})
    global_key = tb_propostas_global.get('external_key', 'NÃO ENCONTRADO')
    print(f"   tb_propostas external_key: {global_key}")
    
    # 2. Criar adapter e aplicar patch
    print("\n2️⃣ APLICANDO PATCH DE BUSINESS UNIT:")
    from salesforce_integration.src.utils.business_unit_adapter import BusinessUnitAdapter, patch_config_for_business_unit
    
    adapter = BusinessUnitAdapter('consorcio')
    patch_config_for_business_unit(adapter)
    
    # 3. Verificar se a configuração global mudou
    print("\n3️⃣ CONFIGURAÇÃO GLOBAL APÓS PATCH:")
    # Reimportar para ver mudanças
    import importlib
    import salesforce_integration.config.config
    importlib.reload(salesforce_integration.config.config)
    
    from salesforce_integration.config.config import DATA_EXTENSIONS as DATA_EXTENSIONS_AFTER
    
    tb_propostas_after = DATA_EXTENSIONS_AFTER.get('tb_propostas', {})
    after_key = tb_propostas_after.get('external_key', 'NÃO ENCONTRADO')
    print(f"   tb_propostas external_key: {after_key}")
    
    # 4. Testar SalesforceClient com configuração dinâmica
    print("\n4️⃣ TESTANDO SALESFORCE CLIENT:")
    try:
        from salesforce_integration.src.clients.salesforce_client import SalesforceClient, get_current_config
        
        # Testar função de configuração dinâmica
        current_config = get_current_config()
        client_data_extensions = current_config['DATA_EXTENSIONS']
        
        tb_propostas_client = client_data_extensions.get('tb_propostas', {})
        client_key = tb_propostas_client.get('external_key', 'NÃO ENCONTRADO')
        print(f"   tb_propostas external_key NO CLIENT: {client_key}")
        
        # Testar criação do client
        print("   Criando SalesforceClient...")
        client = SalesforceClient()
        print("   ✅ SalesforceClient criado com sucesso!")
        
    except Exception as e:
        print(f"   ❌ Erro ao testar client: {e}")
        import traceback
        traceback.print_exc()
    
    # 5. Simular o que acontece durante execução de task
    print("\n5️⃣ SIMULANDO EXECUÇÃO DE TASK:")
    try:
        # Simular o que o airflow_task_wrapper faz
        from salesforce_integration.src.utils.business_unit_adapter import create_business_unit_adapter
        
        # Simular context do Airflow
        mock_context = {
            'dag': type('MockDag', (), {'dag_id': 'SALESFORCE-CONSORCIO'})(),
            'task': type('MockTask', (), {'task_id': 'test_task'})()
        }
        
        print("   Criando adapter via create_business_unit_adapter...")
        task_adapter = create_business_unit_adapter(**mock_context)
        print(f"   Business unit detectada: {task_adapter.business_unit}")
        
        print("   Aplicando patch...")
        patch_config_for_business_unit(task_adapter)
        
        print("   Verificando configuração após patch de task...")
        task_config = get_current_config()
        task_data_extensions = task_config['DATA_EXTENSIONS']
        
        tb_propostas_task = task_data_extensions.get('tb_propostas', {})
        task_key = tb_propostas_task.get('external_key', 'NÃO ENCONTRADO')
        print(f"   tb_propostas external_key APÓS TASK PATCH: {task_key}")
        
    except Exception as e:
        print(f"   ❌ Erro na simulação de task: {e}")
        import traceback
        traceback.print_exc()
    
    # 6. Resumo final
    print("\n📊 RESUMO FINAL:")
    print(f"   Global Original: {global_key}")
    print(f"   Após Patch Manual: {after_key}")
    print(f"   Client Config: {client_key}")
    print(f"   Task Config: {task_key}")
    
    # Verificar se a correção funcionou
    expected_key = "6C9AFF4A-6D83-48BA-814C-ADFAC3B41F58"  # Chave do consórcio
    
    if client_key == expected_key and task_key == expected_key:
        print(f"\n✅ CORREÇÃO FUNCIONOU!")
        print(f"   O SalesforceClient agora usa a configuração correta do consórcio")
        print(f"   Chave esperada: {expected_key}")
        print(f"   Chave obtida: {client_key}")
        return True
    else:
        print(f"\n❌ CORREÇÃO NÃO FUNCIONOU COMPLETAMENTE")
        print(f"   Chave esperada: {expected_key}")
        print(f"   Client: {client_key}")
        print(f"   Task: {task_key}")
        return False

if __name__ == "__main__":
    success = test_config_fix()
    sys.exit(0 if success else 1)
