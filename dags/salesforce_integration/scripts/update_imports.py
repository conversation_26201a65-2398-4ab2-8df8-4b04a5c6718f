#!/usr/bin/env python3
"""
Script para atualizar imports após reorganização de pastas
"""

import os
import re
from pathlib import Path

# Mapeamento de arquivos antigos para novos caminhos
IMPORT_MAPPINGS = {
    # Arquivos movidos para src/ - corrigir imports antigos
    'from salesforce_integration.src.extractors.data_extractors import': 'from salesforce_integration.src.extractors.data_extractors import',
    'from salesforce_integration.src.transformers.data_transformers import': 'from salesforce_integration.src.transformers.data_transformers import',
    'from salesforce_integration.src.clients.salesforce_client import': 'from salesforce_integration.src.clients.salesforce_client import',
    'from salesforce_integration.src.connections.database_connections import': 'from salesforce_integration.src.connections.database_connections import',
    'from salesforce_integration.src.utils.utils import': 'from salesforce_integration.src.utils.utils import',
    'from salesforce_integration.src.utils.sql_loader import': 'from salesforce_integration.src.utils.sql_loader import',
    'from salesforce_integration.src.utils.test_data_manager import': 'from salesforce_integration.src.utils.test_data_manager import',
    'from salesforce_integration.src.utils.business_unit_adapter import': 'from salesforce_integration.src.utils.business_unit_adapter import',
    'from salesforce_integration.src.utils.business_unit_transformers import': 'from salesforce_integration.src.utils.business_unit_transformers import',

    # Arquivo de configuração
    'from salesforce_integration.config.config import': 'from salesforce_integration.config.config import',

    # Airflow task wrappers
    'from salesforce_integration.airflow_tasks.airflow_task_wrappers import': 'from salesforce_integration.airflow_tasks.airflow_task_wrappers import',

    # Benchmark
    'from salesforce_integration.tools.benchmark_performance import': 'from salesforce_integration.tools.benchmark_performance import',

    # Imports simples (sem from)
    'import salesforce_integration.src.extractors.data_extractors': 'import salesforce_integration.src.extractors.data_extractors',
    'import salesforce_integration.src.transformers.data_transformers': 'import salesforce_integration.src.transformers.data_transformers',
    'import salesforce_integration.src.clients.salesforce_client': 'import salesforce_integration.src.clients.salesforce_client',
    'import salesforce_integration.src.connections.database_connections': 'import salesforce_integration.src.connections.database_connections',
    'import salesforce_integration.src.utils.utils': 'import salesforce_integration.src.utils.utils',
    'import salesforce_integration.config.config': 'import salesforce_integration.config.config',
}

# Mapeamentos de caminhos de arquivos
PATH_MAPPINGS = {
    # Caminhos de consultas SQL
    "'data/queries'": "'data/queries'",
    '"data/queries"': '"data/queries"',
    'data/queries/': 'data/queries/',

    # Caminhos de business units
    "'data/business_units'": "'data/business_units'",
    '"data/business_units"': '"data/business_units"',
    'data/business_units/': 'data/data/business_units/',

    # Caminhos de carga fria
    "'data/cold_load'": "'data/cold_load'",
    '"data/cold_load"': '"data/cold_load"',
    'data/cold_load/': 'data/cold_load/',

    # Caminhos relativos que precisam ser ajustados
    "os.path.join(os.path.dirname(__file__), 'data/queries')": "os.path.join(os.path.dirname(__file__), '../../data/queries')",
    "os.path.join(os.path.dirname(__file__), 'data/business_units')": "os.path.join(os.path.dirname(__file__), '../../data/business_units')",
    "os.path.join(os.path.dirname(__file__), 'data/cold_load')": "os.path.join(os.path.dirname(__file__), '../../data/cold_load')",
}

def update_file_imports(file_path):
    """Atualiza imports em um arquivo específico"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Aplicar mapeamentos de imports
        for old_import, new_import in IMPORT_MAPPINGS.items():
            content = content.replace(old_import, new_import)

        # Aplicar mapeamentos de caminhos
        for old_path, new_path in PATH_MAPPINGS.items():
            content = content.replace(old_path, new_path)
        
        # Se houve mudanças, salvar o arquivo
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ Atualizado: {file_path}")
            return True
        else:
            print(f"⏭️ Sem mudanças: {file_path}")
            return False
            
    except Exception as e:
        print(f"❌ Erro ao processar {file_path}: {e}")
        return False

def find_python_files(directory):
    """Encontra todos os arquivos Python no diretório"""
    python_files = []
    for root, dirs, files in os.walk(directory):
        # Pular __pycache__ e .git
        dirs[:] = [d for d in dirs if d not in ['__pycache__', '.git', 'logs']]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(os.path.join(root, file))
    
    return python_files

def main():
    """Função principal"""
    print("🔄 Iniciando atualização de imports...")
    
    # Diretório base
    base_dir = Path(__file__).parent.parent
    
    # Encontrar todos os arquivos Python
    python_files = find_python_files(base_dir)
    
    print(f"📁 Encontrados {len(python_files)} arquivos Python")
    
    updated_count = 0
    
    # Atualizar cada arquivo
    for file_path in python_files:
        if update_file_imports(file_path):
            updated_count += 1
    
    print(f"\n🎉 Atualização concluída!")
    print(f"📊 {updated_count} arquivos atualizados de {len(python_files)} total")

if __name__ == "__main__":
    main()
