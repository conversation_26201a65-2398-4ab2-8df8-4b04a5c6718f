#!/usr/bin/env python3
"""
Script para limpar caches Python que podem estar causando problemas no Docker
"""

import os
import shutil
import subprocess
from pathlib import Path

def clean_python_cache(directory):
    """Remove todos os arquivos de cache Python de um diretório"""
    cache_count = 0
    
    for root, dirs, files in os.walk(directory):
        # Remover arquivos .pyc
        for file in files:
            if file.endswith('.pyc'):
                file_path = os.path.join(root, file)
                try:
                    os.remove(file_path)
                    cache_count += 1
                except Exception as e:
                    print(f"⚠️ Erro ao remover {file_path}: {e}")
        
        # Remover diretórios __pycache__
        for dir_name in dirs[:]:  # Usar slice para modificar lista durante iteração
            if dir_name == '__pycache__':
                dir_path = os.path.join(root, dir_name)
                try:
                    shutil.rmtree(dir_path)
                    dirs.remove(dir_name)  # Remover da lista para não tentar entrar nele
                    cache_count += 1
                    print(f"🗑️ Removido: {dir_path}")
                except Exception as e:
                    print(f"⚠️ Erro ao remover {dir_path}: {e}")
    
    return cache_count

def restart_airflow_services():
    """Tenta reiniciar os serviços do Airflow via Docker Compose"""
    try:
        print("🔄 Tentando reiniciar serviços do Airflow...")
        
        # Verificar se docker-compose está disponível
        result = subprocess.run(['docker-compose', '--version'], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Docker Compose encontrado")
            
            # Reiniciar serviços
            print("🔄 Reiniciando webserver...")
            subprocess.run(['docker-compose', 'restart', 'airflow-webserver'], 
                         capture_output=True)
            
            print("🔄 Reiniciando scheduler...")
            subprocess.run(['docker-compose', 'restart', 'airflow-scheduler'], 
                         capture_output=True)
            
            print("✅ Serviços reiniciados")
            
        else:
            print("⚠️ Docker Compose não encontrado - reinicie manualmente")
            
    except Exception as e:
        print(f"⚠️ Erro ao reiniciar serviços: {e}")
        print("💡 Reinicie manualmente com: docker-compose restart airflow-webserver airflow-scheduler")

def main():
    """Função principal"""
    print("🧹 LIMPEZA DE CACHE PYTHON PARA DOCKER")
    print("=" * 50)
    
    # Diretório base
    base_dir = Path(__file__).parent.parent
    print(f"📁 Diretório base: {base_dir}")
    
    # Limpar cache Python
    print("\n🗑️ Removendo arquivos de cache Python...")
    cache_count = clean_python_cache(base_dir)
    print(f"✅ {cache_count} arquivos/diretórios de cache removidos")
    
    # Verificar se estamos em ambiente Docker
    if os.path.exists('/.dockerenv'):
        print("\n🐳 Ambiente Docker detectado")
        
        # Limpar cache Python do sistema
        try:
            import py_compile
            py_compile.compile('/dev/null', doraise=True)
        except:
            pass
        
        print("✅ Cache do sistema limpo")
        
    else:
        print("\n💻 Ambiente local detectado")
        
        # Tentar reiniciar serviços do Airflow
        restart_airflow_services()
    
    print("\n📋 INSTRUÇÕES PARA COMPLETAR A LIMPEZA:")
    print("-" * 40)
    print("1. Se estiver usando Docker Compose:")
    print("   docker-compose restart airflow-webserver airflow-scheduler")
    print()
    print("2. Se estiver usando Docker standalone:")
    print("   docker restart <container-name>")
    print()
    print("3. Aguarde alguns minutos para o Airflow recarregar")
    print()
    print("4. Verifique os logs do Airflow para confirmar que os imports estão funcionando")
    print()
    print("🎯 TESTE ESPECÍFICO PARA VERIFICAR:")
    print("   Acesse o Airflow UI e verifique se os DAGs SALESFORCE-CONSORCIO e")
    print("   SALESFORCE-SEGUROS estão aparecendo sem o sufixo -FALLBACK")

if __name__ == "__main__":
    main()
