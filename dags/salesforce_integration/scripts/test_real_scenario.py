#!/usr/bin/env python3
"""
Script para testar o cenário real do Airflow
"""

import sys
import os

# Adicionar ao path
sys.path.append('/opt/airflow/dags')
sys.path.append('/home/<USER>/projects/airflow-v1/dags')

def test_real_airflow_scenario():
    """Testa o cenário real como acontece no Airflow"""
    print("🎯 TESTE DO CENÁRIO REAL DO AIRFLOW")
    print("=" * 45)
    
    print("\n📋 SIMULANDO EXECUÇÃO DE TASK COMO NO AIRFLOW:")
    print("1. Task é chamada")
    print("2. Wrapper cria adapter")
    print("3. Wrapper aplica patch")
    print("4. Wrapper chama função original")
    print("5. Função original usa SalesforceClient")
    
    # Simular context do Airflow
    mock_context = {
        'dag': type('MockDag', (), {'dag_id': 'SALESFORCE-CONSORCIO'})(),
        'task': type('MockTask', (), {'task_id': 'extract_orbbits_origin'})()
    }
    
    print(f"\n🎬 EXECUTANDO: airflow_extract_orbbits_origin_individual_task")
    print(f"   DAG: {mock_context['dag'].dag_id}")
    print(f"   Task: {mock_context['task'].task_id}")
    
    try:
        # Importar e executar exatamente como o Airflow faz
        from salesforce_integration.airflow_tasks.airflow_task_wrappers import airflow_extract_orbbits_origin_individual_task
        
        print("\n1️⃣ Wrapper importado com sucesso")
        
        # Executar o wrapper (que vai aplicar o patch e chamar a função original)
        print("2️⃣ Executando wrapper...")
        
        # Não vamos executar de verdade, mas vamos simular o que o wrapper faz
        print("3️⃣ Simulando o que o wrapper faz:")
        
        # Passo 1: Criar adapter
        from salesforce_integration.src.utils.business_unit_adapter import create_business_unit_adapter
        adapter = create_business_unit_adapter(**mock_context)
        print(f"   ✅ Adapter criado para: {adapter.business_unit}")
        
        # Passo 2: Aplicar patch
        from salesforce_integration.src.utils.business_unit_adapter import patch_config_for_business_unit
        patch_config_for_business_unit(adapter)
        print(f"   ✅ Patch aplicado")
        
        # Passo 3: Verificar configuração ANTES de importar salesforce_client
        print("\n4️⃣ Verificando configuração APÓS patch:")
        import sys
        config_module = sys.modules.get('salesforce_integration.config.config')
        if config_module:
            tb_propostas = config_module.DATA_EXTENSIONS.get('tb_propostas', {})
            key_after_patch = tb_propostas.get('external_key', 'NÃO ENCONTRADO')
            print(f"   tb_propostas external_key: {key_after_patch}")
        
        # Passo 4: Agora importar e testar salesforce_client
        print("\n5️⃣ Importando SalesforceClient APÓS patch:")
        from salesforce_integration.src.clients.salesforce_client import SalesforceClient, get_current_config
        
        # Testar get_current_config
        current_config = get_current_config()
        client_data_extensions = current_config['DATA_EXTENSIONS']
        tb_propostas_client = client_data_extensions.get('tb_propostas', {})
        client_key = tb_propostas_client.get('external_key', 'NÃO ENCONTRADO')
        print(f"   get_current_config() retorna: {client_key}")
        
        # Testar criação do client
        print("\n6️⃣ Criando SalesforceClient:")
        client = SalesforceClient()
        print(f"   ✅ SalesforceClient criado")
        
        # Verificar se o client usaria a configuração correta
        print("\n7️⃣ Testando método que usa DATA_EXTENSIONS:")
        
        # Simular o que acontece quando o client tenta enviar dados
        print("   Simulando client.send_data_to_salesforce('tb_propostas', [])...")
        
        # Vamos verificar qual configuração seria usada
        test_config = get_current_config()
        test_data_extensions = test_config['DATA_EXTENSIONS']
        test_tb_propostas = test_data_extensions.get('tb_propostas', {})
        test_key = test_tb_propostas.get('external_key', 'NÃO ENCONTRADO')
        
        print(f"   Configuração que seria usada: {test_key}")
        
        # Verificar se é a chave correta
        expected_key = "6C9AFF4A-6D83-48BA-814C-ADFAC3B41F58"  # Chave do consórcio
        
        if test_key == expected_key:
            print(f"\n✅ SUCESSO! CORREÇÃO FUNCIONOU!")
            print(f"   O SalesforceClient agora usaria a configuração correta")
            print(f"   Chave esperada: {expected_key}")
            print(f"   Chave que seria usada: {test_key}")
            print(f"\n🎉 O erro 'CustomObjectNotFound' deve estar resolvido!")
            return True
        else:
            print(f"\n❌ PROBLEMA PERSISTE")
            print(f"   Chave esperada: {expected_key}")
            print(f"   Chave que seria usada: {test_key}")
            print(f"   O erro 'CustomObjectNotFound' continuará ocorrendo")
            return False
            
    except Exception as e:
        print(f"\n❌ Erro durante teste: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_real_airflow_scenario()
    
    if success:
        print(f"\n🚀 PRÓXIMOS PASSOS:")
        print(f"1. Limpar cache Python no Docker")
        print(f"2. Reiniciar serviços do Airflow")
        print(f"3. Executar a task novamente")
        print(f"4. Verificar se o erro CustomObjectNotFound desapareceu")
    else:
        print(f"\n🔧 INVESTIGAÇÃO ADICIONAL NECESSÁRIA")
        print(f"O problema pode estar em outro lugar do código")
    
    sys.exit(0 if success else 1)
