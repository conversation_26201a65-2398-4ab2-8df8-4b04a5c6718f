#!/usr/bin/env python3
"""
Script para debugar qual configuração está sendo usada
"""

import sys
import os

# Adicionar ao path
sys.path.append('/opt/airflow/dags')
sys.path.append('/home/<USER>/projects/airflow-v1/dags')

def debug_config():
    """Debug das configurações"""
    print("🔍 DEBUG DE CONFIGURAÇÕES")
    print("=" * 40)
    
    # 1. Verificar configuração global original
    print("\n1️⃣ CONFIGURAÇÃO GLOBAL ORIGINAL:")
    from salesforce_integration.config.config import DATA_EXTENSIONS
    
    tb_propostas_global = DATA_EXTENSIONS.get('tb_propostas', {})
    print(f"   tb_propostas external_key: {tb_propostas_global.get('external_key', 'NÃO ENCONTRADO')}")
    
    # 2. Verificar configuração da business unit
    print("\n2️⃣ CONFIGURAÇÃO BUSINESS UNIT (CONSÓRCIO):")
    from salesforce_integration.data.business_units.consorcio.config import DATA_EXTENSIONS_CONSORCIO
    
    tb_propostas_consorcio = DATA_EXTENSIONS_CONSORCIO.get('tb_propostas', {})
    print(f"   tb_propostas external_key: {tb_propostas_consorcio.get('external_key', 'NÃO ENCONTRADO')}")
    
    # 3. Testar criação do adapter
    print("\n3️⃣ TESTANDO BUSINESS UNIT ADAPTER:")
    from salesforce_integration.src.utils.business_unit_adapter import BusinessUnitAdapter
    
    adapter = BusinessUnitAdapter('consorcio')
    adapter_data_extensions = adapter.get_data_extensions()
    
    tb_propostas_adapter = adapter_data_extensions.get('tb_propostas', {})
    print(f"   tb_propostas external_key: {tb_propostas_adapter.get('external_key', 'NÃO ENCONTRADO')}")
    
    # 4. Testar patch
    print("\n4️⃣ TESTANDO PATCH DE CONFIGURAÇÃO:")
    from salesforce_integration.src.utils.business_unit_adapter import patch_config_for_business_unit
    
    print("   Aplicando patch...")
    patch_config_for_business_unit(adapter)
    
    # Verificar se a configuração global mudou
    from salesforce_integration.config.config import DATA_EXTENSIONS as DATA_EXTENSIONS_AFTER_PATCH
    
    tb_propostas_after_patch = DATA_EXTENSIONS_AFTER_PATCH.get('tb_propostas', {})
    print(f"   tb_propostas external_key APÓS PATCH: {tb_propostas_after_patch.get('external_key', 'NÃO ENCONTRADO')}")
    
    # 5. Verificar se o salesforce_client usa a configuração correta
    print("\n5️⃣ TESTANDO SALESFORCE CLIENT:")
    try:
        from salesforce_integration.src.clients.salesforce_client import SalesforceClient
        
        # Simular criação do client (sem conectar)
        client = SalesforceClient(dry_run=True)
        
        # Verificar qual configuração o client está usando
        client_data_extensions = getattr(client, 'data_extensions', None)
        if client_data_extensions:
            tb_propostas_client = client_data_extensions.get('tb_propostas', {})
            print(f"   tb_propostas external_key NO CLIENT: {tb_propostas_client.get('external_key', 'NÃO ENCONTRADO')}")
        else:
            print("   ⚠️ Client não tem data_extensions definido")
            
    except Exception as e:
        print(f"   ❌ Erro ao testar client: {e}")
    
    # 6. Resumo
    print("\n📊 RESUMO:")
    print(f"   Global Original: {tb_propostas_global.get('external_key', 'N/A')}")
    print(f"   Consórcio Config: {tb_propostas_consorcio.get('external_key', 'N/A')}")
    print(f"   Adapter: {tb_propostas_adapter.get('external_key', 'N/A')}")
    print(f"   Após Patch: {tb_propostas_after_patch.get('external_key', 'N/A')}")
    
    # Verificar se as chaves são diferentes
    global_key = tb_propostas_global.get('external_key')
    consorcio_key = tb_propostas_consorcio.get('external_key')
    
    if global_key != consorcio_key:
        print(f"\n⚠️ PROBLEMA IDENTIFICADO:")
        print(f"   A configuração global tem uma chave diferente da configuração do consórcio!")
        print(f"   Global: {global_key}")
        print(f"   Consórcio: {consorcio_key}")
        print(f"   O patch deve estar falhando ou não sendo aplicado.")
    else:
        print(f"\n✅ Configurações estão consistentes")

if __name__ == "__main__":
    debug_config()
