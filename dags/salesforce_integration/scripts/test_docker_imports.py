#!/usr/bin/env python3
"""
Script para testar imports no ambiente Docker do Airflow
"""

import sys
import os
import traceback
from pathlib import Path

def test_import(module_path, function_name=None):
    """Testa um import específico"""
    try:
        print(f"🧪 Testando: {module_path}")
        
        # Adicionar o diretório dags ao path se não estiver
        dags_path = '/opt/airflow/dags'
        if dags_path not in sys.path:
            sys.path.insert(0, dags_path)
            print(f"   ➕ Adicionado ao sys.path: {dags_path}")
        
        # Tentar importar o módulo
        module = __import__(module_path, fromlist=[function_name] if function_name else [''])
        
        if function_name:
            # Tentar acessar a função específica
            func = getattr(module, function_name)
            print(f"   ✅ Função {function_name} encontrada: {func}")
        else:
            print(f"   ✅ Módulo importado com sucesso: {module}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ Erro: {e}")
        print(f"   📍 Traceback:")
        traceback.print_exc()
        return False

def main():
    """Função principal de diagnóstico"""
    print("🔍 DIAGNÓSTICO DE IMPORTS NO DOCKER")
    print("=" * 50)
    
    # Informações do ambiente
    print(f"🐍 Python version: {sys.version}")
    print(f"📁 Current working directory: {os.getcwd()}")
    print(f"🛤️ Python path:")
    for i, path in enumerate(sys.path):
        print(f"   {i}: {path}")
    print()
    
    # Verificar se o diretório dags existe
    dags_path = '/opt/airflow/dags'
    if os.path.exists(dags_path):
        print(f"✅ Diretório dags encontrado: {dags_path}")
        
        # Listar conteúdo do diretório salesforce_integration
        sf_path = os.path.join(dags_path, 'salesforce_integration')
        if os.path.exists(sf_path):
            print(f"✅ Diretório salesforce_integration encontrado: {sf_path}")
            print(f"📂 Conteúdo:")
            for item in os.listdir(sf_path):
                item_path = os.path.join(sf_path, item)
                if os.path.isdir(item_path):
                    print(f"   📁 {item}/")
                else:
                    print(f"   📄 {item}")
        else:
            print(f"❌ Diretório salesforce_integration NÃO encontrado: {sf_path}")
    else:
        print(f"❌ Diretório dags NÃO encontrado: {dags_path}")
    
    print()
    
    # Testes de import específicos
    tests = [
        # Teste básico do pacote
        ('salesforce_integration', None),
        
        # Teste dos módulos principais
        ('salesforce_integration.airflow_tasks', None),
        ('salesforce_integration.airflow_tasks.airflow_task_wrappers', None),
        ('salesforce_integration.airflow_tasks.extraction_tasks', None),
        
        # Teste da função específica que está falhando
        ('salesforce_integration.airflow_tasks.airflow_task_wrappers', 'airflow_extract_orbbits_origin_individual_task'),
        ('salesforce_integration.airflow_tasks.extraction_tasks', 'airflow_extract_orbbits_origin_individual_task'),
        
        # Teste de outros módulos críticos
        ('salesforce_integration.src.extractors.data_extractors', 'DataExtractor'),
        ('salesforce_integration.config.config', 'ENVIRONMENT_CONFIG'),
    ]
    
    print("🧪 EXECUTANDO TESTES DE IMPORT")
    print("-" * 30)
    
    success_count = 0
    total_count = len(tests)
    
    for module_path, function_name in tests:
        if test_import(module_path, function_name):
            success_count += 1
        print()
    
    # Resumo
    print("📊 RESUMO DOS TESTES")
    print("-" * 20)
    print(f"✅ Sucessos: {success_count}/{total_count}")
    print(f"❌ Falhas: {total_count - success_count}/{total_count}")
    
    if success_count == total_count:
        print("🎉 TODOS OS IMPORTS FUNCIONANDO!")
    else:
        print("⚠️ ALGUNS IMPORTS FALHARAM - VERIFIQUE OS ERROS ACIMA")
    
    # Teste específico da função problemática
    print("\n🎯 TESTE ESPECÍFICO DA FUNÇÃO PROBLEMÁTICA")
    print("-" * 40)
    
    try:
        # Simular exatamente o que o Airflow está tentando fazer
        import importlib
        
        callable_path = "salesforce_integration.airflow_tasks.airflow_task_wrappers.airflow_extract_orbbits_origin_individual_task"
        module_path, func_name = callable_path.rsplit('.', 1)
        
        print(f"📍 Tentando importar: {module_path}")
        print(f"📍 Função: {func_name}")
        
        module = importlib.import_module(module_path)
        func = getattr(module, func_name)
        
        print(f"✅ SUCESSO! Função encontrada: {func}")
        print(f"📋 Tipo: {type(func)}")
        print(f"📋 Docstring: {func.__doc__}")
        
    except Exception as e:
        print(f"❌ FALHA: {e}")
        traceback.print_exc()

if __name__ == "__main__":
    main()
