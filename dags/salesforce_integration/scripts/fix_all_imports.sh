#!/bin/bash
# Script para aplicar todas as correções de imports no ambiente Docker

echo "🔧 APLICANDO TODAS AS CORREÇÕES DE IMPORTS"
echo "=========================================="

# Navegar para o diretório correto
cd /opt/airflow/dags/salesforce_integration 2>/dev/null || cd /home/<USER>/projects/airflow-v1/dags/salesforce_integration

echo "📁 Diretório atual: $(pwd)"

# 1. Corrigir imports do airflow_task_wrappers
echo ""
echo "🔄 1. Corrigindo imports do airflow_task_wrappers..."
python3 scripts/fix_airflow_imports.py

# 2. Limpar cache Python
echo ""
echo "🗑️ 2. Limpando cache Python..."
find . -name "*.pyc" -delete
find . -name "__pycache__" -type d -exec rm -rf {} + 2>/dev/null || true

echo "✅ Cache Python limpo"

# 3. Verificar se todos os __init__.py existem
echo ""
echo "📄 3. Verificando arquivos __init__.py..."

# Lista de diretórios que precisam de __init__.py
INIT_DIRS=(
    "."
    "config"
    "data"
    "tools"
    "scripts"
    "airflow_tasks"
    "core"
    "data/business_units"
    "data/business_units/consorcio"
    "data/business_units/seguros"
    "monitoring"
    "orchestrators"
    "reports"
    "src"
    "src/clients"
    "src/connections"
    "src/extractors"
    "src/transformers"
    "src/utils"
    "tests"
)

for dir in "${INIT_DIRS[@]}"; do
    if [ -d "$dir" ]; then
        if [ ! -f "$dir/__init__.py" ]; then
            touch "$dir/__init__.py"
            echo "✅ Criado: $dir/__init__.py"
        fi
    fi
done

# 4. Validar imports críticos
echo ""
echo "🧪 4. Validando imports críticos..."
python3 -c "
import sys
import os

# Adicionar ao path se necessário
current_dir = os.getcwd()
dags_dir = os.path.dirname(current_dir)
if dags_dir not in sys.path:
    sys.path.insert(0, dags_dir)

try:
    # Testar imports críticos
    from salesforce_integration.airflow_tasks.airflow_task_wrappers import airflow_extract_rdstation_leads_individual_task
    from salesforce_integration.src.utils.business_unit_adapter import BusinessUnitAdapter, patch_config_for_business_unit
    from salesforce_integration.config.config import ENVIRONMENT_CONFIG
    
    print('✅ Todos os imports críticos funcionando!')
    
except Exception as e:
    print(f'❌ Erro nos imports: {e}')
    import traceback
    traceback.print_exc()
    exit(1)
"

if [ $? -eq 0 ]; then
    echo "✅ Validação de imports bem-sucedida!"
else
    echo "❌ Falha na validação de imports!"
    exit 1
fi

# 5. Tentar reiniciar serviços do Airflow (se disponível)
echo ""
echo "🔄 5. Tentando reiniciar serviços do Airflow..."

if command -v docker-compose &> /dev/null; then
    echo "🐳 Docker Compose encontrado, reiniciando serviços..."
    docker-compose restart airflow-webserver airflow-scheduler 2>/dev/null || echo "⚠️ Não foi possível reiniciar via docker-compose"
else
    echo "⚠️ Docker Compose não encontrado"
fi

echo ""
echo "🎉 TODAS AS CORREÇÕES APLICADAS COM SUCESSO!"
echo "============================================"
echo ""
echo "📋 PRÓXIMOS PASSOS:"
echo "1. Aguarde alguns minutos para o Airflow recarregar"
echo "2. Verifique o Airflow UI para confirmar que os DAGs estão funcionando"
echo "3. Execute uma task para verificar se não há mais erros de import"
echo ""
echo "🎯 VERIFICAÇÕES ESPERADAS:"
echo "✅ DAGs aparecem sem sufixo -FALLBACK"
echo "✅ Tasks executam sem erro de ModuleNotFoundError"
echo "✅ Logs mostram criação bem-sucedida dos DAGs"
