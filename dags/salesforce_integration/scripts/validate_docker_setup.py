#!/usr/bin/env python3
"""
Script de validação para ambiente Docker do Airflow
Execute este script dentro do container para verificar se tudo está funcionando
"""

import sys
import os
import importlib
import traceback
from pathlib import Path

def check_environment():
    """Verifica o ambiente Docker"""
    print("🐳 VERIFICAÇÃO DO AMBIENTE DOCKER")
    print("=" * 40)
    
    print(f"🐍 Python: {sys.version}")
    print(f"📁 Working Directory: {os.getcwd()}")
    print(f"🔍 Docker Environment: {os.path.exists('/.dockerenv')}")
    
    # Verificar se estamos no diretório correto do Airflow
    expected_paths = ['/opt/airflow/dags', '/home/<USER>/dags']
    dags_path = None
    
    for path in expected_paths:
        if os.path.exists(path):
            dags_path = path
            break
    
    if dags_path:
        print(f"✅ Diretório dags encontrado: {dags_path}")
        
        # Verificar salesforce_integration
        sf_path = os.path.join(dags_path, 'salesforce_integration')
        if os.path.exists(sf_path):
            print(f"✅ Salesforce integration encontrado: {sf_path}")
        else:
            print(f"❌ Salesforce integration NÃO encontrado: {sf_path}")
            return False
    else:
        print(f"❌ Diretório dags não encontrado em: {expected_paths}")
        return False
    
    # Adicionar ao sys.path se necessário
    if dags_path not in sys.path:
        sys.path.insert(0, dags_path)
        print(f"➕ Adicionado ao sys.path: {dags_path}")
    
    return True

def test_critical_imports():
    """Testa imports críticos"""
    print("\n🧪 TESTE DE IMPORTS CRÍTICOS")
    print("=" * 30)
    
    critical_imports = [
        # Pacote principal
        'salesforce_integration',
        
        # Módulos principais
        'salesforce_integration.airflow_tasks',
        'salesforce_integration.airflow_tasks.airflow_task_wrappers',
        'salesforce_integration.airflow_tasks.extraction_tasks',
        
        # Configurações
        'salesforce_integration.config.config',
        'salesforce_integration.data.business_units.consorcio.config',
        'salesforce_integration.data.business_units.seguros.config',
        
        # Core
        'salesforce_integration.core.base_dag_factory',
        'salesforce_integration.src.extractors.data_extractors',
    ]
    
    success_count = 0
    
    for module_name in critical_imports:
        try:
            importlib.import_module(module_name)
            print(f"✅ {module_name}")
            success_count += 1
        except Exception as e:
            print(f"❌ {module_name}: {e}")
    
    print(f"\n📊 Resultado: {success_count}/{len(critical_imports)} imports bem-sucedidos")
    return success_count == len(critical_imports)

def test_specific_functions():
    """Testa funções específicas que estavam falhando"""
    print("\n🎯 TESTE DE FUNÇÕES ESPECÍFICAS")
    print("=" * 35)
    
    functions_to_test = [
        'salesforce_integration.airflow_tasks.airflow_task_wrappers.airflow_extract_orbbits_origin_individual_task',
        'salesforce_integration.airflow_tasks.airflow_task_wrappers.airflow_extract_newcon_clients_individual_task',
        'salesforce_integration.airflow_tasks.extraction_tasks.airflow_extract_orbbits_origin_individual_task',
        'salesforce_integration.airflow_tasks.extraction_tasks.airflow_extract_newcon_clients_individual_task',
    ]
    
    success_count = 0
    
    for func_path in functions_to_test:
        try:
            module_path, func_name = func_path.rsplit('.', 1)
            module = importlib.import_module(module_path)
            func = getattr(module, func_name)
            
            print(f"✅ {func_name}: {type(func)}")
            success_count += 1
            
        except Exception as e:
            print(f"❌ {func_name}: {e}")
    
    print(f"\n📊 Resultado: {success_count}/{len(functions_to_test)} funções encontradas")
    return success_count == len(functions_to_test)

def test_dag_creation():
    """Testa criação de DAGs"""
    print("\n🏗️ TESTE DE CRIAÇÃO DE DAGS")
    print("=" * 30)
    
    try:
        # Testar criação do DAG de consórcio
        from salesforce_integration.core.base_dag_factory import BaseDagFactory
        from salesforce_integration.data.business_units.consorcio.config import DAG_CONSORCIO_CONFIG
        
        factory = BaseDagFactory('consorcio', DAG_CONSORCIO_CONFIG)
        dag = factory.create_dag()
        
        print(f"✅ DAG Consórcio criado: {dag.dag_id}")
        print(f"   📊 Tasks: {len(dag.task_dict)}")
        
        # Testar criação do DAG de seguros
        from salesforce_integration.data.business_units.seguros.config import DAG_SEGUROS_CONFIG
        
        factory_seguros = BaseDagFactory('seguros', DAG_SEGUROS_CONFIG)
        dag_seguros = factory_seguros.create_dag()
        
        print(f"✅ DAG Seguros criado: {dag_seguros.dag_id}")
        print(f"   📊 Tasks: {len(dag_seguros.task_dict)}")
        
        return True
        
    except Exception as e:
        print(f"❌ Erro na criação de DAGs: {e}")
        traceback.print_exc()
        return False

def main():
    """Função principal de validação"""
    print("🔍 VALIDAÇÃO COMPLETA DO SETUP DOCKER")
    print("=" * 50)
    
    # Verificar ambiente
    if not check_environment():
        print("\n❌ FALHA NA VERIFICAÇÃO DO AMBIENTE")
        return False
    
    # Testar imports
    if not test_critical_imports():
        print("\n❌ FALHA NOS IMPORTS CRÍTICOS")
        return False
    
    # Testar funções específicas
    if not test_specific_functions():
        print("\n❌ FALHA NAS FUNÇÕES ESPECÍFICAS")
        return False
    
    # Testar criação de DAGs
    if not test_dag_creation():
        print("\n❌ FALHA NA CRIAÇÃO DE DAGS")
        return False
    
    print("\n🎉 VALIDAÇÃO COMPLETA BEM-SUCEDIDA!")
    print("=" * 40)
    print("✅ Ambiente Docker configurado corretamente")
    print("✅ Todos os imports funcionando")
    print("✅ Funções específicas encontradas")
    print("✅ DAGs podem ser criados")
    print()
    print("🚀 O Airflow deve conseguir executar os DAGs sem fallback!")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
