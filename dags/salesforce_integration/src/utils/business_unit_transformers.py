"""
Business Unit Transformers - Transformações específicas por unidade de negócio
<PERSON>pta as transformações existentes para processar apenas dados da unidade de negócio específica.
"""

import pandas as pd
import logging
from typing import Dict, Optional
from salesforce_integration.src.utils.business_unit_adapter import BusinessUnitAdapter

logger = logging.getLogger(__name__)

def filter_data_by_business_unit(extracted_data: Dict[str, pd.DataFrame], business_unit: str) -> Dict[str, pd.DataFrame]:
    """
    Filtra dados extraídos baseado na unidade de negócio.
    
    Args:
        extracted_data: Dados extraídos de todas as fontes
        business_unit: Unidade de negócio ('consorcio' ou 'seguros')
    
    Returns:
        Dados filtrados para a unidade de negócio específica
    """
    adapter = BusinessUnitAdapter(business_unit)
    filtered_data = adapter.filter_extraction_data(extracted_data)
    
    logger.info(f"Dados filtrados para {business_unit}: {list(filtered_data.keys())}")
    return filtered_data

def transform_produtos_by_business_unit(extracted_data: Dict[str, pd.DataFrame], business_unit: str) -> Dict[str, pd.DataFrame]:
    """
    Transforma produtos filtrando por unidade de negócio.
    
    - Consórcio: NewCon + Orbbits (sem Quiver)
    - Seguros: Quiver (sem NewCon + Orbbits)
    """
    from salesforce_integration.src.transformers.data_transformers import transform_products
    
    logger.info(f"Transformando produtos para unidade: {business_unit}")
    
    # Filtra dados por unidade de negócio
    filtered_data = filter_data_by_business_unit(extracted_data, business_unit)
    
    transformed_data = {}
    
    try:
        if business_unit == 'consorcio':
            # Consórcio: NewCon + Orbbits (sem Quiver)
            newcon_products = filtered_data.get('newcon_products', pd.DataFrame())
            orbbits_prices = filtered_data.get('orbbits_prices', pd.DataFrame())
            
            if not newcon_products.empty and not orbbits_prices.empty:
                transformed_data['tb_produtos'] = transform_products(
                    newcon_products, 
                    orbbits_prices, 
                    quiver_data=None  # Explicitamente sem Quiver
                )
                logger.info(f"✅ Produtos consórcio transformados: {len(transformed_data['tb_produtos'])} registros")
            else:
                logger.warning("⚠️ Dados insuficientes para transformação de produtos (consórcio)")
                
        elif business_unit == 'seguros':
            # Seguros: Apenas Quiver - usar preparação específica
            quiver_products = filtered_data.get('quiver_products', pd.DataFrame())
            
            if not quiver_products.empty:
                # Para seguros, usar função específica de preparação do Quiver
                from salesforce_integration.src.transformers.data_transformers import _prepare_quiver_for_products
                
                transformed_data['tb_produtos'] = _prepare_quiver_for_products(quiver_products)
                logger.info(f"✅ Produtos seguros transformados: {len(transformed_data['tb_produtos'])} registros")
            else:
                logger.warning("⚠️ Dados insuficientes para transformação de produtos (seguros)")
        
        else:
            raise ValueError(f"Unidade de negócio não suportada: {business_unit}")
            
    except Exception as e:
        logger.error(f"❌ Erro na transformação de produtos para {business_unit}: {e}")
        raise
    
    return transformed_data

def transform_clientes_by_business_unit(extracted_data: Dict[str, pd.DataFrame], business_unit: str) -> Dict[str, pd.DataFrame]:
    """
    Transforma clientes filtrando por unidade de negócio.
    
    - Consórcio: NewCon (sem Quiver)
    - Seguros: Quiver (sem NewCon)
    """
    from salesforce_integration.src.transformers.data_transformers import transform_clients
    
    logger.info(f"Transformando clientes para unidade: {business_unit}")
    
    # Filtra dados por unidade de negócio
    filtered_data = filter_data_by_business_unit(extracted_data, business_unit)
    
    transformed_data = {}
    
    try:
        if business_unit == 'consorcio':
            # Consórcio: NewCon + Orbbits (sem Quiver)
            newcon_clients = filtered_data.get('newcon_clients', pd.DataFrame())
            orbbits_origin = filtered_data.get('orbbits_origin', pd.DataFrame())
            
            if not newcon_clients.empty:
                transformed_data['tb_clientes'] = transform_clients(
                    newcon_clients,
                    orbbits_origin,
                    quiver_data=None  # Explicitamente sem Quiver
                )
                logger.info(f"✅ Clientes consórcio transformados: {len(transformed_data['tb_clientes'])} registros")
            else:
                logger.warning("⚠️ Dados insuficientes para transformação de clientes (consórcio)")
                
        elif business_unit == 'seguros':
            # Seguros: Apenas Quiver - usar preparação específica
            quiver_clients = filtered_data.get('quiver_clients', pd.DataFrame())
            
            if not quiver_clients.empty:
                # Para seguros, usar função específica de preparação do Quiver
                from salesforce_integration.src.transformers.data_transformers import _prepare_quiver_for_clients
                
                transformed_data['tb_clientes'] = _prepare_quiver_for_clients(quiver_clients)
                logger.info(f"✅ Clientes seguros transformados: {len(transformed_data['tb_clientes'])} registros")
            else:
                logger.warning("⚠️ Dados insuficientes para transformação de clientes (seguros)")
        
        else:
            raise ValueError(f"Unidade de negócio não suportada: {business_unit}")
            
    except Exception as e:
        logger.error(f"❌ Erro na transformação de clientes para {business_unit}: {e}")
        raise
    
    return transformed_data

def transform_leads_by_business_unit(extracted_data: Dict[str, pd.DataFrame], business_unit: str) -> Dict[str, pd.DataFrame]:
    """
    Transforma leads filtrando por unidade de negócio.
    
    - Consórcio: NewCon + RD Station (sem Quiver)
    - Seguros: Quiver (sem NewCon + RD Station)
    """
    from salesforce_integration.src.transformers.data_transformers import transform_leads
    
    logger.info(f"Transformando leads para unidade: {business_unit}")
    
    # Filtra dados por unidade de negócio
    filtered_data = filter_data_by_business_unit(extracted_data, business_unit)
    
    transformed_data = {}
    
    try:
        if business_unit == 'consorcio':
            # Consórcio: NewCon + RD Station + Orbbits (sem Quiver)
            newcon_leads = filtered_data.get('newcon_leads', pd.DataFrame())
            rdstation_leads = filtered_data.get('rdstation_leads', pd.DataFrame())
            orbbits_origin = filtered_data.get('orbbits_origin', pd.DataFrame())
            
            if not newcon_leads.empty or not rdstation_leads.empty:
                transformed_data['tb_leads'] = transform_leads(
                    newcon_leads,
                    rdstation_leads,
                    orbbits_origin,
                    quiver_data=None  # Explicitamente sem Quiver
                )
                logger.info(f"✅ Leads consórcio transformados: {len(transformed_data['tb_leads'])} registros")
            else:
                logger.warning("⚠️ Dados insuficientes para transformação de leads (consórcio)")
                
        elif business_unit == 'seguros':
            # Seguros: Apenas Quiver - usar preparação específica
            quiver_leads = filtered_data.get('quiver_leads', pd.DataFrame())
            
            if not quiver_leads.empty:
                # Para seguros, usar função específica de preparação do Quiver
                from salesforce_integration.src.transformers.data_transformers import _prepare_quiver_for_leads
                
                transformed_data['tb_leads'] = _prepare_quiver_for_leads(quiver_leads)
                logger.info(f"✅ Leads seguros transformados: {len(transformed_data['tb_leads'])} registros")
            else:
                logger.warning("⚠️ Dados insuficientes para transformação de leads (seguros)")
        
        else:
            raise ValueError(f"Unidade de negócio não suportada: {business_unit}")
            
    except Exception as e:
        logger.error(f"❌ Erro na transformação de leads para {business_unit}: {e}")
        raise
    
    return transformed_data

def transform_propostas_by_business_unit(extracted_data: Dict[str, pd.DataFrame], business_unit: str) -> Dict[str, pd.DataFrame]:
    """
    Transforma propostas filtrando por unidade de negócio.
    
    - Consórcio: NewCon + Orbbits (sem Quiver)
    - Seguros: Quiver (sem NewCon + Orbbits)
    """
    from salesforce_integration.src.transformers.data_transformers import transform_proposals
    
    logger.info(f"Transformando propostas para unidade: {business_unit}")
    
    # Filtra dados por unidade de negócio
    filtered_data = filter_data_by_business_unit(extracted_data, business_unit)
    
    transformed_data = {}
    
    try:
        if business_unit == 'consorcio':
            # Consórcio: NewCon + Orbbits (sem Quiver)
            newcon_proposals = filtered_data.get('newcon_proposals', pd.DataFrame())
            orbbits_payments = filtered_data.get('orbbits_payments', pd.DataFrame())
            orbbits_sales = filtered_data.get('orbbits_sales', pd.DataFrame())
            orbbits_proposals = filtered_data.get('orbbits_proposals', pd.DataFrame())
            
            if not newcon_proposals.empty:
                transformed_data['tb_propostas'] = transform_proposals(
                    newcon_proposals,
                    orbbits_payments,
                    orbbits_sales,
                    orbbits_proposals,
                    quiver_data=None  # Explicitamente sem Quiver
                )
                logger.info(f"✅ Propostas consórcio transformadas: {len(transformed_data['tb_propostas'])} registros")
            else:
                logger.warning("⚠️ Dados insuficientes para transformação de propostas (consórcio)")
                
        elif business_unit == 'seguros':
            # Seguros: Apenas Quiver - usar preparação específica
            quiver_proposals = filtered_data.get('quiver_proposals', pd.DataFrame())
            
            if not quiver_proposals.empty:
                # Para seguros, usar função específica de preparação do Quiver
                from salesforce_integration.src.transformers.data_transformers import _prepare_quiver_for_proposals
                
                transformed_data['tb_propostas'] = _prepare_quiver_for_proposals(quiver_proposals)
                logger.info(f"✅ Propostas seguros transformadas: {len(transformed_data['tb_propostas'])} registros")
            else:
                logger.warning("⚠️ Dados insuficientes para transformação de propostas (seguros)")
        
        else:
            raise ValueError(f"Unidade de negócio não suportada: {business_unit}")
            
    except Exception as e:
        logger.error(f"❌ Erro na transformação de propostas para {business_unit}: {e}")
        raise
    
    return transformed_data