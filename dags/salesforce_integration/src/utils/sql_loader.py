"""
ETL Consolidado - Carregador de Consultas SQL
Módulo centralizado para carregar e gerenciar consultas SQL de diferentes fontes.
"""

import os
import logging
from typing import Dict, Optional, List
from pathlib import Path

logger = logging.getLogger(__name__)

class SQLLoader:
    """
    Carregador centralizado de consultas SQL.
    
    Organiza consultas por fonte (newcon, rdstation, orbbits, quiver) e
    fornece interface unificada para carregamento com suporte a:
    - Detecção automática de encoding
    - Cache de consultas
    - Validação de arquivos
    - Substituição de parâmetros
    """
    
    def __init__(self, base_path: Optional[str] = None):
        """
        Inicializa o carregador SQL.
        
        Args:
            base_path: Caminho base para as consultas. Se None, usa o diretório do módulo.
        """
        if base_path is None:
            base_path = os.path.join(os.path.dirname(__file__), '../../data/queries')
        
        self.base_path = Path(base_path)
        self.cache = {}  # Cache de consultas carregadas
        self.encodings = ['utf-8', 'latin-1', 'cp1252', 'iso-8859-1']
        
        logger.info(f"SQLLoader inicializado com base_path: {self.base_path}")
    
    def _detect_encoding(self, file_path: Path) -> str:
        """
        Detecta o encoding de um arquivo SQL.
        
        Args:
            file_path: Caminho para o arquivo
            
        Returns:
            Encoding detectado
            
        Raises:
            UnicodeDecodeError: Se nenhum encoding funcionar
        """
        for encoding in self.encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    f.read()
                return encoding
            except UnicodeDecodeError:
                continue
        
        raise UnicodeDecodeError(f"Não foi possível detectar encoding para {file_path}")
    
    def _load_sql_file(self, file_path: Path) -> str:
        """
        Carrega conteúdo de um arquivo SQL.
        
        Args:
            file_path: Caminho para o arquivo
            
        Returns:
            Conteúdo do arquivo SQL
        """
        if not file_path.exists():
            raise FileNotFoundError(f"Arquivo SQL não encontrado: {file_path}")
        
        # Detectar encoding
        encoding = self._detect_encoding(file_path)
        
        # Carregar conteúdo
        with open(file_path, 'r', encoding=encoding) as f:
            content = f.read()
        
        logger.debug(f"Arquivo {file_path.name} carregado com encoding {encoding}")
        return content.strip()
    
    def get_query(self, source: str, table: str, use_cache: bool = True) -> str:
        """
        Carrega uma consulta SQL específica.
        
        Args:
            source: Fonte dos dados (newcon, rdstation, orbbits, quiver)
            table: Tabela/tipo de dados (clientes, leads, produtos, propostas, etc.)
            use_cache: Se deve usar cache de consultas
            
        Returns:
            Consulta SQL como string
            
        Example:
            >>> loader = SQLLoader()
            >>> query = loader.get_query('newcon', 'clientes')
            >>> query = loader.get_query('quiver', 'leads')
        """
        cache_key = f"{source}_{table}"
        
        # Verificar cache
        if use_cache and cache_key in self.cache:
            logger.debug(f"Consulta {cache_key} carregada do cache")
            return self.cache[cache_key]
        
        # Construir caminho do arquivo
        file_name = f"{source}_{table}.sql"
        file_path = self.base_path / source / file_name
        
        # Carregar arquivo
        try:
            query = self._load_sql_file(file_path)
            
            # Armazenar no cache
            if use_cache:
                self.cache[cache_key] = query
            
            logger.info(f"Consulta {cache_key} carregada: {len(query)} caracteres")
            return query
            
        except Exception as e:
            logger.error(f"Erro ao carregar consulta {cache_key}: {e}")
            raise
    
    def get_all_queries(self, source: str, use_cache: bool = True) -> Dict[str, str]:
        """
        Carrega todas as consultas de uma fonte.
        
        Args:
            source: Fonte dos dados (newcon, rdstation, orbbits, quiver)
            use_cache: Se deve usar cache de consultas
            
        Returns:
            Dicionário com todas as consultas da fonte
            
        Example:
            >>> loader = SQLLoader()
            >>> queries = loader.get_all_queries('newcon')
            >>> # {'clientes': 'SELECT ...', 'leads': 'SELECT ...', ...}
        """
        source_path = self.base_path / source
        
        if not source_path.exists():
            raise FileNotFoundError(f"Diretório da fonte não encontrado: {source_path}")
        
        queries = {}
        sql_files = list(source_path.glob("*.sql"))
        
        for sql_file in sql_files:
            # Extrair nome da tabela do arquivo (ex: newcon_clientes.sql -> clientes)
            file_name = sql_file.stem
            if file_name.startswith(f"{source}_"):
                table_name = file_name[len(source) + 1:]
            else:
                table_name = file_name
            
            try:
                queries[table_name] = self.get_query(source, table_name, use_cache)
            except Exception as e:
                logger.warning(f"Erro ao carregar {sql_file.name}: {e}")
                continue
        
        logger.info(f"Carregadas {len(queries)} consultas da fonte {source}")
        return queries
    
    def list_available_queries(self) -> Dict[str, List[str]]:
        """
        Lista todas as consultas disponíveis por fonte.
        
        Returns:
            Dicionário com fontes e suas consultas disponíveis
        """
        available = {}
        
        if not self.base_path.exists():
            logger.warning(f"Diretório base não existe: {self.base_path}")
            return available
        
        for source_dir in self.base_path.iterdir():
            if source_dir.is_dir():
                source_name = source_dir.name
                sql_files = list(source_dir.glob("*.sql"))
                
                table_names = []
                for sql_file in sql_files:
                    file_name = sql_file.stem
                    if file_name.startswith(f"{source_name}_"):
                        table_name = file_name[len(source_name) + 1:]
                    else:
                        table_name = file_name
                    table_names.append(table_name)
                
                available[source_name] = sorted(table_names)
        
        return available
    
    def add_test_sample_limit(self, query: str, test_sample: Optional[int] = None) -> str:
        """
        Adiciona LIMIT/TOP para amostras de teste.
        
        Args:
            query: Consulta SQL original
            test_sample: Número de registros para amostra
            
        Returns:
            Consulta modificada com LIMIT/TOP
        """
        if not test_sample or test_sample <= 0:
            return query
        
        query_lower = query.lower().strip()
        
        # Para SQL Server (NewCon, Quiver) - usar TOP
        if any(keyword in query_lower for keyword in ['select', 'with']):
            if 'top ' not in query_lower and 'limit ' not in query_lower:
                # Adicionar TOP após SELECT
                query = query.replace('SELECT', f'SELECT TOP {test_sample}', 1)
                query = query.replace('select', f'select TOP {test_sample}', 1)
        
        # Para MySQL (Orbbits) - usar LIMIT
        elif 'mysql' in query_lower or 'limit' not in query_lower:
            if not query.rstrip().endswith(';'):
                query += f' LIMIT {test_sample}'
            else:
                query = query.rstrip()[:-1] + f' LIMIT {test_sample};'
        
        return query
    
    def clear_cache(self):
        """Limpa o cache de consultas."""
        self.cache.clear()
        logger.info("Cache de consultas limpo")

# Instância global para uso direto
sql_loader = SQLLoader()

# Funções de conveniência
def get_query(source: str, table: str) -> str:
    """Função de conveniência para carregar uma consulta."""
    return sql_loader.get_query(source, table)

def get_all_queries(source: str) -> Dict[str, str]:
    """Função de conveniência para carregar todas as consultas de uma fonte."""
    return sql_loader.get_all_queries(source)

def list_queries() -> Dict[str, List[str]]:
    """Função de conveniência para listar consultas disponíveis."""
    return sql_loader.list_available_queries()

if __name__ == "__main__":
    # Teste do módulo
    print("=== TESTE DO SQL LOADER ===")
    
    loader = SQLLoader()
    
    # Listar consultas disponíveis
    print("\n1. Consultas disponíveis:")
    available = loader.list_available_queries()
    for source, tables in available.items():
        print(f"  {source}: {', '.join(tables)}")
    
    # Testar carregamento
    print("\n2. Teste de carregamento:")
    try:
        if 'quiver' in available and 'clientes' in available['quiver']:
            query = loader.get_query('quiver', 'clientes')
            print(f"  ✅ Quiver clientes: {len(query)} caracteres")
        else:
            print("  ⚠️ Consulta quiver/clientes não encontrada")
    except Exception as e:
        print(f"  ❌ Erro: {e}")
