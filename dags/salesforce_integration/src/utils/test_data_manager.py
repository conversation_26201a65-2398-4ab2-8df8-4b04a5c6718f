#!/usr/bin/env python3
"""
ETL Consolidado - Gerenciador de Dados de Teste
Sistema para identificar, marcar e gerenciar dados de teste de forma segura.

RESPONSABILIDADES:
- Marcar explicitamente dados de teste
- Prevenir confusão com dados de produção
- Validar ambientes antes de execução
- Gerar relatórios de segurança de dados
"""

import os
import sys
import json
import uuid
import logging
import pandas as pd
from datetime import datetime
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
from enum import Enum

# =============================================================================
# CONFIGURAÇÕES DE SEGURANÇA
# =============================================================================

class DataEnvironment(Enum):
    """Ambientes de dados suportados"""
    PRODUCTION = "production"
    STAGING = "staging"
    TESTING = "testing"
    DEVELOPMENT = "development"

class DataType(Enum):
    """Tipos de dados"""
    REAL = "real"
    TEST = "test"
    MOCK = "mock"
    SAMPLE = "sample"

@dataclass
class TestDataMarker:
    """Marcador para dados de teste"""
    data_id: str
    data_type: DataType
    environment: DataEnvironment
    created_at: datetime
    description: str
    source_module: str
    is_safe_for_production: bool = False
    expiry_date: Optional[datetime] = None
    metadata: Dict[str, Any] = None

    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}

# =============================================================================
# GERENCIADOR DE DADOS DE TESTE
# =============================================================================

class TestDataManager:
    """Gerenciador central de dados de teste"""
    
    def __init__(self, environment: DataEnvironment = None):
        self.logger = logging.getLogger(__name__)
        self.environment = environment or self._detect_environment()
        self.test_data_registry = {}
        self.safety_checks_enabled = True
        
        # Prefixos obrigatórios para dados de teste
        self.TEST_PREFIXES = {
            'email': ['test_', 'exemplo_', 'demo_', 'fake_'],
            'name': ['TEST_', 'EXEMPLO_', 'DEMO_', 'FAKE_'],
            'cpf': ['00000000000', '11111111111', '22222222222'],  # CPFs claramente inválidos
            'phone': ['11999999999', '21888888888', '31777777777'],  # Telefones de teste
            'company': ['EMPRESA_TESTE', 'TEST_COMPANY', 'DEMO_CORP']
        }
        
        # Domínios seguros para emails de teste
        self.SAFE_TEST_DOMAINS = [
            'example.com', 'test.com', 'demo.com', 'fake.com',
            'localhost', 'testing.local', 'exemplo.com.br'
        ]
    
    def _detect_environment(self) -> DataEnvironment:
        """Detecta ambiente atual baseado em variáveis"""
        env_var = os.getenv('ETL_ENVIRONMENT', '').lower()
        
        if env_var in ['prod', 'production']:
            return DataEnvironment.PRODUCTION
        elif env_var in ['staging', 'stage']:
            return DataEnvironment.STAGING
        elif env_var in ['test', 'testing']:
            return DataEnvironment.TESTING
        else:
            return DataEnvironment.DEVELOPMENT
    
    def create_test_data_marker(self, 
                               data_type: DataType,
                               description: str,
                               source_module: str,
                               is_safe_for_production: bool = False,
                               expiry_hours: int = None,
                               metadata: Dict[str, Any] = None) -> TestDataMarker:
        """Cria marcador para dados de teste"""
        
        data_id = f"TEST_{uuid.uuid4().hex[:8].upper()}"
        
        expiry_date = None
        if expiry_hours:
            from datetime import timedelta
            expiry_date = datetime.now() + timedelta(hours=expiry_hours)
        
        marker = TestDataMarker(
            data_id=data_id,
            data_type=data_type,
            environment=self.environment,
            created_at=datetime.now(),
            description=description,
            source_module=source_module,
            is_safe_for_production=is_safe_for_production,
            expiry_date=expiry_date,
            metadata=metadata or {}
        )
        
        # Registra o marcador
        self.test_data_registry[data_id] = marker
        
        self.logger.info(f"🏷️  Dados de teste marcados: {data_id} - {description}")
        
        return marker
    
    def mark_dataframe_as_test(self, 
                              df: pd.DataFrame, 
                              description: str,
                              source_module: str,
                              data_type: DataType = DataType.TEST) -> pd.DataFrame:
        """Marca DataFrame como dados de teste"""
        
        if df.empty:
            return df
        
        # Cria marcador
        marker = self.create_test_data_marker(
            data_type=data_type,
            description=description,
            source_module=source_module,
            metadata={
                'original_shape': df.shape,
                'columns': list(df.columns),
                'sample_data': df.head(2).to_dict() if len(df) > 0 else {}
            }
        )
        
        # Adiciona colunas de identificação de teste
        df_marked = df.copy()
        df_marked['__TEST_DATA_ID__'] = marker.data_id
        df_marked['__TEST_DATA_TYPE__'] = data_type.value
        df_marked['__TEST_ENVIRONMENT__'] = self.environment.value
        df_marked['__TEST_CREATED_AT__'] = marker.created_at.isoformat()
        df_marked['__TEST_DESCRIPTION__'] = description
        df_marked['__IS_TEST_DATA__'] = True
        
        return df_marked
    
    def validate_test_data_safety(self, df: pd.DataFrame) -> Dict[str, Any]:
        """Valida se dados de teste são seguros"""
        
        safety_report = {
            'is_safe': True,
            'issues': [],
            'warnings': [],
            'recommendations': []
        }
        
        # Verifica se dados estão marcados como teste
        if '__IS_TEST_DATA__' not in df.columns:
            safety_report['is_safe'] = False
            safety_report['issues'].append("Dados não estão marcados como teste")
        
        # Verifica emails de teste
        if 'email' in df.columns or 'e_mail' in df.columns:
            email_col = 'email' if 'email' in df.columns else 'e_mail'
            unsafe_emails = []
            
            for email in df[email_col].dropna():
                if not self._is_safe_test_email(str(email)):
                    unsafe_emails.append(email)
            
            if unsafe_emails:
                safety_report['is_safe'] = False
                safety_report['issues'].append(f"Emails não seguros encontrados: {unsafe_emails[:5]}")
        
        # Verifica CPFs de teste
        cpf_columns = ['cpf', 'cnpjcpf', 'CNPJCPF']
        for col in cpf_columns:
            if col in df.columns:
                unsafe_cpfs = []
                for cpf in df[col].dropna():
                    if not self._is_safe_test_cpf(str(cpf)):
                        unsafe_cpfs.append(cpf)
                
                if unsafe_cpfs:
                    safety_report['warnings'].append(f"CPFs podem não ser de teste: {unsafe_cpfs[:3]}")
        
        # Verifica telefones de teste
        phone_columns = ['telefone', 'celular', 'Celular', 'phone']
        for col in phone_columns:
            if col in df.columns:
                unsafe_phones = []
                for phone in df[col].dropna():
                    if not self._is_safe_test_phone(str(phone)):
                        unsafe_phones.append(phone)
                
                if unsafe_phones:
                    safety_report['warnings'].append(f"Telefones podem não ser de teste: {unsafe_phones[:3]}")
        
        return safety_report
    
    def _is_safe_test_email(self, email: str) -> bool:
        """Verifica se email é seguro para teste"""
        email = email.lower().strip()
        
        # Verifica prefixos de teste
        for prefix in self.TEST_PREFIXES['email']:
            if email.startswith(prefix):
                return True
        
        # Verifica domínios seguros
        for domain in self.SAFE_TEST_DOMAINS:
            if email.endswith(f'@{domain}'):
                return True
        
        return False
    
    def _is_safe_test_cpf(self, cpf: str) -> bool:
        """Verifica se CPF é seguro para teste"""
        cpf_clean = ''.join(filter(str.isdigit, cpf))
        
        # CPFs claramente de teste
        return cpf_clean in self.TEST_PREFIXES['cpf'] or len(set(cpf_clean)) <= 2
    
    def _is_safe_test_phone(self, phone: str) -> bool:
        """Verifica se telefone é seguro para teste"""
        phone_clean = ''.join(filter(str.isdigit, phone))
        
        return phone_clean in self.TEST_PREFIXES['phone']
    
    def generate_safe_test_data(self, table_name: str, num_records: int = 10) -> pd.DataFrame:
        """Gera dados de teste seguros"""
        
        test_data_generators = {
            'tb_clientes': self._generate_test_clients,
            'tb_leads': self._generate_test_leads,
            'tb_produtos': self._generate_test_products,
            'tb_propostas': self._generate_test_proposals
        }
        
        if table_name not in test_data_generators:
            raise ValueError(f"Gerador não disponível para tabela: {table_name}")
        
        # Gera dados
        df = test_data_generators[table_name](num_records)
        
        # Marca como dados de teste
        df_marked = self.mark_dataframe_as_test(
            df=df,
            description=f"Dados de teste gerados para {table_name}",
            source_module="test_data_manager",
            data_type=DataType.TEST
        )
        
        return df_marked
    
    def _generate_test_clients(self, num_records: int) -> pd.DataFrame:
        """Gera clientes de teste seguros"""
        data = []
        
        for i in range(num_records):
            data.append({
                'id_documento': f'TEST_DOC_{i:03d}',
                'CNPJCPF': '00000000000',  # CPF claramente de teste
                'e_mail': f'test_client_{i:03d}@example.com',
                'PrimeiroNome': f'TEST_USER_{i:03d}',
                'NomeCompleto': f'TEST USER {i:03d} EXEMPLO',
                'Celular': '11999999999',  # Telefone de teste
                'dt_nascimento': '1990-01-01',
                'dt_cadastro': datetime.now().strftime('%Y-%m-%d'),
                'Cidade': 'CIDADE_TESTE',
                'Estado': 'TS',  # Estado fictício
                'Locale': 'br',
                'Genero': 'M' if i % 2 == 0 else 'F',
                'PontoVenda': f'LOJA_TESTE_{i % 3 + 1}',
                'Renda': 5000.0,
                'politicamente_exposto': 0,
                'end_point': 'Clientes'
            })
        
        return pd.DataFrame(data)
    
    def _generate_test_leads(self, num_records: int) -> pd.DataFrame:
        """Gera leads de teste seguros"""
        data = []
        
        for i in range(num_records):
            data.append({
                'CNPJCPF': '11111111111',  # CPF claramente de teste
                'Email': f'test_lead_{i:03d}@example.com',
                'Dt_cadastro': datetime.now().strftime('%Y-%m-%d'),
                'Dt_simulacao': datetime.now().strftime('%Y-%m-%d'),
                'Celular': '21888888888',  # Telefone de teste
                'PrimeiroNome': f'LEAD_TEST_{i:03d}',
                'NomeCompleto': f'LEAD TEST {i:03d} EXEMPLO',
                'Cidade': 'CIDADE_TESTE',
                'Estado': 'TS',
                'TipoEmpresa': 'TESTE',
                'TipoBem': 'AUTOMOVEL',
                'TipoSimulacao': 'NORMAL',
                'ValorSimulacao': 50000.0,
                'end_point': 'Leads'
            })
        
        return pd.DataFrame(data)
    
    def _generate_test_products(self, num_records: int) -> pd.DataFrame:
        """Gera produtos de teste seguros"""
        data = []
        
        for i in range(num_records):
            data.append({
                'id_produto': f'TEST_PROD_{i:03d}',
                'grupo': f'GRUPO_TESTE_{i % 3 + 1}',
                'descricao': f'PRODUTO TESTE {i:03d}',
                'tipo_bem': 'AUTOMOVEL',
                'categoria': 'CONSORCIO',
                'valor_credito': 50000.0 + (i * 1000),
                'qtd_parcelas': 60,
                'taxa_admin': 15.0,
                'dt_atualizao': datetime.now().strftime('%Y-%m-%d'),
                'end_point': 'Produtos'
            })
        
        return pd.DataFrame(data)
    
    def _generate_test_proposals(self, num_records: int) -> pd.DataFrame:
        """Gera propostas de teste seguras"""
        data = []
        
        for i in range(num_records):
            data.append({
                'idproposta': f'TEST_PROP_{i:03d}',
                'CNPJCPF': '22222222222',  # CPF claramente de teste
                'email': f'test_proposal_{i:03d}@example.com',
                'Celular': '31777777777',  # Telefone de teste
                'dt_proposta': datetime.now().strftime('%Y-%m-%d'),
                'Status_proposta': 'TESTE',
                'Valor': 1000.0 + (i * 100),
                'id_produto': f'TEST_PROD_{i % 5}',
                'end_point': 'Propostas'
            })
        
        return pd.DataFrame(data)
    
    def validate_production_safety(self) -> Dict[str, Any]:
        """Valida segurança para ambiente de produção"""
        
        validation_report = {
            'is_safe_for_production': True,
            'environment': self.environment.value,
            'active_test_data': len(self.test_data_registry),
            'issues': [],
            'warnings': [],
            'recommendations': []
        }
        
        # Verifica ambiente
        if self.environment == DataEnvironment.PRODUCTION:
            if self.test_data_registry:
                validation_report['is_safe_for_production'] = False
                validation_report['issues'].append(
                    f"Dados de teste ativos em ambiente de produção: {len(self.test_data_registry)}"
                )
        
        # Verifica dados expirados
        expired_data = []
        for data_id, marker in self.test_data_registry.items():
            if marker.expiry_date and datetime.now() > marker.expiry_date:
                expired_data.append(data_id)
        
        if expired_data:
            validation_report['warnings'].append(f"Dados de teste expirados: {expired_data}")
        
        # Recomendações
        if self.environment == DataEnvironment.PRODUCTION:
            validation_report['recommendations'].append(
                "Execute limpeza de dados de teste antes de produção"
            )
        
        return validation_report
    
    def cleanup_test_data(self, force: bool = False) -> Dict[str, Any]:
        """Remove dados de teste do registro"""
        
        cleanup_report = {
            'cleaned_count': 0,
            'skipped_count': 0,
            'errors': []
        }
        
        if self.environment == DataEnvironment.PRODUCTION and not force:
            cleanup_report['errors'].append(
                "Limpeza em produção requer force=True"
            )
            return cleanup_report
        
        # Remove dados expirados ou não seguros para produção
        to_remove = []
        for data_id, marker in self.test_data_registry.items():
            should_remove = (
                force or 
                (marker.expiry_date and datetime.now() > marker.expiry_date) or
                (self.environment == DataEnvironment.PRODUCTION and not marker.is_safe_for_production)
            )
            
            if should_remove:
                to_remove.append(data_id)
            else:
                cleanup_report['skipped_count'] += 1
        
        # Remove dados
        for data_id in to_remove:
            del self.test_data_registry[data_id]
            cleanup_report['cleaned_count'] += 1
            self.logger.info(f"🧹 Dados de teste removidos: {data_id}")
        
        return cleanup_report
    
    def generate_safety_report(self) -> str:
        """Gera relatório de segurança de dados"""
        
        report_lines = [
            "=" * 60,
            "RELATÓRIO DE SEGURANÇA DE DADOS DE TESTE",
            "=" * 60,
            f"Ambiente: {self.environment.value.upper()}",
            f"Data/Hora: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"Dados de teste ativos: {len(self.test_data_registry)}",
            ""
        ]
        
        # Validação de produção
        prod_validation = self.validate_production_safety()
        status = "✅ SEGURO" if prod_validation['is_safe_for_production'] else "⚠️  ATENÇÃO"
        report_lines.append(f"Status de Produção: {status}")
        
        if prod_validation['issues']:
            report_lines.append("\n🔴 PROBLEMAS CRÍTICOS:")
            for issue in prod_validation['issues']:
                report_lines.append(f"  - {issue}")
        
        if prod_validation['warnings']:
            report_lines.append("\n🟡 AVISOS:")
            for warning in prod_validation['warnings']:
                report_lines.append(f"  - {warning}")
        
        if prod_validation['recommendations']:
            report_lines.append("\n💡 RECOMENDAÇÕES:")
            for rec in prod_validation['recommendations']:
                report_lines.append(f"  - {rec}")
        
        # Dados ativos
        if self.test_data_registry:
            report_lines.append("\n📋 DADOS DE TESTE ATIVOS:")
            for data_id, marker in self.test_data_registry.items():
                status_icon = "✅" if marker.is_safe_for_production else "⚠️"
                expiry_info = f" (expira: {marker.expiry_date})" if marker.expiry_date else ""
                report_lines.append(f"  {status_icon} {data_id}: {marker.description}{expiry_info}")
        
        report_lines.extend([
            "",
            "=" * 60,
            "FIM DO RELATÓRIO",
            "=" * 60
        ])
        
        return "\n".join(report_lines)

# =============================================================================
# INSTÂNCIA GLOBAL
# =============================================================================

# Instância global do gerenciador
test_data_manager = TestDataManager()

# Funções de conveniência
def mark_as_test_data(df: pd.DataFrame, description: str, source_module: str) -> pd.DataFrame:
    """Função de conveniência para marcar dados como teste"""
    return test_data_manager.mark_dataframe_as_test(df, description, source_module)

def validate_test_safety(df: pd.DataFrame) -> Dict[str, Any]:
    """Função de conveniência para validar segurança"""
    return test_data_manager.validate_test_data_safety(df)

def generate_test_data(table_name: str, num_records: int = 10) -> pd.DataFrame:
    """Função de conveniência para gerar dados de teste"""
    return test_data_manager.generate_safe_test_data(table_name, num_records)

def production_safety_check() -> bool:
    """Função de conveniência para verificar segurança de produção"""
    validation = test_data_manager.validate_production_safety()
    return validation['is_safe_for_production']

if __name__ == "__main__":
    # Demonstração do sistema
    print("🔒 DEMONSTRAÇÃO DO GERENCIADOR DE DADOS DE TESTE")
    print("=" * 60)
    
    # Gera dados de teste seguros
    test_clients = generate_test_data('tb_clientes', 3)
    print(f"\n✅ Gerados {len(test_clients)} clientes de teste")
    print(f"Colunas de segurança: {[col for col in test_clients.columns if col.startswith('__TEST_')]}")
    
    # Valida segurança
    safety_report = validate_test_safety(test_clients)
    print(f"\n🔍 Validação de segurança: {'✅ SEGURO' if safety_report['is_safe'] else '⚠️ PROBLEMAS'}")
    
    # Gera relatório
    print("\n📋 RELATÓRIO DE SEGURANÇA:")
    print(test_data_manager.generate_safety_report())
