#!/usr/bin/env python3
"""
Performance Benchmark - Fase 2 Refatoração
Compara performance entre extração sequencial e paralela.
"""

import time
import logging
import pandas as pd
from datetime import datetime
from typing import Dict, Any, List
import json

from salesforce_integration.src.extractors.data_extractors import DataExtractor
from salesforce_integration.monitoring.memory_monitor import get_memory_monitor
from salesforce_integration.config.config import ENVIRONMENT_CONFIG


class PerformanceBenchmark:
    """Classe para executar benchmarks de performance"""
    
    def __init__(self, test_sample: int = 1000):
        self.test_sample = test_sample
        self.logger = logging.getLogger(__name__)
        self.results = {}
        
        self.logger.info(f"Benchmark inicializado com test_sample={test_sample}")
    
    def run_sequential_benchmark(self) -> Dict[str, Any]:
        """Executa benchmark da extração sequencial"""
        self.logger.info("🔄 Executando benchmark SEQUENCIAL...")
        
        monitor = get_memory_monitor()
        extractor = DataExtractor(test_sample=self.test_sample)
        
        # Medições iniciais
        start_time = time.time()
        initial_memory = monitor.get_current_stats()
        
        try:
            # Forçar extração sequencial
            result = extractor.extract_all_sources()
            
            # Medições finais
            end_time = time.time()
            final_memory = monitor.get_current_stats()
            
            # Calcular métricas
            duration = end_time - start_time
            memory_peak = final_memory.rss_mb
            memory_diff = final_memory.rss_mb - initial_memory.rss_mb
            
            # Contar registros
            total_records = 0
            tables_count = 0
            for table_name, df in result['data'].items():
                if isinstance(df, pd.DataFrame):
                    total_records += len(df)
                    tables_count += 1
            
            benchmark_result = {
                'method': 'sequential',
                'duration_seconds': duration,
                'duration_minutes': duration / 60,
                'total_records': total_records,
                'tables_count': tables_count,
                'records_per_second': total_records / duration if duration > 0 else 0,
                'memory_initial_mb': initial_memory.rss_mb,
                'memory_final_mb': final_memory.rss_mb,
                'memory_peak_mb': memory_peak,
                'memory_diff_mb': memory_diff,
                'success': result['success'],
                'errors': result.get('errors', []),
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"✅ Benchmark sequencial concluído em {duration:.2f}s")
            return benchmark_result
            
        except Exception as e:
            self.logger.error(f"❌ Erro no benchmark sequencial: {e}")
            return {
                'method': 'sequential',
                'error': str(e),
                'success': False,
                'timestamp': datetime.now().isoformat()
            }
    
    def run_parallel_benchmark(self) -> Dict[str, Any]:
        """Executa benchmark da extração paralela"""
        self.logger.info("🚀 Executando benchmark PARALELO...")
        
        monitor = get_memory_monitor()
        extractor = DataExtractor(test_sample=self.test_sample)
        
        # Medições iniciais
        start_time = time.time()
        initial_memory = monitor.get_current_stats()
        
        try:
            # Extração paralela
            result = extractor.extract_all_sources_parallel()
            
            # Medições finais
            end_time = time.time()
            final_memory = monitor.get_current_stats()
            
            # Calcular métricas
            duration = end_time - start_time
            memory_peak = final_memory.rss_mb
            memory_diff = final_memory.rss_mb - initial_memory.rss_mb
            
            # Contar registros
            total_records = 0
            tables_count = 0
            for table_name, df in result['data'].items():
                if isinstance(df, pd.DataFrame):
                    total_records += len(df)
                    tables_count += 1
            
            benchmark_result = {
                'method': 'parallel',
                'duration_seconds': duration,
                'duration_minutes': duration / 60,
                'total_records': total_records,
                'tables_count': tables_count,
                'records_per_second': total_records / duration if duration > 0 else 0,
                'memory_initial_mb': initial_memory.rss_mb,
                'memory_final_mb': final_memory.rss_mb,
                'memory_peak_mb': memory_peak,
                'memory_diff_mb': memory_diff,
                'success': result['success'],
                'errors': result.get('errors', []),
                'max_workers': ENVIRONMENT_CONFIG.get('max_extraction_workers', 4),
                'timestamp': datetime.now().isoformat()
            }
            
            self.logger.info(f"✅ Benchmark paralelo concluído em {duration:.2f}s")
            return benchmark_result
            
        except Exception as e:
            self.logger.error(f"❌ Erro no benchmark paralelo: {e}")
            return {
                'method': 'parallel',
                'error': str(e),
                'success': False,
                'timestamp': datetime.now().isoformat()
            }
    
    def compare_results(self, sequential: Dict[str, Any], parallel: Dict[str, Any]) -> Dict[str, Any]:
        """Compara resultados dos benchmarks"""
        if not sequential.get('success') or not parallel.get('success'):
            return {
                'comparison_valid': False,
                'error': 'Um ou ambos benchmarks falharam'
            }
        
        # Calcular melhorias
        time_improvement = (sequential['duration_seconds'] - parallel['duration_seconds']) / sequential['duration_seconds']
        throughput_improvement = (parallel['records_per_second'] - sequential['records_per_second']) / sequential['records_per_second']
        
        comparison = {
            'comparison_valid': True,
            'time_improvement_percent': time_improvement * 100,
            'throughput_improvement_percent': throughput_improvement * 100,
            'sequential_duration': sequential['duration_seconds'],
            'parallel_duration': parallel['duration_seconds'],
            'sequential_throughput': sequential['records_per_second'],
            'parallel_throughput': parallel['records_per_second'],
            'memory_usage_comparison': {
                'sequential_peak_mb': sequential['memory_peak_mb'],
                'parallel_peak_mb': parallel['memory_peak_mb'],
                'memory_difference_mb': parallel['memory_peak_mb'] - sequential['memory_peak_mb']
            },
            'data_consistency': {
                'sequential_records': sequential['total_records'],
                'parallel_records': parallel['total_records'],
                'records_match': sequential['total_records'] == parallel['total_records']
            }
        }
        
        return comparison
    
    def run_full_benchmark(self) -> Dict[str, Any]:
        """Executa benchmark completo e gera relatório"""
        self.logger.info("=" * 60)
        self.logger.info("INICIANDO BENCHMARK COMPLETO DE PERFORMANCE")
        self.logger.info("=" * 60)
        
        # Executar benchmarks
        sequential_result = self.run_sequential_benchmark()
        time.sleep(2)  # Pausa entre testes
        parallel_result = self.run_parallel_benchmark()
        
        # Comparar resultados
        comparison = self.compare_results(sequential_result, parallel_result)
        
        # Gerar relatório final
        report = {
            'benchmark_info': {
                'test_sample': self.test_sample,
                'environment': ENVIRONMENT_CONFIG.get('env', 'unknown'),
                'max_workers': ENVIRONMENT_CONFIG.get('max_extraction_workers', 4),
                'timestamp': datetime.now().isoformat()
            },
            'sequential_benchmark': sequential_result,
            'parallel_benchmark': parallel_result,
            'comparison': comparison
        }
        
        self.results = report
        self._log_summary(report)
        
        return report
    
    def _log_summary(self, report: Dict[str, Any]):
        """Log do resumo dos resultados"""
        self.logger.info("=" * 60)
        self.logger.info("RESUMO DO BENCHMARK")
        self.logger.info("=" * 60)
        
        if report['comparison']['comparison_valid']:
            comp = report['comparison']
            self.logger.info(f"⏱️ Melhoria de tempo: {comp['time_improvement_percent']:.1f}%")
            self.logger.info(f"🚀 Melhoria de throughput: {comp['throughput_improvement_percent']:.1f}%")
            self.logger.info(f"📊 Consistência de dados: {'✅' if comp['data_consistency']['records_match'] else '❌'}")
            
            seq = report['sequential_benchmark']
            par = report['parallel_benchmark']
            self.logger.info(f"📈 Sequencial: {seq['duration_seconds']:.2f}s, {seq['records_per_second']:.0f} rec/s")
            self.logger.info(f"📈 Paralelo: {par['duration_seconds']:.2f}s, {par['records_per_second']:.0f} rec/s")
        else:
            self.logger.error("❌ Comparação inválida - verifique erros nos benchmarks")
        
        self.logger.info("=" * 60)
    
    def save_results(self, filename: str = None):
        """Salva resultados em arquivo JSON"""
        if not self.results:
            self.logger.warning("Nenhum resultado para salvar")
            return
        
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"benchmark_results_{timestamp}.json"
        
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(self.results, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"✅ Resultados salvos em: {filename}")
        except Exception as e:
            self.logger.error(f"❌ Erro ao salvar resultados: {e}")


def main():
    """Função principal para executar benchmark"""
    import sys
    
    # Configurar logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    # Determinar test_sample
    test_sample = 1000
    if len(sys.argv) > 1:
        try:
            test_sample = int(sys.argv[1])
        except ValueError:
            print(f"Erro: test_sample deve ser um número. Usando padrão: {test_sample}")
    
    # Executar benchmark
    benchmark = PerformanceBenchmark(test_sample=test_sample)
    results = benchmark.run_full_benchmark()
    
    # Salvar resultados
    benchmark.save_results()
    
    return results


if __name__ == "__main__":
    main()
