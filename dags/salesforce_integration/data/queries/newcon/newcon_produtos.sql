
-- =====================================================
-- CONSULTA: NewCon Produtos
-- DESCRIÇÃO: Extrai dados de produtos do sistema NewCon
-- FONTE: Banco NewCon (SQL Server)
-- =====================================================

SELECT
    'Produtos' AS end_point,
    vw_grproduto.id_grupo,
    vw_grproduto.id_plano_venda,
    vw_grproduto.id_taxa_plano,
    vw_grproduto.id_produto,
    conbi008a.id_bem,
    vw_grproduto.cd_grupo AS grupo,
    conbe007.nm_bem AS descricao,
    vw_grproduto.nm_produto AS tipo_bem,
    'Consórcio' AS categoria,

    -- Valores financeiros
    CAST(conbi008a.vl_bem AS NUMERIC(18,2)) AS valor_credito,
    NULL AS valor_parc1,
    NULL AS valor_parc1_com_seguro,
    NULL AS valor_demais_parc,
    NULL AS valor_demais_parc_com_seguro,

    -- Informações do plano
    vw_grproduto.pz_comercializacao AS qtd_parcelas,
    conve041.pe_contemplacao AS reducao_contemplacao,
    vw_grproduto.pe_ta AS taxa_admin,
    vw_grproduto.pe_fr AS fundo_reserva,
    CAST(conbe015a.dt_reajuste AS DATE) AS dt_atualizacao

FROM vw_grproduto

    -- Dados do bem/produto
    INNER JOIN conbi008a ON conbi008a.id_plano_venda = vw_grproduto.id_plano_venda
        AND conbi008a.id_taxa_plano = vw_grproduto.id_taxa_plano
        AND conbi008a.id_produto = vw_grproduto.id_produto

    -- Descrição do bem
    INNER JOIN conbe007 ON conbe007.id_bem = conbi008a.id_bem

    -- Dados do plano de venda
    INNER JOIN conve041 ON conve041.id_plano_venda = vw_grproduto.id_plano_venda

    -- Data de último reajuste
    LEFT JOIN (
        SELECT
            id_bem,
            MAX(dt_ht_conbe015) AS dt_reajuste
        FROM conbe015a
        GROUP BY id_bem, id_regiao_fiscal
    ) conbe015a ON conbe015a.id_bem = conbi008a.id_bem
    