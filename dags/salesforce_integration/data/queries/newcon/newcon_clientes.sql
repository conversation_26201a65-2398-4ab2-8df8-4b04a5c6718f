
-- =====================================================
-- CONSULTA: NewCon Clientes
-- DESCRIÇÃO: Extrai dados de clientes do sistema NewCon
-- FONTE: Banco NewCon (SQL Server)
-- =====================================================

SELECT
    'Clientes' AS end_point,
    concc036.id_documento,
    corcc023.cd_inscricao_nacional AS cnpj_cpf,
    LEFT(corcc023.nm_pessoa, CHARINDEX(' ', corcc023.nm_pessoa + ' ') - 1) AS PrimeiroNome,
    corcc023.nm_pessoa AS nome_completo,
    ddd_endereco.nm_cidade AS Cidade,
    ddd_endereco.id_uf AS Estado,
    CAST(corcc023a.dt_nascimento AS DATE) AS dt_nascimento,
    CONCAT('(55', ISNULL(ddd_telefone.ddd, ddd_endereco.ddd), corcc027.telefone, ')') AS celular,
    'br' AS Locale,
    corcc030.e_mail,
    corcc023a.st_sexo AS Genero,
    conve001.nm_fantasia AS PontoVenda,
    CAST(corcc023a.dh_inclusao AS DATE) AS dt_cadastro,
    CAST(corcc023a.vl_renda AS NUMERIC(18,2)) AS Renda,

    -- Flags de status
    CASE
        WHEN corcc023.sn_politicamente_exposto = 'S' THEN 1
        ELSE 0
    END AS politicamente_exposto,

    -- Campos de Opt-in (não utilizados no NewCon)
    NULL AS Optin_seguros_email,
    NULL AS Optin_seguros_SMS,
    NULL AS Optin_seguros_wpp,
    NULL AS Optin_capital_email,
    NULL AS Optin_capital_SMS,
    NULL AS Optin_capital_whatsapp,

    -- Campos de marketing (não utilizados no NewCon)
    NULL AS Campaign,
    NULL AS Source,
    NULL AS Medium,
    NULL AS Term,
    NULL AS Content,

    -- Flags de tipo de cliente
    1 AS cliente_consorcio,
    NULL AS cliente_seguros,
    CASE
        WHEN conve001.id_ponto_venda = 259 THEN 1
        ELSE 0
    END AS cliente_digital,
    NULL AS cliente_capital

FROM corcc023

    -- Busca última venda do cliente
    INNER JOIN (
        SELECT
            MAX(id_cota) AS id_cota,
            id_pessoa
        FROM conve002
        WHERE versao = 0
        GROUP BY id_pessoa
    ) ult_venda ON ult_venda.id_pessoa = corcc023.id_pessoa

    -- Dados da venda
    INNER JOIN conve002 ON conve002.id_cota = ult_venda.id_cota
    INNER JOIN conve001 ON conve001.id_ponto_venda = conve002.id_ponto_venda

    -- Dados complementares da pessoa
    LEFT JOIN corcc023a ON corcc023a.id_pessoa = corcc023.id_pessoa

    -- Dados de contato
    INNER JOIN corcc030 ON corcc030.id_pessoa = conve002.id_pessoa
        AND corcc030.id_e_mail = conve002.id_e_mail

    -- Dados de endereço
    INNER JOIN corcc026 ON corcc026.id_pessoa = conve002.id_pessoa
        AND corcc026.id_endereco = conve002.id_endereco

    -- Dados de telefone
    INNER JOIN corcc027 ON corcc027.id_pessoa = conve002.id_pessoa
        AND corcc027.id_telefone = conve002.id_telefone

    -- DDDs para telefone e endereço
    INNER JOIN corcc015 ddd_endereco ON ddd_endereco.id_cidade = corcc026.id_cidade
    INNER JOIN corcc015 ddd_telefone ON ddd_telefone.id_cidade = corcc027.id_cidade

    -- Dados do documento/cota
    INNER JOIN concc036 ON concc036.id_cota = conve002.id_cota
