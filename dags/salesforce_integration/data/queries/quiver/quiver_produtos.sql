-- =====================================================
-- CONSULTA: Quiver Produtos
-- DESCRIÇÃO: Extrai dados de produtos do sistema Quiver
-- FONTE: <PERSON><PERSON>ui<PERSON> (SQL Server)
-- =====================================================

select
	--t1.*,
	'Produtos' end_point,
	t1.Produto as id_produto,
	t1.Nome as descricao,
	tr.Descricao as categoria,
	tc.Descricao as TipoBem,
	case when t1.Situacao = 'A' then 'Ativo' Else 'Inativo' end as status,
	null as valor_credito,
	null as valor_primeira_parcela,
	null as valor_parcelas,
	null as qtd_parcelas,
	null as reducao_contemplacao,
	null as taxa_admin,
	null as fundo_reserva,
	null as seguro,
	null as dt_atualizacao,
	null as Status_do_contrato,
	null as dt_venda
from Tabela_Produtos t1 (nolock)
inner join [dbo].[Tabela_Ramos] tr (nolock) on tr.Ramo = t1.Ramo
inner join [dbo].[Tabela_celulas] tc (nolock) on tc.Celula = t1.Celula