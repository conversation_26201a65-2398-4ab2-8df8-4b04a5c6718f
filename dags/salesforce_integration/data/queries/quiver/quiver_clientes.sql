-- =====================================================
-- CONSULTA: Quiver Clientes
-- DESCRIÇÃO: Extrai dados de clientes do sistema Quiver
-- FONTE: <PERSON>co Quiver (SQL Server)
-- =====================================================

SELECT
	'Clientes' end_point
	,right(replace(replace(replace(t1.cgc_cpf,'.',''),'-',''),'/',''),14) cnpj_cpf	
	,t1.nome_abreviado PrimeiroNome
	,t1.nome nome_completo
	,t2.cidade Cidade
	,t2.estado Estado
	,t1.data_nascimento dt_nascimento
	,case when coalesce(t3.telefone,'') <> '' then concat('(55', t3.ddd, t3.telefone, ')') else null end as celular
	,'br' Locale
	,t1.e_mail email
	,case t1.tipo_pessoa when 'F' then 'Feminino' when 'M' then 'Masculino' when 'J' then 'Pessoa Juridica' else 'Não declarado' end as Genero
	,null PontoVenda
	,t1.data_inclusao Dt_cadastro
	,t1.renda_fat Renda
	,case
		when t4.cliente is not null then 1
		else 0
	end politicamente_exposta --Tabela_ClientPPE
	,t5.comp_carac Origem --Tabela_ClientCaracs
	,0 Optin_consorcio_email
	,0 Optin_consorcio_SMS
	,0 Optin_consorcio_whatsapp
	,t1.aceita_prop Optin_seguros_email
	,0 Optin_seguros_SMS
	,0 Optin_seguros_wpp
	,0 Optin_digital_email
	,0 Optin_digital_SMS
	,0 Optin_digital_whatsapp
	,0 Optin_capital_email
	,0 Optin_capital_SMS
	,0 Optin_capitaL_whatsapp
	,null Campaign
	,null Source
	,null Medium
	,null Term
	,null Content
	,null cliente_consorcio
	,1 cliente_seguros
	,null cliente_digital
	,null cliente_capital
FROM
    tabela_clientes t1
	inner join
		(
			select
				max(doc.documento) documento
				,doc.cliente
			from
				tabela_documentos doc
				inner join
					tabela_docsrepasses doc_rep on
					doc.documento = doc_rep.documento
					and doc.alteracao = doc_rep.alteracao
			where
				(doc.data_emissao is not null or (doc.situacao <> '2' and doc.data_emissao is null))
				and doc.situacao = 1
				and doc_rep.nivel = 1
				-- and cast(doc.inicio_vigencia as date) >= '2025-06-01'
			group by doc.cliente
		) ult_venda on
		ult_venda.cliente = t1.cliente
	left join
		(
			select
				cliente
				,max(cliente_end) cliente_end
				,max(isnull(data_alteracao, data_inclusao)) dt_cadastro
			from tabela_clientender
			group by cliente
		) ult_endereco on
		ult_endereco.cliente = t1.cliente
	left join
		tabela_clientender t2 on
		t2.cliente = ult_endereco.cliente
		and t2.cliente_end = ult_endereco.cliente_end
	left join
		(
			select
				cliente
				,max(cliente_fone) cliente_fone
				,max(isnull(data_alteracao, data_inclusao)) dt_cadastro
			from tabela_clientfones
			where situacao = 'A' and tipo_telefone = 4
			group by cliente
		) ult_celular on
		ult_celular.cliente = t1.cliente
	left join
		tabela_clientfones t3 on
		t3.cliente = ult_celular.cliente
		and t3.cliente_fone = ult_celular.cliente_fone
	left join
		(
			select
				cliente,
				max(cliente_ppe) cliente_ppe
			from
				Tabela_ClientPPE
			where
				cast(Data_inicial as date) >= cast(getdate() as date)
				and cast(Data_final as date) >= cast(getdate() as date)
				and situacao <> 'I'
			group by
				cliente
		) t4 on
		t4.cliente = t1.cliente
	left join
		tabela_clientcaracs t5 on
		t5.cliente = t1.cliente
		and t5.Tipo_caracteristica = 5 --Origem
		and t5.situacao = 'A'
where coalesce (t1.E_mail,'') <> '' 
	and coalesce(t1.cgc_cpf,'') <> ''
order by 4