-- =====================================================
-- CONSULTA: Orbbits Prices
-- DESCRIÇÃO: Extrai dados de preços com progressão de parcelas
-- FONTE: Banco Orbbits (MySQL)
-- =====================================================

WITH primeira_parc AS (
    -- Valores da primeira parcela
    SELECT
        id_plano_de_venda,
        id_ponto_de_venda,
        id_bem,
        GROUP_CONCAT(CONCAT(parc_inic, '-', parc_final, ' = ', valor_parc_sem_seguro) SEPARATOR '; ') AS valor_primeira_parcela,
        GROUP_CONCAT(CONCAT(parc_inic, '-', parc_final, ' = ', valor_parc_com_seguro) SEPARATOR '; ') AS valor_primeira_parcela_c_seguro
    FROM orbbitsdb.lista_preco_progressao
    WHERE parc_inic = 1
    GROUP BY id_plano_de_venda, id_ponto_de_venda, id_bem
),

demais_parc AS (
    -- Valores das demais parcelas
    SELECT
        id_plano_de_venda,
        id_ponto_de_venda,
        id_bem,
        GROUP_CONCAT(CONCAT(parc_inic, '-', parc_final, ' = ', valor_parc_sem_seguro) SEPARATOR '; ') AS valor_parcelas,
        GROUP_CONCAT(CONCAT(parc_inic, '-', parc_final, ' = ', valor_parc_com_seguro) SEPARATOR '; ') AS valor_parcelas_c_seguro
    FROM orbbitsdb.lista_preco_progressao
    WHERE parc_inic > 1
    GROUP BY id_plano_de_venda, id_ponto_de_venda, id_bem
)

-- Resultado final combinando primeira parcela e demais
SELECT
    p.id_plano_de_venda,
    p.id_ponto_de_venda,
    p.id_bem,
    p.valor_primeira_parcela,
    p.valor_primeira_parcela_c_seguro,
    d.valor_parcelas,
    d.valor_parcelas_c_seguro,
    CONCAT(p.id_ponto_de_venda, '-', 'grupo', '-', p.id_plano_de_venda, '-', p.id_bem) AS idProduto
FROM primeira_parc p
    LEFT JOIN demais_parc d ON p.id_plano_de_venda = d.id_plano_de_venda
        AND p.id_ponto_de_venda = d.id_ponto_de_venda
        AND p.id_bem = d.id_bem