-- =====================================================
-- CONSULTA: Orbbits Payments
-- DESCRIÇÃO: Extrai dados de pagamentos com links por tipo
-- FONTE: Banco Orbbits (MySQL)
-- =====================================================

SELECT DISTINCT
    p.newcon_proposal_contract_number,

    -- Links de pagamento por tipo (último de cada tipo)
    (SELECT payment_link
     FROM orbbits_charges.payments
     WHERE newcon_proposal_contract_number = p.newcon_proposal_contract_number
       AND type = 'bill'
     ORDER BY id DESC
     LIMIT 1) AS payment_link_bill,

    (SELECT payment_link
     FROM orbbits_charges.payments
     WHERE newcon_proposal_contract_number = p.newcon_proposal_contract_number
       AND type = 'credit_card'
     ORDER BY id DESC
     LIMIT 1) AS payment_link_credit_card,

    (SELECT payment_link
     FROM orbbits_charges.payments
     WHERE newcon_proposal_contract_number = p.newcon_proposal_contract_number
       AND type = 'pix'
     ORDER BY id DESC
     LIMIT 1) AS payment_link_pix,

    -- Dados do último pagamento efetivado
    (SELECT payment_date
     FROM orbbits_charges.payments
     WHERE newcon_proposal_contract_number = p.newcon_proposal_contract_number
       AND status = 'paid'
     ORDER BY id DESC
     LIMIT 1) AS payment_date,

    (SELECT type
     FROM orbbits_charges.payments
     WHERE newcon_proposal_contract_number = p.newcon_proposal_contract_number
       AND status = 'paid'
       AND payment_date IS NOT NULL
     ORDER BY id DESC
     LIMIT 1) AS type

FROM orbbits_charges.payments p
