"""
Airflow Tasks Module - Task Functions for Airflow DAGs

This module contains all Airflow task functions organized by type:
- extraction_tasks: Data extraction tasks
- transformation_tasks: Data transformation tasks  
- loading_tasks: Data loading tasks
"""

# Import all task functions for backward compatibility
from .extraction_tasks import *
from .transformation_tasks import *
from .loading_tasks import *
