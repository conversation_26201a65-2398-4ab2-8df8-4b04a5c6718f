#!/usr/bin/env python3
"""
Memory Monitor - Sistema de Monitoramento de Memória
Monitora uso de memória em tempo real e executa garbage collection automático.
"""

import gc
import logging
import psutil
import time
from typing import Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime

from salesforce_integration.config.config import ENVIRONMENT_CONFIG


@dataclass
class MemoryStats:
    """Estatísticas de uso de memória"""
    rss_mb: float
    vms_mb: float
    percent: float
    available_mb: float
    timestamp: datetime
    
    def to_dict(self) -> Dict[str, Any]:
        return {
            'rss_mb': self.rss_mb,
            'vms_mb': self.vms_mb,
            'percent': self.percent,
            'available_mb': self.available_mb,
            'timestamp': self.timestamp.isoformat()
        }


class MemoryMonitor:
    """Monitor de memória com garbage collection automático"""
    
    def __init__(self, memory_limit_mb: Optional[int] = None, gc_threshold: float = 0.8):
        self.logger = logging.getLogger(__name__)
        self.memory_limit_mb = memory_limit_mb or ENVIRONMENT_CONFIG.get('memory_limit_mb', 2048)
        self.gc_threshold = gc_threshold  # Porcentagem do limite para trigger GC
        self.process = psutil.Process()
        self.stats_history = []
        self.gc_count = 0
        
        self.logger.info(f"MemoryMonitor inicializado - Limite: {self.memory_limit_mb}MB, GC Threshold: {gc_threshold*100}%")
    
    def get_current_stats(self) -> MemoryStats:
        """Obtém estatísticas atuais de memória"""
        try:
            # Estatísticas do processo atual
            memory_info = self.process.memory_info()
            
            # Estatísticas do sistema
            system_memory = psutil.virtual_memory()
            
            stats = MemoryStats(
                rss_mb=memory_info.rss / 1024 / 1024,
                vms_mb=memory_info.vms / 1024 / 1024,
                percent=system_memory.percent,
                available_mb=system_memory.available / 1024 / 1024,
                timestamp=datetime.now()
            )
            
            return stats
            
        except Exception as e:
            self.logger.error(f"Erro ao obter estatísticas de memória: {e}")
            # Retorna stats vazias em caso de erro
            return MemoryStats(0, 0, 0, 0, datetime.now())
    
    def check_memory_usage(self, force_gc: bool = False) -> Dict[str, Any]:
        """Verifica uso de memória e executa GC se necessário"""
        stats = self.get_current_stats()
        self.stats_history.append(stats)
        
        # Manter apenas últimas 100 medições
        if len(self.stats_history) > 100:
            self.stats_history = self.stats_history[-100:]
        
        # Verificar se precisa de garbage collection
        memory_usage_ratio = stats.rss_mb / self.memory_limit_mb
        needs_gc = force_gc or memory_usage_ratio > self.gc_threshold
        
        result = {
            'stats': stats.to_dict(),
            'memory_usage_ratio': memory_usage_ratio,
            'needs_gc': needs_gc,
            'gc_executed': False,
            'memory_freed_mb': 0
        }
        
        if needs_gc:
            result.update(self._execute_garbage_collection(stats))
        
        # Log de alerta se uso alto
        if memory_usage_ratio > 0.9:
            self.logger.warning(f"⚠️ Alto uso de memória: {stats.rss_mb:.1f}MB ({memory_usage_ratio*100:.1f}% do limite)")
        elif memory_usage_ratio > self.gc_threshold:
            self.logger.info(f"📊 Uso de memória: {stats.rss_mb:.1f}MB ({memory_usage_ratio*100:.1f}% do limite)")
        
        return result
    
    def _execute_garbage_collection(self, before_stats: MemoryStats) -> Dict[str, Any]:
        """Executa garbage collection e mede efetividade"""
        self.logger.info(f"🗑️ Executando garbage collection (uso atual: {before_stats.rss_mb:.1f}MB)")
        
        # Executar garbage collection
        gc_start = time.time()
        collected = gc.collect()
        gc_time = time.time() - gc_start
        
        # Medir memória após GC
        after_stats = self.get_current_stats()
        memory_freed = before_stats.rss_mb - after_stats.rss_mb
        
        self.gc_count += 1
        
        self.logger.info(
            f"✅ GC concluído em {gc_time:.2f}s - "
            f"Objetos coletados: {collected}, "
            f"Memória liberada: {memory_freed:.1f}MB"
        )
        
        return {
            'gc_executed': True,
            'memory_freed_mb': memory_freed,
            'objects_collected': collected,
            'gc_time_seconds': gc_time,
            'after_stats': after_stats.to_dict()
        }
    
    def get_memory_summary(self) -> Dict[str, Any]:
        """Retorna resumo do uso de memória"""
        if not self.stats_history:
            return {'error': 'Nenhuma estatística disponível'}
        
        current = self.stats_history[-1]
        
        # Calcular estatísticas agregadas
        rss_values = [s.rss_mb for s in self.stats_history]
        
        return {
            'current': current.to_dict(),
            'limit_mb': self.memory_limit_mb,
            'usage_ratio': current.rss_mb / self.memory_limit_mb,
            'gc_count': self.gc_count,
            'stats_count': len(self.stats_history),
            'peak_rss_mb': max(rss_values),
            'avg_rss_mb': sum(rss_values) / len(rss_values),
            'min_rss_mb': min(rss_values)
        }
    
    def log_memory_summary(self):
        """Log do resumo de memória"""
        summary = self.get_memory_summary()
        if 'error' in summary:
            self.logger.warning(summary['error'])
            return
        
        self.logger.info(
            f"📊 Resumo de Memória - "
            f"Atual: {summary['current']['rss_mb']:.1f}MB "
            f"({summary['usage_ratio']*100:.1f}%), "
            f"Pico: {summary['peak_rss_mb']:.1f}MB, "
            f"GCs: {summary['gc_count']}"
        )


# =============================================================================
# FUNÇÕES DE CONVENIÊNCIA
# =============================================================================

# Instância global do monitor
_memory_monitor = None

def get_memory_monitor() -> MemoryMonitor:
    """Obtém instância global do monitor de memória"""
    global _memory_monitor
    if _memory_monitor is None:
        _memory_monitor = MemoryMonitor()
    return _memory_monitor

def get_memory_usage() -> Dict[str, Any]:
    """Função de conveniência para obter uso atual de memória"""
    monitor = get_memory_monitor()
    return monitor.get_current_stats().to_dict()

def check_memory_and_gc() -> Dict[str, Any]:
    """Função de conveniência para verificar memória e executar GC se necessário"""
    monitor = get_memory_monitor()
    return monitor.check_memory_usage()

def force_garbage_collection() -> Dict[str, Any]:
    """Força execução de garbage collection"""
    monitor = get_memory_monitor()
    return monitor.check_memory_usage(force_gc=True)

def log_memory_summary():
    """Log do resumo de memória"""
    monitor = get_memory_monitor()
    monitor.log_memory_summary()


# =============================================================================
# DECORADOR PARA MONITORAMENTO AUTOMÁTICO
# =============================================================================

def monitor_memory(func):
    """Decorador para monitorar memória durante execução de função"""
    def wrapper(*args, **kwargs):
        monitor = get_memory_monitor()
        
        # Verificar memória antes
        before = monitor.check_memory_usage()
        
        try:
            # Executar função
            result = func(*args, **kwargs)
            
            # Verificar memória depois
            after = monitor.check_memory_usage()
            
            # Log se houve mudança significativa
            memory_diff = after['stats']['rss_mb'] - before['stats']['rss_mb']
            if abs(memory_diff) > 50:  # Mais de 50MB de diferença
                monitor.logger.info(
                    f"📊 {func.__name__} - Mudança de memória: {memory_diff:+.1f}MB"
                )
            
            return result
            
        except Exception as e:
            # Verificar memória em caso de erro
            monitor.check_memory_usage()
            raise
    
    return wrapper
