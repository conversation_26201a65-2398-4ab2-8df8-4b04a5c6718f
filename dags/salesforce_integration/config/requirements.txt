# =============================================================================
# REQUIREMENTS.TXT - ETL SALESFORCE MARKETING CLOUD
# =============================================================================
# Pipeline ETL para integração com Salesforce Marketing Cloud
# Versão atualizada com todas as dependências identificadas
# Data: 2025-01-17

# =============================================================================
# CORE DEPENDENCIES - ESSENCIAIS
# =============================================================================

# Orquestração de workflows
apache-airflow>=2.5.0,<3.0.0

# Manipulação de dados
pandas>=1.5.0,<2.0.0
numpy>=1.24.0,<2.0.0

# Cliente HTTP para APIs
requests>=2.28.0,<3.0.0
urllib3>=1.26.0,<2.0.0

# Configuração por variáveis de ambiente
python-decouple>=3.6,<4.0

# =============================================================================
# CONECTORES DE BANCO DE DADOS
# =============================================================================

# SQL Server (NewCon)
pymssql>=2.2.5,<3.0.0

# PostgreSQL (DW Corporativo)
psycopg2-binary>=2.9.0,<3.0.0

# MySQL (Orbbits)
mysql-connector-python>=8.0.0,<9.0.0

# =============================================================================
# APIS E INTEGRAÇÕES EXTERNAS
# =============================================================================

# RD Station API
rdstation>=1.0.0,<2.0.0

# Salesforce Marketing Cloud (se necessário)
# salesforce-marketing-cloud>=1.0.0,<2.0.0

# =============================================================================
# MONITORAMENTO E PERFORMANCE
# =============================================================================

# Informações do sistema
psutil>=5.9.0,<6.0.0

# Logs estruturados
structlog>=22.0.0,<23.0.0

# =============================================================================
# PROCESSAMENTO E UTILITÁRIOS
# =============================================================================

# Paralelização avançada
concurrent-futures>=3.1.1;python_version<"3.2"

# Operações decimais precisas
decimal>=1.70

# Data e hora
python-dateutil>=2.8.0,<3.0.0

# Validação de dados
cerberus>=1.3.0,<2.0.0

# =============================================================================
# DESENVOLVIMENTO E TESTES
# =============================================================================

# Testes unitários
pytest>=7.0.0,<8.0.0
pytest-cov>=4.0.0,<5.0.0

# Qualidade de código
black>=22.0.0,<23.0.0
flake8>=5.0.0,<6.0.0
isort>=5.10.0,<6.0.0

# =============================================================================
# SEGURANÇA E AUTENTICAÇÃO
# =============================================================================

# Criptografia
cryptography>=3.4.8,<4.0.0

# Tokens JWT
PyJWT>=2.4.0,<3.0.0

# =============================================================================
# OPCIONAIS - PERFORMANCE E CACHE
# =============================================================================

# Cache Redis (opcional)
redis>=4.3.0,<5.0.0

# Processamento paralelo distribuído (opcional)
celery>=5.2.0,<6.0.0

# Compressão de dados (opcional)
lz4>=4.0.0,<5.0.0

# =============================================================================
# AIRFLOW PROVIDERS
# =============================================================================

# Providers para conexões específicas
apache-airflow-providers-postgres>=5.0.0,<6.0.0
apache-airflow-providers-mysql>=5.0.0,<6.0.0
apache-airflow-providers-microsoft-mssql>=3.0.0,<4.0.0

# =============================================================================
# COMPATIBILIDADE E EXTRAS
# =============================================================================

# Para compatibilidade com versões antigas do Python
typing-extensions>=4.0.0,<5.0.0

# Para serialização JSON avançada
orjson>=3.8.0,<4.0.0

# Para manipulação de arquivos CSV grandes
csvkit>=1.0.7,<2.0.0

# =============================================================================
# NOTAS DE INSTALAÇÃO
# =============================================================================

# Para instalar todas as dependências:
# pip install -r requirements.txt

# Para instalar apenas as dependências essenciais:
# pip install apache-airflow pandas requests python-decouple pymssql psycopg2-binary mysql-connector-python

# Para desenvolvimento:
# pip install -r requirements.txt pytest black flake8

# =============================================================================
# DEPENDÊNCIAS DO SISTEMA OPERACIONAL
# =============================================================================

# Ubuntu/Debian:
# sudo apt-get update
# sudo apt-get install -y python3-dev libpq-dev default-libmysqlclient-dev build-essential

# CentOS/RHEL:
# sudo yum install -y python3-devel postgresql-devel mysql-devel gcc

# =============================================================================
# VARIÁVEIS DE AMBIENTE NECESSÁRIAS
# =============================================================================

# Configurações obrigatórias (exemplo):
# export AIRFLOW_HOME=/opt/airflow
# export SALESFORCE_CLIENT_ID=your_client_id
# export SALESFORCE_CLIENT_SECRET=your_client_secret
# export NEWCON_SERVER=server_address
# export NEWCON_DATABASE=database_name
# export NEWCON_USERNAME=username
# export NEWCON_PASSWORD=password
# export DW_HOST=postgresql_host
# export DW_DATABASE=database_name
# export DW_USER=username
# export DW_PASSWORD=password
# export ORBBITS_HOST=mysql_host
# export ORBBITS_DATABASE=database_name
# export ORBBITS_USER=username
# export ORBBITS_PASSWORD=password
# export RD_TOKEN=rd_station_token