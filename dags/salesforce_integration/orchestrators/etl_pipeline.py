#!/usr/bin/env python3
"""
ETL Pipeline Orchestrator - Extracted from etl_main.py

Main pipeline orchestrator class responsible for coordinating the entire ETL process.
This class was extracted from the monolithic etl_main.py file as part of the refactoring effort.
"""

import time
import logging
from typing import Dict, List, Any, Optional

# Import ETL modules
from salesforce_integration.src.extractors.data_extractors import DataExtractor
from salesforce_integration.src.transformers.data_transformers import transform_all_data
from salesforce_integration.src.clients.salesforce_client import (
    process_etl_pipeline,
    validate_salesforce_config,
    test_salesforce_connection
)
from salesforce_integration.src.connections.database_connections import test_all_connections


class ETLPipeline:
    """Orquestrador principal do pipeline ETL"""
    
    def __init__(self, dry_run: bool = False, test_sample: int = None):
        self.dry_run = dry_run
        self.test_sample = test_sample
        self.logger = logging.getLogger(__name__)
        self.execution_stats = {
            'start_time': None,
            'end_time': None,
            'total_records': 0,
            'tables_processed': [],
            'errors': []
        }

        # Componentes do pipeline
        self.extractor = DataExtractor(test_sample=test_sample)
        self.sf_client = None
        
        # Initialize failure tracking (fix for missing failure_reporter)
        self.failures = []

        sample_info = f" (amostra: {test_sample})" if test_sample else ""
        self.logger.info(f"ETL Pipeline inicializado (dry_run={dry_run}){sample_info}")
    
    def validate_prerequisites(self) -> bool:
        """Valida pré-requisitos antes da execução"""
        self.logger.info("Validando pré-requisitos...")
        
        # Valida configuração do Salesforce
        is_valid, missing_fields = validate_salesforce_config()
        if not is_valid:
            self.logger.error(f"❌ Configuração Salesforce inválida: {missing_fields}")
            return False
        
        # Testa conexões de banco de dados
        if not test_all_connections():
            self.logger.error("❌ Falha na conexão com bancos de dados")
            return False
        
        # Testa conexão com Salesforce (se não for dry-run)
        if not self.dry_run:
            if not test_salesforce_connection():
                self.logger.error("❌ Falha na conexão com Salesforce")
                return False
        
        self.logger.info("✅ Todos os pré-requisitos validados")
        return True
    
    def extract_data(self, table_filter: Optional[List[str]] = None) -> Dict[str, Any]:
        """Extrai dados de todas as fontes"""
        self.logger.info("Iniciando extração de dados...")
        extraction_start = time.time()
        
        try:
            # Extrai dados de todas as fontes usando paralelização
            from salesforce_integration.config.config import ENVIRONMENT_CONFIG

            if ENVIRONMENT_CONFIG.get('parallel_execution', True):
                self.logger.info("🚀 Usando extração PARALELA")
                extraction_result = self.extractor.extract_all_sources_parallel()
            else:
                self.logger.info("⏳ Usando extração sequencial")
                extraction_result = self.extractor.extract_all_sources()

            if not extraction_result.get('success', False):
                self.logger.error("❌ Falha na extração de dados")
                errors = extraction_result.get('errors', ['Erro desconhecido'])
                return {'success': False, 'error': f'Falha na extração: {"; ".join(errors)}'}
            
            # Registra métricas de extração por fonte
            extraction_time = (time.time() - extraction_start) / 60
            for table_name, df in extraction_result['data'].items():
                records_count = len(df) if hasattr(df, '__len__') else 0
                
                # Log de volumes suspeitos
                if records_count == 0:
                    self.logger.warning(f"⚠️ Extração de {table_name} retornou 0 registros")
            
            # Filtra tabelas se especificado
            if table_filter:
                filtered_data = {}
                for table in table_filter:
                    if table in extraction_result['data']:
                        filtered_data[table] = extraction_result['data'][table]
                    else:
                        self.logger.warning(f"⚠️ Tabela '{table}' não encontrada nos dados extraídos")
                
                extraction_result['data'] = filtered_data
            
            self.logger.info(f"✅ Extração concluída: {extraction_result['total_records']} registros")
            return extraction_result
            
        except Exception as e:
            self.logger.error(f"❌ Erro na extração: {e}")
            return {'success': False, 'error': str(e)}

    def transform_data(self, raw_data: Dict[str, Any]) -> Dict[str, Any]:
        """Transforma dados extraídos"""
        self.logger.info("Iniciando transformação de dados...")
        transformation_start = time.time()

        try:
            # Conta registros antes da transformação
            input_counts = {}
            for table_name, df in raw_data.items():
                input_counts[table_name] = len(df) if hasattr(df, '__len__') else 0

            # Transforma dados usando o módulo de transformação
            transformation_result = transform_all_data(raw_data)

            if not transformation_result['success']:
                self.logger.error("❌ Falha na transformação de dados")
                return {'success': False, 'error': 'Falha na transformação'}

            # Verifica perda de registros na transformação
            transformation_time = (time.time() - transformation_start) / 60
            for table_name, df in transformation_result['transformed_data'].items():
                output_count = len(df) if hasattr(df, '__len__') else 0

                # Estima input baseado em mapeamentos conhecidos
                estimated_input = 0
                if table_name == 'tb_clientes':
                    estimated_input = input_counts.get('newcon_clients', 0)
                elif table_name == 'tb_produtos':
                    estimated_input = input_counts.get('newcon_products', 0)
                elif table_name == 'tb_propostas':
                    estimated_input = input_counts.get('newcon_proposals', 0)
                elif table_name == 'tb_leads':
                    estimated_input = input_counts.get('newcon_leads', 0) + input_counts.get('rdstation_leads', 0)

                # Log de perda significativa de registros
                if estimated_input > 0 and output_count < estimated_input * 0.8:  # Mais de 20% de perda
                    loss_percentage = (estimated_input - output_count) / estimated_input
                    self.logger.warning(f"⚠️ {loss_percentage:.0%} perda de registros na transformação de {table_name} ({output_count:,} vs {estimated_input:,})")

                # Detecta campos obrigatórios nulos
                if hasattr(df, 'isnull'):
                    required_fields = {
                        'tb_clientes': ['cnpjcpf', 'email'],
                        'tb_leads': ['cnpjcpf', 'dt_simulacao'],
                        'tb_produtos': ['id_produto'],
                        'tb_propostas': ['idproposta', 'email']
                    }.get(table_name, [])

                    for field in required_fields:
                        if field in df.columns:
                            null_count = df[field].isnull().sum()
                            if null_count > 0:
                                self.logger.warning(f"⚠️ Campo obrigatório '{field}' com {null_count} valores nulos em {table_name}")

            self.logger.info(f"✅ Transformação concluída: {transformation_result['total_records']} registros")
            return transformation_result

        except Exception as e:
            self.logger.error(f"❌ Erro na transformação: {e}")
            return {'success': False, 'error': str(e)}

    def load_data(self, transformed_data: Dict[str, List[Dict[str, Any]]],
                  table_sequence: Optional[List[str]] = None) -> Dict[str, Any]:
        """Carrega dados no Salesforce"""
        self.logger.info("Iniciando carregamento de dados...")
        loading_start = time.time()

        try:
            # Carrega dados usando o cliente Salesforce
            load_result = process_etl_pipeline(
                tables_data=transformed_data,
                dry_run=self.dry_run,
                table_sequence=table_sequence
            )

            if not load_result['success']:
                self.logger.error("❌ Falha no carregamento de dados")
                return {'success': False, 'error': 'Falha no carregamento'}

            # Registra métricas de carregamento e detecta falhas
            loading_time = (time.time() - loading_start) / 60
            processing_summary = load_result.get('processing_summary', {})

            for table_name, table_result in processing_summary.items():
                total_records = table_result.get('total_records', 0)
                failed_batches = table_result.get('failed_batches', 0)
                errors = table_result.get('errors', [])

                # Log de métricas básicas
                self.logger.info(f"📊 {table_name}: {total_records} registros, {failed_batches} lotes falharam")

                # Log de falhas específicas do Salesforce
                if not table_result.get('success', True):
                    self.logger.error(f"❌ {table_name}: {failed_batches} lotes falharam de {table_result.get('total_batches', 0)}")
                    if errors:
                        self.logger.error(f"Primeiros erros: {errors[:3]}")

            self.logger.info(f"✅ Carregamento concluído: {load_result['total_records']} registros")
            return load_result

        except Exception as e:
            self.logger.error(f"❌ Erro no carregamento: {e}")
            return {'success': False, 'error': str(e)}

    def generate_failure_report(self) -> str:
        """Generates a simple failure report (replacement for missing failure_reporter)"""
        if not self.failures:
            return "No failures detected"

        report_lines = ["FAILURE REPORT:", "=" * 50]
        for i, failure in enumerate(self.failures, 1):
            report_lines.append(f"{i}. {failure}")

        return "\n".join(report_lines)

    def get_failure_summary(self) -> Dict[str, Any]:
        """Gets failure summary (replacement for missing failure_reporter)"""
        return {
            'total_failures': len(self.failures),
            'failed_tables': list(set(f.split(':')[0] for f in self.failures if ':' in f)),
            'failures': self.failures
        }

    def run_pipeline(self, table_filter: Optional[List[str]] = None,
                    table_sequence: Optional[List[str]] = None) -> Dict[str, Any]:
        """Executa pipeline ETL completo"""
        self.execution_stats['start_time'] = time.time()

        self.logger.info("=" * 60)
        self.logger.info("INICIANDO PIPELINE ETL CONSOLIDADO")
        self.logger.info("=" * 60)

        # Valida pré-requisitos
        if not self.validate_prerequisites():
            return {'success': False, 'error': 'Falha na validação de pré-requisitos'}

        # Extrai dados
        extraction_result = self.extract_data(table_filter)
        if not extraction_result['success']:
            return extraction_result

        # Transforma dados
        transformation_result = self.transform_data(extraction_result['data'])
        if not transformation_result['success']:
            return transformation_result

        # Carrega dados
        load_result = self.load_data(
            transformation_result['transformed_data'],
            table_sequence
        )
        if not load_result['success']:
            return load_result

        # Estatísticas finais
        self.execution_stats['end_time'] = time.time()
        self.execution_stats['total_time'] = (
            self.execution_stats['end_time'] - self.execution_stats['start_time']
        )
        self.execution_stats['total_records'] = load_result['total_records']
        self.execution_stats['tables_processed'] = load_result['tables_processed']

        # Gera relatório de falhas
        failure_report = self.generate_failure_report()

        # Log final
        self.logger.info("=" * 60)
        self.logger.info("PIPELINE ETL CONCLUÍDO")
        self.logger.info("=" * 60)
        self.logger.info(f"Tempo total: {self.execution_stats['total_time']:.2f}s")
        self.logger.info(f"Registros processados: {self.execution_stats['total_records']}")
        self.logger.info(f"Tabelas processadas: {len(self.execution_stats['tables_processed'])}")

        # Exibe relatório de falhas se houver problemas
        if self.failures:
            self.logger.warning("⚠️ PROBLEMAS ENCONTRADOS DURANTE A EXECUÇÃO:")
            for line in failure_report.split('\n'):
                if line.strip():
                    self.logger.warning(line)
        else:
            self.logger.info("✅ Nenhum problema detectado durante a execução")

        return {
            'success': True,
            'execution_stats': self.execution_stats,
            'extraction_result': extraction_result,
            'transformation_result': transformation_result,
            'load_result': load_result,
            'failure_report': failure_report,
            'failure_summary': self.get_failure_summary()
        }

    def run_single_table(self, table_name: str) -> Dict[str, Any]:
        """Executa pipeline para uma única tabela"""
        self.logger.info(f"Executando pipeline para tabela: {table_name}")

        return self.run_pipeline(table_filter=[table_name])

    def get_execution_stats(self) -> Dict[str, Any]:
        """Retorna estatísticas da execução"""
        return self.execution_stats

    def cleanup(self):
        """Limpa recursos"""
        if self.sf_client:
            self.sf_client.close()

        self.logger.info("Pipeline finalizado")
