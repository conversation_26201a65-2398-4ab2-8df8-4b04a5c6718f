"""
SYONET INTELIGENTE ETL - ESTRATÉGIA INTELIGENTE COM ANÁLISE DE DIFERENÇA

Este ETL implementa uma estratégia inteligente que analisa a diferença real
entre origem e destino para decidir a melhor abordagem:

1. NÍVEL 1 - INCREMENTAL (DIÁRIO): Tenta carregar apenas dados do dia atual

2. NÍVEL 2 - ANÁLISE INTELIGENTE:
   📊 Analisa diferença entre origem e destino
   • Se diferença ≤ 10%: INCREMENTAL 7 DIAS (últimos 7 dias - rápido e eficiente)
   • Se diferença > 10%: FULL LOAD direto (reprocessamento completo)

3. NÍVEL 3 - FULL LOAD: Fallback final se incremental 7 dias falhar

Aplicado a:
✅ Tabelas normais: Incremental → Análise → 7 Dias/Full → Full Load  
✅ SQL customizado: Incremental Customizado → Análise → 7 Dias Customizado/Full Customizado → Full Customizado

Benefícios:
✅ Decisão automática baseada em dados reais (MAX_GAP_PERCENTAGE = 10%)
✅ Incremental 7 dias para diferenças pequenas (muito mais rápido que gap filling)
✅ Full Load direto para diferenças grandes (evita overhead desnecessário)
✅ Logs detalhados mostram qual estratégia foi escolhida e por quê
✅ Suporte completo a chaves simples e compostas

Estratégias implementadas:
- INCREMENTAL: dt_inc/dt_alt >= hoje  
- INCREMENTAL_7_DAYS: dt_inc/dt_alt >= hoje-7 (DELETE/INSERT últimos 7 dias)
- FULL_LOAD: DROP + CREATE completo

Suporte a chaves:
✅ Chaves simples: id_field = 'id_cliente'
✅ Chaves compostas: id_field = 'id_cliente,id_empresa' (concatenação automática)

Configurações:
- MAX_GAP_PERCENTAGE = 10% (limite para decidir 7 dias vs full load)
- SEVEN_DAYS_TIMEOUT = 300s (timeout de 5 minutos para incremental 7 dias)
"""

from datetime import datetime, timedelta
from airflow import DAG
from airflow.operators.python import PythonOperator
from airflow.operators.dummy import DummyOperator
from airflow.utils.trigger_rule import TriggerRule
from airflow.exceptions import AirflowSkipException
import pymssql
import logging
import threading
import time
import re
from contextlib import contextmanager

# ===== CONFIGURAÇÕES UNIFICADAS =====

# Configurações base da DAG
BASE_DAG_ARGS = {
    'owner': 'airflow',
    'depends_on_past': False,
    'start_date': datetime(2024, 1, 1),
    'email_on_failure': False,
    'email_on_retry': False,
    'retries': 2,
    'retry_delay': timedelta(minutes=1),
    'trigger_rule': TriggerRule.ALL_DONE,
}

# Configuração de conexão centralizada
DB_CONFIG = {
    'server': '*************',
    'user': 'bq_dwcorporativo_u', 
    'password': 'N#OK+#{Yx*',
    'database': 'dbdwsyonet',
    'port': 52543
}

# Configurações de tolerância para validação
MAX_ABS_DIFF = 10

# Configurações para Incremental 7 Dias Inteligente
MAX_GAP_PERCENTAGE = 10  # Se diferença ≤ 10%, tenta incremental 7 dias
SEVEN_DAYS_TIMEOUT = 300  # Timeout de 5 minutos (300 segundos) para incremental 7 dias

# Filtro para carga incremental (diária) - ASPAS DUPLAS CORRETAS
DAILY_FILTER_CONDITION = """(TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)
                            OR
                            TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP))"""

# Filtro para carga incremental (7 dias) - ASPAS DUPLAS CORRETAS
SEVEN_DAYS_FILTER_CONDITION = """(TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days''
                                 OR
                                 TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days'')"""

# Configuração única e simples - apenas o essencial
TABLES_CONFIG = {
    # Tabelas pequenas (sempre full load)
    'syo_agenda': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_contaemail': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_contatoemail': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_clientearea': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_telefones': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'campanhav2whatsapp': {'id_field': None, 'source': 'syo_campanhav2whatsapp', 'select': None, 'custom_sql': None},
    'syo_novaclassificacao': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_novaclassificacaotipocliente': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_faixanovaclassificacao': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_etapafunil': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_empresa': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_empresausuario': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_donoconta': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_oficina': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_veiculo': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_interfacenegociacao': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_campointerfacenegociacao': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_usuario': {'id_field': None, 'source': None, 'select': 'id_usuario,nm_login,id_grupo,id_dominio,id_tipo,ap_usuario,nm_usuario,nm_sobrenome,ds_usuario,ds_email,ic_ativo,id_empresa,no_cgccpf', 'custom_sql': None},
    
    # Tabelas grandes (incremental com fallback)
    'syo_evento': {'id_field': 'id_evento', 'source': None, 'select': 'id_evento, id_pesquisa, id_contato, id_estacao, id_empresa, id_cliente, nm_cliente, id_agente, id_ramal, id_statusevento, id_situacaoevento, id_prioridadeevento, id_grupoevento, id_tipoevento, id_componente, id_grupocampanha, id_email, ds_formacontato, ds_acaoevento, dt_proximaacao, ds_iniciativacontato, ds_assunto, ds_origem, dt_horainicio, dt_horafinal, dt_limite, substring(ds_conclusao,1,3800) as ds_conclusao, dt_conclusao, ds_palavrachave, dt_previsaoresposta, dt_previsaotermino, ic_primeiroatendimento, id_dealer, id_campanha, no_versao, cd_usuarioinc, cd_usuarioalt, dt_inc, dt_alt, no_nota, ds_observacao_json, ds_prisma, ds_midia, ds_pedidocompra_json, id_agenda, no_minuta, ds_valorinvestido, ds_tempoinvestido, no_horaproximaacao, id_equipesobdemanda, no_pedido, dt_periodobase, id_modulocriacaoevento, ic_geradoautomatico, ic_principal, id_eventoprincipal, ic_tempolimiteesgotado, ds_resultado, ic_reativar, dt_classificacaofrio, id_campanhav2, ds_temperatura, uuid_evento, dt_visita, dt_venda, id_agendamentoerp, ic_centralleads, dt_previsaoentrega, id_filarecepcao, ds_resumolead, dt_visitafrotista, id_atendente_atual, id_empresa_atual, id_usuario_atual, no_os', 'custom_sql': None},
    'syo_cliente': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    'syo_acao': {'id_field': 'id_acao', 'source': None, 'select': 'id_acao, id_evento, id_cliente, id_agente, tp_acao, ds_origem, ds_iniciativa, dh_acao, ds_resultado, CAST(LEFT(ds_conclusaoacao, 1000) AS VARCHAR(3900)) as ds_conclusaoacao, id_motivoresultado, ds_descricao, ic_confirmado, ds_temperatura, id_contato, ds_pendencia, no_versao, cd_usuarioinc, cd_usuarioalt, dt_inc, dt_alt, ds_agendamento, ds_respostasmsmo, id_email, id_sms, no_latitude, no_longitude, ic_mobile', 'custom_sql': None},
    'syo_cidade': {'id_field': 'id_cidade', 'source': None, 'select': None, 'custom_sql': None},
    'syo_clientealteracao': {'id_field': 'id_clientealteracao', 'source': None, 'select': None, 'custom_sql': None},
    'syo_registrointerface': {'id_field': 'id_registrointerface', 'source': None, 'select': None, 'custom_sql': None},    
    'syo_clientenovaclassificacao': {'id_field': 'id_novaclassificacao', 'source': None, 'select': None, 'custom_sql': None},
    'syo_historicoetapafunilevento': {'id_field': 'id_historicoetapafunilevento', 'source': None, 'select': None, 'custom_sql': None},
    'syo_encaminhamento': {
        'id_field': 'id_encaminhamento,id_evento', # Chave composta
        'source': None, 
        'select': None, 
        'custom_sql': {'incremental':"""
                DELETE FROM dbo.syo_encaminhamento 
                                            WHERE id_evento in
                                            (select distinct id_evento from OPENQUERY(POSTGRES,
                                            '
                                            with
                                            base as (
                                                select id_evento from public.syo_encaminhamento
                                                where (TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days''
                                                OR
                                                TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days'')

                                            )

                                            select e.* from public.syo_encaminhamento e
                                            inner join base b on e.id_evento = b.id_evento
                                            ')) 

                                            INSERT INTO dbo.syo_encaminhamento
                                            select * from OPENQUERY(POSTGRES,
                                            '
                                            with
                                            base as (
                                                select distinct id_evento from public.syo_encaminhamento
                                                where (TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days''
                                                OR
                                                TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days'')

                                            )

                                            select e.* from public.syo_encaminhamento e
                                            inner join base b on e.id_evento = b.id_evento
                                            ')   
        """}
        },
    'syo_empresacliente': {
        'id_field': 'id_cliente,id_empresa',  # Chave composta
        'source': None, 
        'select': None, 
        'custom_sql': {
            'incremental': """
                DELETE FROM dbo.syo_empresacliente 
                WHERE cast(id_cliente as varchar(30)) + '-' + cast(id_empresa as varchar(30)) 
                            IN (SELECT *
                                FROM OPENQUERY(POSTGRES, 'SELECT cast(id_cliente as varchar(30)) || ''-'' ||cast(id_empresa as varchar(30)) as cliente_empresa
                                                            FROM PUBLIC.syo_empresacliente 
                                                            WHERE (dt_inc is not null  OR dt_alt is not null)
                                                            AND {DAILY_FILTER_CONDITION}'))
                
                INSERT INTO dbo.syo_empresacliente
                SELECT *
                FROM OPENQUERY(POSTGRES, 'SELECT *
                                            FROM PUBLIC.syo_empresacliente 
                                            WHERE (dt_inc is not null  OR dt_alt is not null)
                                                AND {DAILY_FILTER_CONDITION}')
            """
        }
    },
    'syo_donocontacliente': {'id_field': 'id_donocontacliente', 'source': None, 'select': None, 'custom_sql': None},
    'syo_camposregistrointerface': {'id_field': 'id_camposregistrointerface', 'source': None, 'select': 'id_camposregistrointerface, id_campointerfacenegociacao, id_registrointerface, ds_etiqueta, CAST(LEFT(ds_valor, 1000) AS VARCHAR(3900)) as ds_valor, no_versao, cd_usuarioinc, cd_usuarioalt, dt_inc, dt_alt', 'custom_sql': None},
    'syo_peca': {'id_field': 'no_controle', 'source': None, 'select': None, 'custom_sql': None},
    'syo_dadosinterfacecliente': {'id_field': None, 'source': None, 'select': None, 'custom_sql': None},
    
    # Tabela customizada (SQL complexo próprio)
    'syo_evento_obs': {
        'id_field': 'id_evento', 
        'source': 'syo_evento', 
        'select': None, 
        'custom_sql': {
            'full': """
                IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = N'syo_evento_obs')
                    DROP TABLE syo_evento_obs
                    
                SELECT id_evento, dt_inc,
                       SUBSTRING(ds_observacao, 
                                CHARINDEX('Cadencia Meetime: ', ds_observacao), 
                                CHARINDEX('Cargo:', ds_observacao) - (CHARINDEX('Cadencia Meetime: ', ds_observacao) + LEN('Cadencia Meetime: ')+1) + 16) AS ds_observacao
                INTO syo_evento_obs 
                FROM OPENQUERY(POSTGRES, 'SELECT id_evento, dt_inc, ds_observacao
                                          FROM public.syo_evento
                                          WHERE ds_observacao LIKE ''%Cadencia Meetime:%''')
            """,
            'incremental': """
                DELETE FROM syo_evento_obs
                WHERE id_evento IN (SELECT id_evento FROM OPENQUERY (POSTGRES, 'SELECT id_evento
                                                    from syo_evento 
                                                    where {DAILY_FILTER_CONDITION}'))

                INSERT INTO dbo.syo_evento_obs (id_evento, dt_inc, ds_observacao)
                SELECT id_evento, 
                       dt_inc,
                       SUBSTRING(ds_observacao, 
                                CHARINDEX('Cadencia Meetime: ', ds_observacao), 
                                CHARINDEX('Cargo:', ds_observacao) - (CHARINDEX('Cadencia Meetime: ', ds_observacao) + LEN('Cadencia Meetime: ')+1) + 16) AS ds_observacao
                FROM OPENQUERY(POSTGRES, 
                    'SELECT id_evento, dt_inc, ds_observacao
                     from public.syo_evento
                     where {DAILY_FILTER_CONDITION}
                           and ds_observacao like ''%Cadencia Meetime:%''')
            """
        }
    }
}

@contextmanager
def get_db_cursor():
    """Context manager para gerenciar conexões de forma otimizada"""
    conn = None
    try:
        conn = pymssql.connect(**DB_CONFIG)
        cursor = conn.cursor()
        yield cursor, conn
    finally:
        if conn:
            conn.close()

def execute_sql_with_timeout(sql_query, task_name, timeout_seconds):
    """Executa SQL com timeout usando threading"""
    result = {'success': False, 'error': None}
    
    def execute_query():
        try:
            with get_db_cursor() as (cursor, conn):
                cursor.execute(sql_query)   
                conn.commit()
                result['success'] = True
        except Exception as e:
            result['error'] = str(e)
    
    # Inicia thread da query
    thread = threading.Thread(target=execute_query)
    thread.daemon = True
    thread.start()
    
    # Aguarda conclusão ou timeout
    thread.join(timeout_seconds)
    
    if thread.is_alive():
        # Timeout ocorreu
        raise TimeoutError(f"Query {task_name} excedeu timeout de {timeout_seconds}s")
    
    if not result['success']:
        raise Exception(result['error'] or "Erro desconhecido na execução")

def execute_sql(sql_query, task_name, timeout_seconds=None):
    """Executa query SQL com logging otimizado e timeout opcional"""
    try:
        print(f"\n===== Iniciando {task_name} =====")
        if timeout_seconds:
            print(f"⏱️ Timeout configurado: {timeout_seconds} segundos")
            
        logging.info(f"Executando {task_name}")
        # print(f"--- SQL ({task_name}) ---\n{sql_query[:500]}{'...' if len(sql_query) > 500 else ''}\n--- END ---")
        print(f"--- SQL ({task_name}) ---\n{sql_query}\n--- END ---")
        
        if timeout_seconds:
            # Usa execução com timeout
            execute_sql_with_timeout(sql_query, task_name, timeout_seconds)
        else:
            # Execução normal sem timeout
            with get_db_cursor() as (cursor, conn):
                cursor.execute(sql_query)   
                conn.commit()
                
        logging.info(f"{task_name} executado com sucesso")
        print(f"✓ {task_name} concluído\n")
        
    except TimeoutError as te:
        timeout_error = f"TIMEOUT ({timeout_seconds}s): {task_name} excedeu o limite de tempo"
        logging.error(timeout_error)
        print(f"⏱️ {timeout_error}\n")
        raise TimeoutError(timeout_error)
    except Exception as e:
        error_msg = str(e)
        logging.error(f"Erro em {task_name}: {error_msg}")
        print(f"✗ Erro em {task_name}: {error_msg}\n")
        raise

def build_full_load_query(table_name, source_table=None, special_select=None):
    """Constrói query para full load (DROP/CREATE)"""
    source = source_table if source_table else table_name
    select_clause = special_select if special_select else "*"
    
    return f"""
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = N'{table_name}')
        DROP TABLE {table_name}
    SELECT {select_clause} INTO {table_name} 
    FROM OPENQUERY(POSTGRES, 'SELECT {select_clause} FROM PUBLIC.{source}')
    """

def build_incremental_query(table_name, id_field, special_select=None):
    """
    Constrói query para carga incremental (DELETE/INSERT)
    Suporta chaves simples e compostas (separadas por vírgula)
    """
    select_clause = special_select if special_select else "*"
    
    # Verifica se é chave composta (contém vírgula)
    if ',' in id_field:
        # Chave composta - usa concatenação para comparação
        id_fields = [field.strip() for field in id_field.split(',')]
        
        # Constrói concatenação para DELETE
        destino_concat = " + '-' + ".join([f"CAST({field} AS VARCHAR(50))" for field in id_fields])
        origem_concat = " + '-' + ".join([f"CAST({field} AS VARCHAR(50))" for field in id_fields])
        
        return f"""
        DELETE FROM dbo.{table_name}
        WHERE {destino_concat} IN (
            SELECT {origem_concat}
            FROM OPENQUERY(POSTGRES, 'SELECT {id_field} FROM PUBLIC.{table_name} WHERE {DAILY_FILTER_CONDITION}')
        )
        
        INSERT INTO dbo.{table_name}
        SELECT {select_clause}
        FROM OPENQUERY(POSTGRES, 'SELECT {select_clause} FROM PUBLIC.{table_name} WHERE {DAILY_FILTER_CONDITION}')
        """
    else:
        # Chave simples - lógica original
        return f"""
        DELETE FROM dbo.{table_name}
        WHERE {id_field} IN (
            SELECT {id_field}
            FROM OPENQUERY(POSTGRES, 'SELECT {id_field} FROM PUBLIC.{table_name} WHERE {DAILY_FILTER_CONDITION}')
        )
        
        INSERT INTO dbo.{table_name}
        SELECT {select_clause}
        FROM OPENQUERY(POSTGRES, 'SELECT {select_clause} FROM PUBLIC.{table_name} WHERE {DAILY_FILTER_CONDITION}')
        """

def build_seven_days_query(table_name, id_field, source_table=None, special_select=None):
    """
    Constrói query para carga incremental de 7 dias - DELETE/INSERT dos últimos 7 dias
    ESTRATÉGIA EFICIENTE: Muito mais rápido que gap filling em tabelas grandes
    Suporta chaves simples e compostas (separadas por vírgula)
    """
    source = source_table if source_table else table_name
    select_clause = special_select if special_select else "*"
    
    # Verifica se é chave composta (contém vírgula)
    if ',' in id_field:
        # Chave composta - usa concatenação para comparação
        id_fields = [field.strip() for field in id_field.split(',')]
        
        # Constrói concatenação para DELETE (destino)
        destino_concat = " + '-' + ".join([f"CAST({field} AS VARCHAR(50))" for field in id_fields])
        
        return f"""
        DELETE FROM dbo.{table_name}
        WHERE {destino_concat} IN (
            SELECT {" + '-' + ".join([f"CAST({field} AS VARCHAR(50))" for field in id_fields])}
            FROM OPENQUERY(POSTGRES, 'SELECT {id_field} FROM PUBLIC.{source} WHERE {SEVEN_DAYS_FILTER_CONDITION}')
        )
        
        INSERT INTO dbo.{table_name}
        SELECT {select_clause}
        FROM OPENQUERY(POSTGRES, 'SELECT {select_clause} FROM PUBLIC.{source} WHERE {SEVEN_DAYS_FILTER_CONDITION}')
        """
    else:
        # Chave simples - DELETE/INSERT direto
        return f"""
        DELETE FROM dbo.{table_name}
        WHERE {id_field} IN (
            SELECT {id_field}
            FROM OPENQUERY(POSTGRES, 'SELECT {id_field} FROM PUBLIC.{source} WHERE {SEVEN_DAYS_FILTER_CONDITION}')
        )
        
        INSERT INTO dbo.{table_name}
        SELECT {select_clause}
        FROM OPENQUERY(POSTGRES, 'SELECT {select_clause} FROM PUBLIC.{source} WHERE {SEVEN_DAYS_FILTER_CONDITION}')
        """

def validate_single_table(table_name, source_table=None, task_name=None):
    """
    Valida uma única tabela imediatamente após processamento
    Retorna: (is_valid, analysis_info) onde analysis_info = (should_gap_fill, source_count, dest_count, diff_percentage)
    """
    try:
        with get_db_cursor() as (cursor, conn):
            # Conta registros no destino
            cursor.execute(f"SELECT COUNT(*) FROM dbo.{table_name}")
            result = cursor.fetchone()
            dest_count = result[0] if result else 0

            # Determina tabela de origem
            source = source_table if source_table else table_name
            
            # VALIDAÇÃO CUSTOMIZADA para syo_evento_obs
            if table_name == 'syo_evento_obs':
                # Conta registros na origem COM O FILTRO aplicado
                cursor.execute(f"SELECT cnt FROM OPENQUERY(POSTGRES, 'SELECT COUNT(*) AS cnt FROM PUBLIC.syo_evento WHERE ds_observacao LIKE ''%Cadencia Meetime:%''')")
                result = cursor.fetchone()
                source_count = result[0] if result else 0
            else:
                # Conta registros na origem (validação padrão)
                cursor.execute(f"SELECT cnt FROM OPENQUERY(POSTGRES, 'SELECT COUNT(*) AS cnt FROM PUBLIC.{source}')")
                result = cursor.fetchone()
                source_count = result[0] if result else 0

            # Calcula diferenças
            abs_diff = abs(dest_count - source_count)
            diff_percentage = (abs_diff / source_count * 100) if source_count > 0 else 0
            
            # Análise para incremental 7 dias (informação extra para fallback)
            should_use_seven_days = (
                source_count > 0 and  
                dest_count < source_count and  
                diff_percentage <= MAX_GAP_PERCENTAGE
            )
            
            analysis_info = (should_use_seven_days, source_count, dest_count, diff_percentage)
            
            task_label = task_name or table_name
            print(f"🔍 Validação imediata {task_label}: origem={source_count:,}, destino={dest_count:,}")

            # Verifica tolerâncias
            if abs_diff > MAX_ABS_DIFF:
                print(f"❌ Divergência em {table_name}: origem={source_count:,}, destino={dest_count:,}, diff={abs_diff:,} ({diff_percentage:.1f}%)")
                print(f"📊 Recomendação para fallback: {'Incremental 7 Dias' if should_use_seven_days else 'Full Load'} (limite: {MAX_GAP_PERCENTAGE}%)")
                return False, analysis_info
            
            print(f"✅ {task_label} validado com sucesso")
            return True, analysis_info
                    
    except Exception as e:
        error_msg = f"Erro na validação de {table_name}: {str(e)}"
        print(f"❌ {error_msg}")
        return False, (False, 0, 0, 0)

def process_tb_oportunidades_base(**context):
    """Gera tb_oportunidades_base replicando 100% da lógica da view bqvw_IM_Oportunidades_original."""
    sql = r"""
    IF OBJECT_ID('dbo.tb_oportunidades_base','U') IS NOT NULL
        DROP TABLE dbo.tb_oportunidades_base;

    /* =====================================================================
       CTEs para evitar subconsultas correlacionadas e consolidar 20+ joins
    ======================================================================*/
    WITH evt_base AS (
        SELECT *
        FROM syo_evento WITH (NOLOCK)
        WHERE id_tipoevento IN ('DVM LEAD','DVM OPORTUNIDADE','DVM LEAD RELACIONAMENTO','DVM RELACIONAMENTO','DVM LICITACAO')
          AND CAST(DATEADD(minute, dt_inc/60000,'1970-01-01') AS date) >= '2023-01-01'
    ),
    acao_last AS (
        SELECT id_evento, dt_alt, id_motivoresultado,
               ROW_NUMBER() OVER(PARTITION BY id_evento ORDER BY id_acao DESC) AS rn
        FROM syo_acao WITH (NOLOCK)
        WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
    ),
    acao_first AS (
        SELECT id_evento, dt_alt,
               ROW_NUMBER() OVER(PARTITION BY id_evento ORDER BY id_acao) AS rn
        FROM syo_acao WITH (NOLOCK)
        WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
    ),
    ri_last AS (
        SELECT id_evento, id_registrointerface,
               ROW_NUMBER() OVER(PARTITION BY id_evento ORDER BY id_registrointerface DESC) AS rn
        FROM syo_registrointerface WITH (NOLOCK)
        WHERE COALESCE(ic_comentario,'0')='0'
          AND COALESCE(ic_excluido,'0')='0'
          AND COALESCE(ic_editado,'0')='0'
    ),
    ri_pivot AS (
        SELECT id_registrointerface,
               MAX(CASE WHEN ds_etiqueta='_MODELO'  THEN ds_valor END) AS _MODELO,
               MAX(CASE WHEN ds_etiqueta='_MODELO2' THEN ds_valor END) AS _MODELO2,
               MAX(CASE WHEN ds_etiqueta='_MODELO3' THEN ds_valor END) AS _MODELO3,
               MAX(CASE WHEN ds_etiqueta='_MODELO4' THEN ds_valor END) AS _MODELO4,
               MAX(CASE WHEN ds_etiqueta='_MODELO5' THEN ds_valor END) AS _MODELO5,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO'  THEN ds_valor END) AS VALOR1,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 2' THEN ds_valor END) AS VALOR2,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 3' THEN ds_valor END) AS VALOR3,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 4' THEN ds_valor END) AS VALOR4,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 5' THEN ds_valor END) AS VALOR5,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE'  THEN ds_valor END) AS QTD1,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE2' THEN ds_valor END) AS QTD2,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE3' THEN ds_valor END) AS QTD3,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE4' THEN ds_valor END) AS QTD4,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE5' THEN ds_valor END) AS QTD5,
               MAX(CASE WHEN ds_etiqueta='_VERSAO'  THEN ds_valor END) AS _VERSAO,
               MAX(CASE WHEN ds_etiqueta='_VERSAO2' THEN ds_valor END) AS _VERSAO2,
               MAX(CASE WHEN ds_etiqueta='_VERSAO3' THEN ds_valor END) AS _VERSAO3,
               MAX(CASE WHEN ds_etiqueta='_VERSAO4' THEN ds_valor END) AS _VERSAO4,
               MAX(CASE WHEN ds_etiqueta='_VERSAO5' THEN ds_valor END) AS _VERSAO5,
               MAX(CASE WHEN ds_etiqueta='Previsão de Faturamento' THEN ds_valor END) AS PREV_FAT,
               MAX(CASE WHEN ds_etiqueta='Data do faturamento'     THEN ds_valor END) AS DT_FAT,
               MAX(CASE WHEN ds_etiqueta='FATURA ESSE MÊS?'        THEN ds_valor END) AS FATURA_MES
        FROM syo_camposregistrointerface WITH (NOLOCK)
        GROUP BY id_registrointerface
    ),
    hef_agg AS (
        SELECT id_evento,
               'MAQUINA FATURADA' AS Faturada,
               MIN(DATEADD(hour,-3,DATEADD(minute, dt_inc/60000,'1970-01-01'))) AS dt_inc
        FROM syo_historicoetapafunilevento WITH (NOLOCK)
        WHERE id_etapafunil IN (6,9,17,99,20,45,153,144,154)
        GROUP BY id_evento
    )
    SELECT
        EVT.id_cliente,
        CLI.nm_cliente         AS 'Cliente',
        CLI.no_cpfcnpj         AS 'CNPJ/CPF',
        COALESCE(CLI.nm_cidadecom, CLI.nm_cidaderes) AS 'Cidade',
        COALESCE(CLI.sg_ufcom,    CLI.sg_ufres)      AS 'UF',
        EVT.id_tipoevento,
        EMP.ap_empresa         AS 'Filial',
        EVT.id_evento          AS 'Nº Evento',
        EVT.ds_formacontato    AS 'Origem',
        EVT.ds_midia           AS 'Midia',
        EVT.ds_assunto         AS 'Assunto',
        DATEADD(hour,-3,DATEADD(minute, EVT.dt_conclusao/60000,'1970-01-01')) AS 'Data Fechamento',
        DATEADD(hour,-3,DATEADD(minute, EVT.dt_inc/60000,'1970-01-01'))       AS 'Data Inclusão',
        DATEADD(hour,-3,DATEADD(minute, al.dt_alt/60000,'1970-01-01'))        AS 'Data U.Alteração',
        DATEADD(hour,-3,DATEADD(minute, af.dt_alt/60000,'1970-01-01'))        AS 'Data P.Alteração',
        DATEADD(hour,-3,DATEADD(minute, EVT.dt_proximaacao/60000,'1970-01-01')) AS 'Data P.Acao',
        COALESCE(EVT.ds_resultado,EVT.id_statusevento) AS 'Status',
        rp._MODELO  AS 'Modelo Interesse 1',
        rp._MODELO2 AS 'Modelo Interesse 2',
        rp._MODELO3 AS 'Modelo Interesse 3',
        rp._MODELO4 AS 'Modelo Interesse 4',
        rp._MODELO5 AS 'Modelo Interesse 5',
        mv1.id_versao AS 'Máquina 1', mv2.id_versao AS 'Máquina 2', mv3.id_versao AS 'Máquina 3',
        mv4.id_versao AS 'Máquina 4', mv5.id_versao AS 'Máquina 5',
        rp.QTD1 AS 'Quantidade 1', rp.QTD2 AS 'Quantidade 2', rp.QTD3 AS 'Quantidade 3',
        rp.QTD4 AS 'Quantidade 4', rp.QTD5 AS 'Quantidade 5',
        STR(ETF.no_ordem)+'- '+ETF.nm_etapafunil AS 'Etapa Funil',
        CASE WHEN EVT.id_statusevento = 'ANDAMENTO' THEN MR.ds_motivo END AS 'Motivo de Andamento',
        CASE WHEN EVT.ds_resultado   = 'INSUCESSO'  THEN MR.ds_motivo END AS 'Motivo da Perda',
        USU.nm_login AS 'Usuário AJ',
        REPLACE(rp.VALOR1,'Não informado','0,00') AS 'Valor 1',
        REPLACE(rp.VALOR2,'Não informado','0,00') AS 'Valor 2',
        REPLACE(rp.VALOR3,'Não informado','0,00') AS 'Valor 3',
        REPLACE(rp.VALOR4,'Não informado','0,00') AS 'Valor 4',
        REPLACE(rp.VALOR5,'Não informado','0,00') AS 'Valor 5',
        DATEADD(hour,-3,DATEADD(minute, CONVERT(float,rp.PREV_FAT)/60000,'1970-01-01')) AS 'Previsão Faturamento',
        evt.ds_temperatura AS 'Temperatura',
        EVT.id_componente  AS 'Evento Anterior',
        hef_agg.Faturada,
        hef_agg.dt_inc        AS 'Data Etapa',
        DATEADD(hour,-3,DATEADD(minute, CONVERT(float,rp.DT_FAT)/60000,'1970-01-01')) AS 'Data do Faturamento',
        rp.FATURA_MES     AS 'Fatura esse mês ?'
    INTO tb_oportunidades_base
    FROM        evt_base EVT
    INNER JOIN  syo_encaminhamento ENC WITH (NOLOCK)
               ON ENC.id_evento = EVT.id_evento AND ENC.id_statusagenteativo = 'S'
    LEFT JOIN   syo_cliente CLI WITH (NOLOCK) ON CLI.id_cliente = EVT.id_cliente
    INNER JOIN  syo_usuario USU WITH (NOLOCK) ON USU.id_usuario = ENC.id_agente
    LEFT JOIN   syo_empresa EMP WITH (NOLOCK) ON EMP.id_empresa = USU.id_empresa
    LEFT JOIN   acao_last al  ON al.id_evento = EVT.id_evento AND al.rn = 1
    LEFT JOIN   acao_first af ON af.id_evento = EVT.id_evento AND af.rn = 1
    LEFT JOIN   syo_motivoresultado MR WITH (NOLOCK) ON al.id_motivoresultado = MR.id_motivoresultado
    LEFT JOIN   ri_last ril ON ril.id_evento = EVT.id_evento AND ril.rn = 1
    LEFT JOIN   ri_pivot rp ON rp.id_registrointerface = ril.id_registrointerface
    LEFT JOIN   syo_modeloversao mv1 ON mv1.id_modeloversao = rp._VERSAO
    LEFT JOIN   syo_modeloversao mv2 ON mv2.id_modeloversao = rp._VERSAO2
    LEFT JOIN   syo_modeloversao mv3 ON mv3.id_modeloversao = rp._VERSAO3
    LEFT JOIN   syo_modeloversao mv4 ON mv4.id_modeloversao = rp._VERSAO4
    LEFT JOIN   syo_modeloversao mv5 ON mv5.id_modeloversao = rp._VERSAO5
    LEFT JOIN   syo_historicoetapafunilevento HEF WITH (NOLOCK) ON EVT.id_evento = HEF.id_evento AND HEF.ic_etapaatual = 1
    LEFT JOIN   syo_etapafunil ETF WITH (NOLOCK) ON HEF.id_etapafunil = ETF.id_etapafunil
    LEFT JOIN   hef_agg ON hef_agg.id_evento = EVT.id_evento;
    """
    execute_sql(sql, "TB_OPORTUNIDADES_BASE")
    
    # Validação - tabela de negócio, sem comparação direta com origem
    print(f"🔍 Iniciando validação imediata de tb_oportunidades_base")
    try:
        with get_db_cursor() as (cursor, conn):
            cursor.execute("SELECT COUNT(*) FROM dbo.tb_oportunidades_base")
            result = cursor.fetchone()
            count = result[0] if result else 0
            
            print(f"📊 tb_oportunidades_base criada com {count} registros")
            
            if count == 0:
                print(f"⚠️ AVISO: tb_oportunidades_base está vazia - verificar filtros e dependências")
            else:
                print(f"✅ tb_oportunidades_base validada com sucesso")
                
    except Exception as e:
        error_msg = f"Erro na validação de tb_oportunidades_base: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

def process_tb_im_assinatura_original(**context):
    """Gera tb_IM_Assinatura_original replicando 100% da lógica da view bqvw_IM_Assinatura, de forma otimizada com CTEs e PIVOT."""
    sql = r"""
    IF OBJECT_ID('dbo.tb_IM_Assinatura_original','U') IS NOT NULL
        DROP TABLE dbo.tb_IM_Assinatura_original;

    /* =====================================================================
       CTEs para reduzir 25+ joins e eliminar subconsultas correlacionadas
    ======================================================================*/
    WITH evt_base AS (
        SELECT *
        FROM syo_evento WITH (NOLOCK)
        WHERE id_tipoevento IN ('INDICACAO ASSINATURA','DVA ASSINATURA','PRECIFICACAO ASSINATURA',
                                'DVA LEAD RELACIONAMENTO','DVA RELACIONAMENTO',
                                'PRECIFICACAO AVANT','DVA ASSINATURA AVANT')
          AND id_statusevento <> 'CANCELADO'
          AND CAST(DATEADD(minute, dt_inc/60000,'1970-01-01') AS date) > '2023-12-31'
    ),
    acao_last AS (
        SELECT id_evento, dt_alt, id_motivoresultado,
               ROW_NUMBER() OVER(PARTITION BY id_evento ORDER BY id_acao DESC) AS rn
        FROM syo_acao WITH (NOLOCK)
        WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
    ),
    acao_first AS (
        SELECT id_evento, dt_alt,
               ROW_NUMBER() OVER(PARTITION BY id_evento ORDER BY id_acao) AS rn
        FROM syo_acao WITH (NOLOCK)
        WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
    ),
    ri_last AS (
        SELECT id_evento, id_registrointerface,
               ROW_NUMBER() OVER(PARTITION BY id_evento ORDER BY id_registrointerface DESC) AS rn
        FROM syo_registrointerface WITH (NOLOCK)
        WHERE COALESCE(ic_comentario,'0')='0'
          AND COALESCE(ic_excluido,'0')='0'
          AND COALESCE(ic_editado,'0')='0'
    ),
    ri_pivot AS (
        SELECT id_registrointerface,
               MAX(CASE WHEN ds_etiqueta='_MODELO'  THEN ds_valor END)  AS _MODELO,
               MAX(CASE WHEN ds_etiqueta='_MODELO2' THEN ds_valor END) AS _MODELO2,
               MAX(CASE WHEN ds_etiqueta='_MODELO3' THEN ds_valor END) AS _MODELO3,
               MAX(CASE WHEN ds_etiqueta='_MODELO4' THEN ds_valor END) AS _MODELO4,
               MAX(CASE WHEN ds_etiqueta='_MODELO5' THEN ds_valor END) AS _MODELO5,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE'  THEN ds_valor END) AS QTD1,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE2' THEN ds_valor END) AS QTD2,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE3' THEN ds_valor END) AS QTD3,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE4' THEN ds_valor END) AS QTD4,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE5' THEN ds_valor END) AS QTD5,
               MAX(CASE WHEN ds_etiqueta='_VERSAO'  THEN ds_valor END) AS _VERSAO,
               MAX(CASE WHEN ds_etiqueta='_VERSAO2' THEN ds_valor END) AS _VERSAO2,
               MAX(CASE WHEN ds_etiqueta='_VERSAO3' THEN ds_valor END) AS _VERSAO3,
               MAX(CASE WHEN ds_etiqueta='_VERSAO4' THEN ds_valor END) AS _VERSAO4,
               MAX(CASE WHEN ds_etiqueta='_VERSAO5' THEN ds_valor END) AS _VERSAO5,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO'  THEN ds_valor END) AS VALOR1,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO2' THEN ds_valor END) AS VALOR2,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO3' THEN ds_valor END) AS VALOR3,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO4' THEN ds_valor END) AS VALOR4,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO5' THEN ds_valor END) AS VALOR5
        FROM syo_camposregistrointerface WITH (NOLOCK)
        GROUP BY id_registrointerface
    )
    SELECT
        EVT.id_cliente                               AS 'id_cliente',
        CLI.nm_cliente                               AS 'Cliente',
        CLI.no_cpfcnpj                               AS 'CNPJ/CPF',
        EVT.id_tipoevento                            AS 'Tipo Evento',
        EMP.ap_empresa                               AS 'Filial',
        EVT.id_evento                                AS 'Nº Evento',
        EVT.ds_formacontato                          AS 'Origem',
        rp._MODELO                                   AS 'Modelo Interesse 1',
        mv1.id_versao                                AS 'Máquina 1',
        rp.QTD1                                      AS 'Quantidade 1',
        REPLACE(rp.VALOR1,'Não informado','0,00')    AS 'Valor 1',
        rp._MODELO2                                  AS 'Modelo Interesse 2',
        mv2.id_versao                                AS 'Máquina 2',
        rp.QTD2                                      AS 'Quantidade 2',
        REPLACE(rp.VALOR2,'Não informado','0,00')    AS 'Valor 2',
        rp._MODELO3                                  AS 'Modelo Interesse 3',
        mv3.id_versao                                AS 'Máquina 3',
        rp.QTD3                                      AS 'Quantidade 3',
        REPLACE(rp.VALOR3,'Não informado','0,00')    AS 'Valor 3',
        rp._MODELO4                                  AS 'Modelo Interesse 4',
        mv4.id_versao                                AS 'Máquina 4',
        rp.QTD4                                      AS 'Quantidade 4',
        REPLACE(rp.VALOR4,'Não informado','0,00')    AS 'Valor 4',
        rp._MODELO5                                  AS 'Modelo Interesse 5',
        mv5.id_versao                                AS 'Máquina 5',
        rp.QTD5                                      AS 'Quantidade 5',
        REPLACE(rp.VALOR5,'Não informado','0,00')    AS 'Valor 5',
        EVT.ds_assunto                               AS 'Assunto',
        DATEADD(hour,-3,DATEADD(minute, EVT.dt_conclusao/60000,'1970-01-01')) AS 'Data Fechamento',
        DATEADD(hour,-3,DATEADD(minute, EVT.dt_inc/60000,'1970-01-01'))        AS 'Data Inclusão',
        DATEADD(hour,-3,DATEADD(minute, al.dt_alt/60000,'1970-01-01'))         AS 'Data U.Alteração',
        DATEADD(hour,-3,DATEADD(minute, af.dt_alt/60000,'1970-01-01'))         AS 'Data P.Alteração',
        DATEADD(hour,-3,DATEADD(minute, EVT.dt_proximaacao/60000,'1970-01-01')) AS 'Data P.Acao',
        COALESCE(EVT.ds_resultado,EVT.id_statusevento) AS 'Status',
        STR(ETF.no_ordem)+'- '+ETF.nm_etapafunil       AS 'Etapa Funil',
        CASE WHEN EVT.id_statusevento='ANDAMENTO' THEN MR.ds_motivo END AS 'Motivo de Andamento',
        CASE WHEN EVT.ds_resultado='INSUCESSO' THEN MR.ds_motivo END    AS 'Motivo da Perda',
        USU.nm_login                                   AS 'Vendedor',
        EVT.ds_temperatura                             AS 'Temperatura',
        EVT1.id_tipoevento                             AS 'Tipo Evento Anterior',
        EVT1.cd_usuarioinc                             AS 'Indicante Evento Anterior'
    INTO tb_IM_Assinatura_original
    FROM evt_base EVT
    INNER JOIN syo_encaminhamento ENC WITH (NOLOCK)
            ON ENC.id_evento = EVT.id_evento AND ENC.id_statusagenteativo = 'S'
    LEFT JOIN syo_cliente CLI WITH (NOLOCK) ON CLI.id_cliente = EVT.id_cliente
    INNER JOIN syo_empresa EMP WITH (NOLOCK) ON EMP.id_empresa = ENC.id_empresa
    INNER JOIN syo_usuario USU WITH (NOLOCK) ON USU.id_usuario = ENC.id_agente
    LEFT JOIN acao_last al  ON al.id_evento = EVT.id_evento AND al.rn = 1
    LEFT JOIN acao_first af ON af.id_evento = EVT.id_evento AND af.rn = 1
    LEFT JOIN syo_motivoresultado MR WITH (NOLOCK) ON al.id_motivoresultado = MR.id_motivoresultado
    LEFT JOIN ri_last ril ON ril.id_evento = EVT.id_evento AND ril.rn = 1
    LEFT JOIN ri_pivot rp ON rp.id_registrointerface = ril.id_registrointerface
    LEFT JOIN syo_modeloversao mv1 ON mv1.id_modeloversao = rp._VERSAO
    LEFT JOIN syo_modeloversao mv2 ON mv2.id_modeloversao = rp._VERSAO2
    LEFT JOIN syo_modeloversao mv3 ON mv3.id_modeloversao = rp._VERSAO3
    LEFT JOIN syo_modeloversao mv4 ON mv4.id_modeloversao = rp._VERSAO4
    LEFT JOIN syo_modeloversao mv5 ON mv5.id_modeloversao = rp._VERSAO5
    LEFT JOIN syo_historicoetapafunilevento HEF WITH (NOLOCK) ON EVT.id_evento = HEF.id_evento AND HEF.ic_etapaatual = 1
    LEFT JOIN syo_etapafunil ETF WITH (NOLOCK) ON HEF.id_etapafunil = ETF.id_etapafunil
    LEFT JOIN syo_evento EVT1 ON EVT1.id_evento = EVT.id_componente;
    """
    execute_sql(sql, "TB_IM_ASSINATURA_ORIGINAL")
    
    # Validação - tabela de negócio, sem comparação direta com origem
    print(f"🔍 Iniciando validação imediata de tb_IM_Assinatura_original")
    try:
        with get_db_cursor() as (cursor, conn):
            cursor.execute("SELECT COUNT(*) FROM dbo.tb_IM_Assinatura_original")
            result = cursor.fetchone()
            count = result[0] if result else 0
            
            print(f"📊 tb_IM_Assinatura_original criada com {count} registros")
            
            if count == 0:
                print(f"⚠️ AVISO: tb_IM_Assinatura_original está vazia - verificar filtros e dependências")
            else:
                print(f"✅ tb_IM_Assinatura_original validada com sucesso")
                
    except Exception as e:
        error_msg = f"Erro na validação de tb_IM_Assinatura_original: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

def process_tb_maquinas_semana_passada(**context):
    """Processa tb_maquinas_semana_passada APENAS nas sextas-feiras"""
    if not datetime.now().weekday() == 4: # 4 = sexta-feira
        print("✓ Não é sexta-feira - pulando tb_maquinas_semana_passada")
        return
        
    sql = """
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = N'tb_maquinas_semana_passada')
        DROP TABLE tb_maquinas_semana_passada

    SELECT [Cliente], [CNPJ/CPF], [id_tipoevento], [Filial], [Nº Evento], 
           [Data Fechamento], [Data Inclusão], [Data U.Alteração], [Data P.Alteração], 
           [Status], [Modelo Interesse 1], [Modelo Interesse 2], [Modelo Interesse 3], 
           [Modelo Interesse 4], [Modelo Interesse 5], [Etapa Funil], 
           [Motivo de Andamento], [Motivo da Perda], [Usuário AJ], 
           [Valor 1], [Valor 2], [Valor 3], [Valor 4], [Valor 5], 
           [Temperatura], [Evento Anterior]
    INTO tb_maquinas_semana_passada
    FROM [dbo].[tb_oportunidades_base]
    """
    
    print("🗓️ SEXTA-FEIRA - Processando tb_maquinas_semana_passada")
    execute_sql(sql, "TB_MAQUINAS_SEMANA_PASSADA")
    
    # Validação baseada na tabela de origem
    print(f"🔍 Iniciando validação imediata de tb_maquinas_semana_passada")
    try:
        with get_db_cursor() as (cursor, conn):
            # Conta registros na semana passada
            cursor.execute("SELECT COUNT(*) FROM dbo.tb_maquinas_semana_passada")
            result = cursor.fetchone()
            week_count = result[0] if result else 0
            
            # Conta registros na tabela base
            cursor.execute("SELECT COUNT(*) FROM dbo.tb_oportunidades_base")
            result = cursor.fetchone()
            base_count = result[0] if result else 0
            
            print(f"📊 tb_maquinas_semana_passada: {week_count} registros (base: {base_count})")
            
            if week_count != base_count:
                print(f"⚠️ AVISO: Diferença entre semana passada ({week_count}) e base ({base_count})")
            else:
                print(f"✅ tb_maquinas_semana_passada validada com sucesso")
                
    except Exception as e:
        error_msg = f"Erro na validação de tb_maquinas_semana_passada: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

def process_tb_maquinas_atual(**context):
    """Processa tb_maquinas_atual - executa sempre"""
    sql = """
    IF EXISTS (SELECT * FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_NAME = N'tb_maquinas_atual')
        DROP TABLE tb_maquinas_atual

    SELECT * INTO tb_maquinas_atual FROM [dbo].[tb_oportunidades_base]
    """
    
    print("📊 Processando tb_maquinas_atual")
    execute_sql(sql, "TB_MAQUINAS_ATUAL")
    
    # Validação baseada na tabela de origem
    print(f"🔍 Iniciando validação imediata de tb_maquinas_atual")
    try:
        with get_db_cursor() as (cursor, conn):
            # Conta registros na atual
            cursor.execute("SELECT COUNT(*) FROM dbo.tb_maquinas_atual")
            result = cursor.fetchone()
            atual_count = result[0] if result else 0
            
            # Conta registros na tabela base
            cursor.execute("SELECT COUNT(*) FROM dbo.tb_oportunidades_base")
            result = cursor.fetchone()
            base_count = result[0] if result else 0
            
            print(f"📊 tb_maquinas_atual: {atual_count} registros (base: {base_count})")
            
            if atual_count != base_count:
                error_msg = f"Divergência entre atual ({atual_count}) e base ({base_count})"
                print(f"❌ {error_msg}")
                raise ValueError(error_msg)
            else:
                print(f"✅ tb_maquinas_atual validada com sucesso")
                
    except Exception as e:
        error_msg = f"Erro na validação de tb_maquinas_atual: {str(e)}"
        print(f"❌ {error_msg}")
        raise RuntimeError(error_msg)

def process_table_etl(table_name, load_mode, id_field=None, source_table=None, special_select=None, custom_sql=None, **context):
    """
    Processa ETL de uma tabela com o modo especificado
    """
    try:
        source_table = source_table or table_name
        
        # ESTRATÉGIA 1: SQL Customizado (para tabelas especiais)
        if custom_sql:
            print(f"   🔄 Executando SQL customizado para {table_name}")
            
            # Tenta usar SQL customizado para o modo solicitado
            if load_mode == 'full' and 'full' in custom_sql:
                sql = custom_sql['full']
                execute_sql(sql, f"{table_name.upper()}_CUSTOM_FULL")
                is_valid, analysis_info = validate_single_table(table_name, source_table, f"{table_name.upper()}_CUSTOM_FULL")
                return is_valid, analysis_info
                
            elif load_mode == 'incremental' and 'incremental' in custom_sql:
                sql = custom_sql['incremental'].format(DAILY_FILTER_CONDITION=DAILY_FILTER_CONDITION)
                execute_sql(sql, f"{table_name.upper()}_CUSTOM_INCREMENTAL")
                is_valid, analysis_info = validate_single_table(table_name, source_table, f"{table_name.upper()}_CUSTOM_INCREMENTAL")
                return is_valid, analysis_info
                
            else:
                # Fallback para SQL padrão se não tiver o modo customizado
                print(f"   ⚠️ SQL customizado não disponível para modo {load_mode}, usando SQL padrão")
                 
        # ESTRATÉGIA 2: SQL Padrão (tabelas normais)
        if load_mode == 'full':
            print(f"   🔄 Executando Full Load para {table_name}")
            sql = build_full_load_query(table_name, source_table, special_select)
            execute_sql(sql, f"{table_name.upper()}_FULL")
            is_valid, analysis_info = validate_single_table(table_name, source_table, f"{table_name.upper()}_FULL")
            return is_valid, analysis_info
            
        elif load_mode == 'incremental':
            print(f"   🔄 Executando Incremental para {table_name}")
            sql = build_incremental_query(table_name, id_field, special_select)
            execute_sql(sql, f"{table_name.upper()}_INCREMENTAL")
            is_valid, analysis_info = validate_single_table(table_name, source_table, f"{table_name.upper()}_INCREMENTAL")
            return is_valid, analysis_info
            
        elif load_mode == 'seven_days':
            print(f"   🔄 Executando Incremental 7 Dias para {table_name}")
            sql = build_seven_days_query(table_name, id_field, source_table, special_select)
            try:
                execute_sql(sql, f"{table_name.upper()}_SEVEN_DAYS", timeout_seconds=SEVEN_DAYS_TIMEOUT)
                is_valid, analysis_info = validate_single_table(table_name, source_table, f"{table_name.upper()}_SEVEN_DAYS")
                return is_valid, analysis_info
            except TimeoutError as te:
                print(f"   ⏱️ Incremental 7 Dias timeout para {table_name} - será feito Full Load")
                return False, (False, 0, 0, 100)  # Força fallback para full load
            
        else:
            raise ValueError(f"Modo de carga inválido: {load_mode}")
            
    except Exception as e:
        print(f"   ❌ Erro no processamento {load_mode} de {table_name}: {str(e)}")
        return False

def process_table_with_smart_fallback(table_name, id_field=None, source_table=None, special_select=None, **context):
    """
    Processa tabela com fallback automático:
    1. Verifica configuração da tabela
    2. Tenta incremental (se possível)
    3. Valida resultado
    4. Se falhar na validação -> executa full load
    5. Valida novamente
    """
    
    # Pega configuração da tabela
    table_config = TABLES_CONFIG.get(table_name, {})
    
    # Parâmetros da tabela (prioriza parâmetros passados, depois config)
    final_id_field = id_field or table_config.get('id_field')
    final_source_table = source_table or table_config.get('source') or table_name
    final_special_select = special_select or table_config.get('select')
    final_custom_sql = table_config.get('custom_sql')
    incremental_failed = False
    incremental_custom_failed = False
    
    print(f"🚀 SMART FALLBACK: {table_name}")
    print(f"   📊 Configuração: {'SQL Customizado' if final_custom_sql else 'Incremental + Fallback'}")
    print(f"   🔑 ID Field: {final_id_field}")
    print(f"   📂 Source: {final_source_table}")
    print(f"   🔍 Select: {'Customizado' if final_special_select else 'Padrão'}")
    
    # ESTRATÉGIA 1: Tabelas com SQL customizado - Fallback com gap filling
    if final_custom_sql:
        print(f"   ⚡ Estratégia: SQL Customizado → Gap Filling → Full Load")
        
        # NÍVEL 1: Tenta incremental customizado primeiro (se disponível)
        if 'incremental' in final_custom_sql and final_id_field:
            print(f"   🔄 NÍVEL 1: Tentando incremental customizado (diário)...")
            success, analysis_info = process_table_etl(
                table_name=table_name,
                load_mode='incremental',
                id_field=final_id_field,
                source_table=final_source_table,
                special_select=final_special_select,
                custom_sql=final_custom_sql,
                **context
            )
            
            if success:
                print(f"   ✅ Incremental customizado executado com sucesso!")
                log_fallback_metrics(table_name, 'incremental_custom', 'incremental_custom', **context)
                return True
            else:
                incremental_custom_failed = True
                print(f"   ⚠️ Incremental customizado falhou, analisando estratégia...")
                
                # NÍVEL 2: Usa dados da validação para decidir Incremental 7 Dias vs Full Load
                should_use_seven_days, source_count, dest_count, diff_percentage = analysis_info
                print(f"   📊 Análise: origem={source_count:,}, destino={dest_count:,}, diferença={diff_percentage:.1f}%")
                
                if should_use_seven_days:
                    # NÍVEL 2A: Incremental 7 Dias padrão (diferença ≤ 10%)
                    print(f"   🎯 Incremental 7 Dias: diferença {diff_percentage:.1f}% ≤ {MAX_GAP_PERCENTAGE}% (apenas {source_count - dest_count:,} registros)")
                    success, _ = process_table_etl(
                        table_name=table_name,
                        load_mode='seven_days',
                        id_field=final_id_field,
                        source_table=final_source_table,
                        special_select=final_special_select,
                        custom_sql=None,  # Usa SQL padrão para 7 dias
                        **context
                    )
                    
                    if success:
                        print(f"   ✅ Incremental 7 Dias executado com sucesso!")
                        log_fallback_metrics(table_name, 'incremental_custom', 'seven_days', **context)
                        raise AirflowSkipException(f"Incremental customizado falhou para {table_name}, resolvido com incremental 7 dias")
                    else:
                        print(f"   ⚠️ Incremental 7 Dias falhou, iniciando full load...")
                else:
                    # NÍVEL 2B: Full Load customizado direto (diferença > 10%)
                    print(f"   🔄 Full Load customizado direto: diferença {diff_percentage:.1f}% > {MAX_GAP_PERCENTAGE}% (muitos registros)")
        else:
            print(f"   ⚠️ Sem incremental customizado disponível, pulando para full load...")
        
        # NÍVEL 3: Fallback final para full load customizado
        print(f"   🔄 NÍVEL 3: Executando Full Load customizado...")
        success, _ = process_table_etl(
            table_name=table_name,
            load_mode='full',
            id_field=final_id_field,
            source_table=final_source_table,
            special_select=final_special_select,
            custom_sql=final_custom_sql,
            **context
        )
        
        if success:
            print(f"   ✅ Full Load customizado executado com sucesso!")
            if incremental_custom_failed:
                log_fallback_metrics(table_name, 'incremental_custom', 'full_custom', **context)
                raise AirflowSkipException(f"Incremental customizado e incremental 7 dias falharam para {table_name}")
            else:
                log_fallback_metrics(table_name, 'full_custom', 'full_custom', **context)
                return True
        else:
            print(f"   ❌ Full Load customizado também falhou - erro crítico")
            raise Exception(f"Todos os níveis (incremental customizado, incremental 7 dias e full customizado) falharam para {table_name}")
    
    # ESTRATÉGIA 2: Tabelas normais -> incremental com 7 dias
    else:
        print(f"   ⚡ Estratégia: Incremental → Incremental 7 Dias → Full Load")
        
        # NÍVEL 1: Tenta incremental primeiro
        if final_id_field:
            print(f"   🔄 NÍVEL 1: Tentando incremental (diário)...")
            success, analysis_info = process_table_etl(
                table_name=table_name,
                load_mode='incremental',
                id_field=final_id_field,
                source_table=final_source_table,
                special_select=final_special_select,
                custom_sql=None,
                **context
            )
            
            if success:
                print(f"   ✅ Incremental executado com sucesso!")
                log_fallback_metrics(table_name, 'incremental', 'incremental', **context)
                return True
            else:
                incremental_failed = True
                print(f"   ⚠️ Incremental falhou, analisando estratégia...")
                
                # NÍVEL 2: Usa dados da validação para decidir Incremental 7 Dias vs Full Load
                should_use_seven_days, source_count, dest_count, diff_percentage = analysis_info
                print(f"   📊 Análise: origem={source_count:,}, destino={dest_count:,}, diferença={diff_percentage:.1f}%")
                
                if should_use_seven_days:
                    # NÍVEL 2A: Incremental 7 Dias (diferença ≤ 10%)
                    print(f"   🎯 Incremental 7 Dias: diferença {diff_percentage:.1f}% ≤ {MAX_GAP_PERCENTAGE}% (apenas {source_count - dest_count:,} registros)")
                    success, _ = process_table_etl(
                        table_name=table_name,
                        load_mode='seven_days',
                        id_field=final_id_field,
                        source_table=final_source_table,
                        special_select=final_special_select,
                        custom_sql=None,
                        **context
                    )
                    
                    if success:
                        print(f"   ✅ Incremental 7 Dias executado com sucesso!")
                        log_fallback_metrics(table_name, 'incremental', 'seven_days', **context)
                        raise AirflowSkipException(f"Incremental falhou para {table_name}, resolvido com incremental 7 dias")
                    else:
                        print(f"   ⚠️ Incremental 7 Dias falhou, iniciando full load...")
                else:
                    # NÍVEL 2B: Full Load direto (diferença > 10%)
                    print(f"   🔄 Full Load direto: diferença {diff_percentage:.1f}% > {MAX_GAP_PERCENTAGE}% (muitos registros)")
        else:
            print(f"   ⚠️ Sem ID field para incremental, pulando para full load...")
        
        # NÍVEL 3: Fallback final para full load
        print(f"   🔄 NÍVEL 3: Executando Full Load...")
        success, _ = process_table_etl(
            table_name=table_name,
            load_mode='full',
            id_field=final_id_field,
            source_table=final_source_table,
            special_select=final_special_select,
            custom_sql=None,
            **context
        )
        
        if success:
            print(f"   ✅ Full Load executado com sucesso!")
            if incremental_failed:
                log_fallback_metrics(table_name, 'incremental', 'full', **context)
                raise AirflowSkipException(f"Incremental e incremental 7 dias falharam para {table_name}")
            else:
                log_fallback_metrics(table_name, 'full', 'full', **context)
                return True
        else:
            print(f"   ❌ Full Load também falhou - erro crítico")
            raise Exception(f"Todos os níveis (incremental, incremental 7 dias e full) falharam para {table_name}")

def create_smart_unified_dag():
    """
    Cria a DAG única inteligente com fallback automático
    """
    
    # ===== CONFIGURAÇÃO DA DAG =====
    
    dag = DAG(
        dag_id='MIGRACAO-SYONET-HULKBUSTER',
        description='ETL Inteligente com Análise de Diferença (Incremental → Análise → Gap/Full) - Syonet',
        schedule_interval='30 3,11,13,15,17,19,21,23 * * *',  # Execuções frequentes
        start_date=datetime(2024, 1, 1),
        catchup=False,
        max_active_runs=1,
        max_active_tasks=5,
        default_args=BASE_DAG_ARGS
    )
    
    # ===== TASK INICIAL =====
    
    start_task = DummyOperator(
        task_id='start_smart_etl',
        dag=dag
    )
    
    # ===== GERAÇÃO DINÂMICA DE TASKS =====
    
    # Dicionário para armazenar as tasks criadas
    tasks = {}
    
    # Criar tasks para cada tabela na configuração
    for table_name, config in TABLES_CONFIG.items():
        task_id = f'smart_process_{table_name}'
        
        tasks[table_name] = PythonOperator(
            task_id=task_id,
            python_callable=process_table_with_smart_fallback,
            op_kwargs={
                'table_name': table_name,
                'id_field': config.get('id_field'),
                'source_table': config.get('source'),
                'special_select': config.get('select')
            },
            dag=dag
        )
    
    # ===== CONFIGURAÇÃO DE DEPENDÊNCIAS =====
    
    # Tabelas independentes (primeira onda)
    independent_tables = [
        'syo_agenda', 'syo_contaemail', 'syo_contatoemail', 'syo_clientearea', 
        'syo_telefones', 'campanhav2whatsapp', 'syo_novaclassificacao',
        'syo_novaclassificacaotipocliente', 'syo_faixanovaclassificacao',
        'syo_etapafunil', 'syo_empresa', 'syo_empresausuario', 'syo_donoconta',
        'syo_oficina', 'syo_usuario', 'syo_veiculo', 'syo_dadosinterfacecliente',
        'syo_peca', 'syo_evento', 'syo_cliente'
    ]
    
    # Configurar dependências básicas
    for table_name in independent_tables:
        if table_name in tasks:
            start_task >> tasks[table_name]
    
    # Dependências específicas (segunda onda)
    dependencies = {
        'syo_acao': ['syo_evento'],
        'syo_encaminhamento': ['syo_evento'],
        'syo_registrointerface': ['syo_evento'],
        'syo_historicoetapafunilevento': ['syo_evento'],
        'syo_evento_obs': ['syo_evento'],  # Evento_obs depende de evento
        'syo_clientealteracao': ['syo_cliente'],
        'syo_clientenovaclassificacao': ['syo_cliente'],
        'syo_empresacliente': ['syo_cliente'],
        'syo_donocontacliente': ['syo_cliente'],
        'syo_camposregistrointerface': ['syo_registrointerface'],
        'syo_campointerfacenegociacao': ['syo_interfacenegociacao']
    }
    
    # Configurar dependências específicas
    for dependent_table, prerequisite_tables in dependencies.items():
        if dependent_table in tasks:
            for prerequisite in prerequisite_tables:
                if prerequisite in tasks:
                    tasks[prerequisite] >> tasks[dependent_table]
    
    # ===== TASKS ESPECIAIS (NÃO-SYO) =====
    
    # Tasks especiais que não são processadas pela lógica padrão
    task_oportunidades_base = PythonOperator(
        task_id='process_tb_oportunidades_base',
        python_callable=process_tb_oportunidades_base,
        dag=dag
    )
    
    task_im_assinatura = PythonOperator(
        task_id='process_tb_im_assinatura_original',
        python_callable=process_tb_im_assinatura_original,
        dag=dag
    )
    
    task_maquinas_semana_passada = PythonOperator(
        task_id='process_tb_maquinas_semana_passada',
        python_callable=process_tb_maquinas_semana_passada,
        dag=dag
    )
    
    task_maquinas_atual = PythonOperator(
        task_id='process_tb_maquinas_atual',
        python_callable=process_tb_maquinas_atual,
        dag=dag
    )
    
    # Criar task intermediária para controlar fluxo
    middle_task = DummyOperator(
        task_id='middle_smart_etl',
        dag=dag
    )
    
    # Todas as tabelas syo_ (incluindo evento_obs) devem terminar antes das tabelas de negócio
    all_syo_tasks = list(tasks.values())
    
    for task in all_syo_tasks:
        task >> middle_task
    
    middle_task >> [task_oportunidades_base, task_im_assinatura]
    task_oportunidades_base >> [task_maquinas_semana_passada, task_maquinas_atual]
    
    # ===== TASK FINAL =====
    
    end_task = DummyOperator(
        task_id='end_smart_etl',
        dag=dag
    )
    
    # Conectar tasks especiais ao end_task
    [task_maquinas_semana_passada, task_maquinas_atual, task_im_assinatura] >> end_task
    
    return dag

# Criar a DAG inteligente
smart_unified_dag = create_smart_unified_dag()

# Registrar no globals para o Airflow
globals()['Syonet-Smart-Unified-ETL'] = smart_unified_dag

def log_fallback_metrics(table_name, attempted_method, successful_method, **context):
    """Log métricas de fallback para monitoramento"""
    dag_run = context.get('dag_run')
    if dag_run:
        print(f"📊 MÉTRICA FALLBACK: {table_name} | Tentativa: {attempted_method} | Sucesso: {successful_method} | DAG: {dag_run.dag_id}")