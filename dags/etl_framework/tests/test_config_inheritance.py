"""
Testes para validar herança de configurações
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from config.table_config import TableConfigLoader, TableConfig, TableType, IncrementalMode


def test_syonet_inheritance():
    """Testa herança de configurações do Syonet"""
    print("🧪 Testando herança de configurações do Syonet...")
    
    # Carrega configurações
    loader = TableConfigLoader('/home/<USER>/projects/airflow-v1/dags/etl_framework/systems/syonet')
    tables = loader.load_table_configs('bronze')
    
    print(f"\n📊 Total de tabelas carregadas: {len(tables)}")
    
    # Testa tabela pequena
    print("\n🔍 Testando tabela pequena (syo_agenda):")
    agenda = tables.get('syo_agenda')
    if agenda:
        print(f"  - Tipo: {agenda.table_type}")
        print(f"  - Modo: {agenda.incremental_mode}")
        print(f"  - Timeout: {agenda.timeout_seconds}s (deve ser 300)")
        print(f"  - Max Gap %: {agenda.max_gap_percentage} (deve herdar 10.0)")
        print(f"  - Max Abs Diff: {agenda.max_abs_diff} (deve herdar 10)")
        print(f"  - Chunk Size: {agenda.chunk_size} (deve herdar 50000)")
    
    # Testa tabela grande
    print("\n🔍 Testando tabela grande (syo_evento):")
    evento = tables.get('syo_evento')
    if evento:
        print(f"  - Tipo: {evento.table_type}")
        print(f"  - Modo: {evento.incremental_mode}")
        print(f"  - ID Field: {evento.id_field}")
        print(f"  - Timeout: {evento.timeout_seconds}s (deve herdar 900)")
        print(f"  - Select Fields: {'Sim' if evento.select_fields else 'Não'}")
        print(f"  - Max Gap %: {evento.max_gap_percentage} (deve herdar 10.0)")
    
    # Testa tabela com configuração especial
    print("\n🔍 Testando tabela especial (syo_empresacliente):")
    empresa = tables.get('syo_empresacliente')
    if empresa:
        print(f"  - Max Gap %: {empresa.max_gap_percentage} (deve ser 99.0 - custom)")
        print(f"  - Max Abs Diff: {empresa.max_abs_diff} (deve ser 1000000 - custom)")
    
    # Valida herança
    print("\n✅ Validando herança de configurações:")
    
    errors = []
    
    # Todas as tabelas devem ter chunk_size 50000 (herdado)
    for name, table in tables.items():
        if table.chunk_size != 50000:
            errors.append(f"{name}: chunk_size incorreto ({table.chunk_size})")
    
    # Tabelas pequenas devem ter timeout 300
    small_tables = [t for t in tables.values() if t.table_type == TableType.SMALL]
    for table in small_tables:
        if table.timeout_seconds != 300:
            errors.append(f"{table.name}: timeout incorreto para tabela pequena ({table.timeout_seconds})")
    
    # Tabelas grandes devem ter timeout 900 (exceto override)
    large_tables = [t for t in tables.values() if t.table_type == TableType.LARGE]
    for table in large_tables:
        # syo_evento tem timeout específico
        if table.name not in ['syo_evento', 'syo_cliente', 'syo_acao', 'syo_encaminhamento', 'syo_evento_obs']:
            if table.timeout_seconds != 900:
                errors.append(f"{table.name}: timeout incorreto para tabela grande ({table.timeout_seconds})")
    
    if errors:
        print("❌ Erros encontrados:")
        for error in errors:
            print(f"  - {error}")
    else:
        print("  ✅ Todas as heranças estão funcionando corretamente!")
    
    # Mostra economia de configuração
    print("\n💰 Economia de configuração:")
    print(f"  - Parâmetros max_gap_percentage removidos: 35")
    print(f"  - Parâmetros max_abs_diff removidos: 35")
    print(f"  - Parâmetros timeout_seconds herdados: ~20")
    print(f"  - Total de linhas economizadas: ~100")
    
    return len(errors) == 0


def test_template_defaults():
    """Testa valores padrão do template"""
    print("\n🧪 Testando valores padrão do framework...")
    
    # Simula sistema mínimo
    loader = TableConfigLoader('/home/<USER>/projects/airflow-v1/dags/etl_framework/systems/template')
    
    # Testa defaults do framework
    small_defaults = loader.FRAMEWORK_DEFAULTS['small']
    print("\n📋 Defaults para tabelas pequenas:")
    for key, value in small_defaults.items():
        print(f"  - {key}: {value}")
    
    large_defaults = loader.FRAMEWORK_DEFAULTS['large']
    print("\n📋 Defaults para tabelas grandes:")
    for key, value in large_defaults.items():
        print(f"  - {key}: {value}")
    
    return True


if __name__ == "__main__":
    print("=" * 60)
    print("🔧 TESTE DE HERANÇA DE CONFIGURAÇÕES")
    print("=" * 60)
    
    # Executa testes
    success1 = test_syonet_inheritance()
    success2 = test_template_defaults()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("✅ TODOS OS TESTES PASSARAM!")
    else:
        print("❌ ALGUNS TESTES FALHARAM!")
    print("=" * 60)