# ETL Framework V4 - Quick Start

## 🚀 Para Começar Imediatamente

### **1. Verificar se está funcionando**
```bash
cd /home/<USER>/projects/airflow-v1/dags
python3 etl_framework/validate_framework.py
```

**Resultado esperado**: ✅ TODAS AS VALIDAÇÕES PASSARAM!

### **2. Ver DAGs geradas**
```bash
# Verificar se DAGs foram criadas
python3 -c "
import v4_bronze, v4_silver
print('✅ DAGs Bronze:', [name for name in dir(v4_bronze) if 'V4_BRONZE' in name])
print('✅ DAGs Silver:', [name for name in dir(v4_silver) if 'V4_SILVER' in name])
"
```

**Resultado esperado**: 
- ✅ DAGs Bronze: ['V4_BRONZE_SYONET']
- ✅ DAGs Silver: ['V4_SILVER_SYONET']

### **3. Executar processamento de teste**
```bash
# Testar processamento bronze
python3 -c "
from etl_framework.c1_bronze.processor import create_bronze_processor
from etl_framework.config.dw_config import create_corporate_dw_config

dw_config = create_corporate_dw_config()
processor = create_bronze_processor('syonet', dw_config)

# Ver tabelas configuradas
tables = processor.get_bronze_tables_for_parallel_processing()
print(f'✅ {len(tables)} tabelas bronze configuradas')
for table in tables[:5]:
    print(f'   - {table.name} ({table.table_type.value})')

processor.cleanup()
"
```

## 🎯 Adicionar Novo Sistema (Exemplo: Oracle ERP)

### **Passo 1**: Criar arquivo de configuração
```bash
mkdir -p etl_framework/systems/oracle_erp
```

### **Passo 2**: Implementar configuração
```python
# etl_framework/systems/oracle_erp/config.py
from etl_framework.config.system_config import SystemConfig
from etl_framework.config.database_config import DatabaseConfig, DatabaseType
from etl_framework.config.table_config import TableConfig, TableType, IncrementalMode
from etl_framework.c2_silver.transformation import SilverTransformation

def create_oracle_erp_system_config(dw_config) -> SystemConfig:
    # Conexão Oracle
    oracle_config = DatabaseConfig(
        name="oracle_erp",
        db_type=DatabaseType.ORACLE,
        host="oracle-server",
        port=1521,
        database="ORCL",
        user="etl_user",
        password="password"
    )
    
    # Sistema
    system_config = SystemConfig(
        name="oracle_erp",
        description="Sistema Oracle ERP -> DW Corporativo",
        source_db_config=oracle_config,
        incremental_fields=["created_date", "updated_date"]
    )
    
    # Tabela exemplo
    system_config.add_bronze_table(TableConfig(
        name="tb_vendas",
        table_type=TableType.LARGE,
        incremental_mode=IncrementalMode.SMART,
        id_field="venda_id",
        source_table="vendas"
    ))
    
    # Transformação exemplo
    system_config.add_silver_transformation(SilverTransformation(
        name="tb_vendas_consolidadas",
        description="Vendas consolidadas por período",
        sql='''
            CREATE TABLE dbdwcorporativo.silver_oracle_tb_vendas_consolidadas AS
            SELECT 
                cliente_id,
                SUM(valor_venda) as total_vendas,
                COUNT(*) as qtd_vendas,
                DATE_TRUNC('month', data_venda) as mes_venda
            FROM dbdwcorporativo.bronze_oracle_erp_tb_vendas
            WHERE data_venda >= CURRENT_DATE - INTERVAL '12 months'
            GROUP BY cliente_id, DATE_TRUNC('month', data_venda)
        ''',
        dependencies=["bronze_oracle_erp_tb_vendas"]
    ))
    
    return system_config
```

### **Passo 3**: Adicionar aos geradores de DAG
```python
# v4_bronze.py - adicionar em SYSTEMS_CONFIG
'oracle_erp': {
    'description': 'Sistema Oracle ERP - Financeiro',
    'schedule_interval': '0 7 * * *',  # 7h da manhã
    'max_active_runs': 1,
    'tags': ['oracle', 'bronze', 'erp', 'financeiro']
}

# v4_silver.py - adicionar em SYSTEMS_CONFIG  
'oracle_erp': {
    'description': 'Sistema Oracle ERP - Transformações Silver',
    'schedule_interval': '0 9 * * *',  # 9h da manhã
    'max_active_runs': 1,
    'tags': ['oracle', 'silver', 'transformations', 'dw'],
    'bronze_dag_id': 'V4_BRONZE_ORACLE_ERP'
}
```

### **Passo 4**: Testar novo sistema
```bash
# Verificar se DAGs foram criadas
python3 -c "
import v4_bronze, v4_silver
print('✅ DAGs Bronze:', [name for name in dir(v4_bronze) if 'V4_BRONZE' in name])
print('✅ DAGs Silver:', [name for name in dir(v4_silver) if 'V4_SILVER' in name])
"
```

**Resultado esperado**:
- ✅ DAGs Bronze: ['V4_BRONZE_SYONET', 'V4_BRONZE_ORACLE_ERP']
- ✅ DAGs Silver: ['V4_SILVER_SYONET', 'V4_SILVER_ORACLE_ERP']

## 🔧 Troubleshooting

### **Erro: ModuleNotFoundError**
```bash
# Verificar se está no diretório correto
cd /home/<USER>/projects/airflow-v1/dags

# Verificar estrutura
ls -la etl_framework/
```

### **Erro: Conexão com banco**
```bash
# Testar conexões
python3 -c "
from etl_framework.config.dw_config import create_corporate_dw_config
dw_config = create_corporate_dw_config()
dw_config.connection.test_connection()
print('✅ Conexão DW OK')
"
```

### **Erro: DAG não aparece no Airflow**
1. Verificar se arquivo está em `/dags/`
2. Verificar logs do Airflow para erros de importação
3. Executar validação: `python3 etl_framework/validate_framework.py`

## 📞 Suporte

### **Validação Completa**
```bash
python3 etl_framework/validate_framework.py
```

### **Logs Detalhados**
```bash
# Ver configuração do Syonet
python3 -c "
from etl_framework.systems.syonet.config import create_syonet_system_config
from etl_framework.config.dw_config import create_corporate_dw_config

dw_config = create_corporate_dw_config()
syonet_config = create_syonet_system_config(dw_config)

print(f'Sistema: {syonet_config.name}')
print(f'Tabelas Bronze: {len(syonet_config.bronze_tables)}')
print(f'Transformações Silver: {len(syonet_config.silver_transformations)}')
"
```

### **Estrutura de Arquivos**
```bash
find etl_framework/ -name "*.py" | head -20
```

## 🎉 Pronto!

O framework está configurado e funcionando. Para usar:

1. **Sistemas existentes**: DAGs já funcionam automaticamente
2. **Novos sistemas**: Seguir passos acima
3. **Problemas**: Executar validação e verificar logs

**Framework V4 = Zero código, máxima reutilização!** 🚀
