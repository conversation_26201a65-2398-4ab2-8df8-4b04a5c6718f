"""
Definição de Transformações Silver - ETL Framework

Define transformações CREATE TABLE AS para a camada Silver.
"""

from dataclasses import dataclass, field
from typing import List, Optional, Dict, Any
from enum import Enum


class TransformationFrequency(Enum):
    """Frequência de execução das transformações"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    ON_DEMAND = "on_demand"


@dataclass
class SilverTransformation:
    """
    Definição de uma transformação Silver
    
    Representa uma transformação CREATE TABLE AS que será executada
    dentro do Data Warehouse para criar tabelas enriquecidas.
    """
    
    name: str
    description: str
    sql: str
    
    # Configurações de execução
    frequency: TransformationFrequency = TransformationFrequency.DAILY
    timeout_seconds: int = 600
    
    # Dependências (tabelas que devem existir antes)
    dependencies: List[str] = field(default_factory=list)
    
    # Grants a serem aplicados após criação
    grants: List[str] = field(default_factory=list)
    
    # Metadados
    tags: List[str] = field(default_factory=list)
    owner: str = "data-engineering"
    
    # Configurações específicas
    drop_if_exists: bool = True
    validate_result: bool = True
    
    def __post_init__(self):
        """Validações após inicialização"""
        if not self.sql.strip():
            raise ValueError(f"SQL não pode estar vazio para transformação {self.name}")
        
        # Garante que o SQL seja CREATE TABLE AS ou SELECT (ignora comentários no início)
        # Remove comentários de linha simples (--) do início
        sql_lines = [line.strip() for line in self.sql.strip().split('\n') if line.strip() and not line.strip().startswith('--')]
        if sql_lines:
            sql_upper = sql_lines[0].upper().strip()
            valid_starts = ('CREATE TABLE', 'DROP TABLE', 'SELECT', 'WITH')
            if not any(sql_upper.startswith(start) for start in valid_starts):
                raise ValueError(f"SQL deve começar com CREATE TABLE, DROP TABLE, SELECT ou WITH para {self.name} (encontrado: {sql_upper[:50]}...)")
    
    def get_full_sql(self, system_name: str, dw_config) -> str:
        """
        Retorna SQL completo com DROP IF EXISTS se configurado
        """
        table_name = dw_config.get_table_name(system_name, self.name, "silver")
        
        if self.drop_if_exists and not self.sql.upper().strip().startswith('DROP'):
            drop_sql = f"DROP TABLE IF EXISTS {table_name};"
            return f"{drop_sql}\n\n{self.sql}"
        
        return self.sql
    
    def should_execute_today(self) -> bool:
        """
        Verifica se a transformação deve ser executada hoje
        """
        from datetime import datetime
        
        if self.frequency == TransformationFrequency.DAILY:
            return True
        elif self.frequency == TransformationFrequency.WEEKLY:
            # Executa apenas às sextas-feiras (weekday 4)
            return datetime.now().weekday() == 4
        elif self.frequency == TransformationFrequency.MONTHLY:
            # Executa apenas no primeiro dia do mês
            return datetime.now().day == 1
        else:
            # ON_DEMAND não executa automaticamente
            return False
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte para dicionário"""
        return {
            'name': self.name,
            'description': self.description,
            'sql': self.sql,
            'frequency': self.frequency.value,
            'timeout_seconds': self.timeout_seconds,
            'dependencies': self.dependencies,
            'grants': self.grants,
            'tags': self.tags,
            'owner': self.owner,
            'drop_if_exists': self.drop_if_exists,
            'validate_result': self.validate_result
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'SilverTransformation':
        """Cria transformação a partir de dicionário"""
        return cls(
            name=data['name'],
            description=data['description'],
            sql=data['sql'],
            frequency=TransformationFrequency(data.get('frequency', 'daily')),
            timeout_seconds=data.get('timeout_seconds', 600),
            dependencies=data.get('dependencies', []),
            grants=data.get('grants', []),
            tags=data.get('tags', []),
            owner=data.get('owner', 'data-engineering'),
            drop_if_exists=data.get('drop_if_exists', True),
            validate_result=data.get('validate_result', True)
        )


def create_standard_grants(table_name: str, users: List[str] = None) -> List[str]:
    """
    Cria grants padrão para uma tabela silver
    """
    if users is None:
        users = ['bq_vitor_barros_u']  # Usuário padrão das DAGs V3
    
    grants = []
    for user in users:
        grants.append(f"GRANT SELECT ON TABLE {table_name} TO {user}")
    
    return grants


def create_syonet_transformations() -> List[SilverTransformation]:
    """
    Cria transformações silver específicas do Syonet
    Baseadas nas DAGs V3_SILVER_SYONET
    """
    transformations = []
    
    # tb_oportunidades_base - Transformação principal (OTIMIZADA)
    transformations.append(SilverTransformation(
        name="tb_oportunidades_base",
        description="Tabela consolidada de oportunidades do Syonet com dados enriquecidos (OTIMIZADA: SELECT específico + CTE combinada)",
        sql=_get_oportunidades_base_sql(),
        frequency=TransformationFrequency.DAILY,
        timeout_seconds=900,  # Aumentado para 15 minutos devido às otimizações
        dependencies=[
            "bronze_syonet_syo_evento",
            "bronze_syonet_syo_cliente", 
            "bronze_syonet_syo_encaminhamento",
            "bronze_syonet_syo_usuario"
        ],
        tags=["syonet", "oportunidades", "vendas"],
        validate_result=True
    ))
    
    # tb_IM_Assinatura_original - Transformação de assinaturas (OTIMIZADA)
    transformations.append(SilverTransformation(
        name="tb_IM_Assinatura_original",
        description="Tabela de assinaturas e indicações do Syonet (OTIMIZADA: SELECT específico + CTE combinada)",
        sql=_get_im_assinatura_sql(),
        frequency=TransformationFrequency.DAILY,
        timeout_seconds=900,  # Aumentado para 15 minutos devido às otimizações
        dependencies=[
            "bronze_syonet_syo_evento",
            "bronze_syonet_syo_cliente",
            "bronze_syonet_syo_encaminhamento"
        ],
        grants=["GRANT SELECT ON TABLE {table_name} TO bq_vitor_barros_u"],
        tags=["syonet", "assinaturas", "indicacoes"],
        validate_result=True
    ))
    
    # tb_maquinas_semana_passada - Transformação semanal
    transformations.append(SilverTransformation(
        name="tb_maquinas_semana_passada",
        description="Relatório semanal de máquinas (executado apenas às sextas-feiras)",
        sql=_get_maquinas_semana_passada_sql(),
        frequency=TransformationFrequency.WEEKLY,
        timeout_seconds=300,
        dependencies=["bronze_syonet_syo_evento"],
        tags=["syonet", "maquinas", "semanal"],
        validate_result=True
    ))
    
    # tb_maquinas_atual - Cópia da tb_oportunidades_base
    transformations.append(SilverTransformation(
        name="tb_maquinas_atual",
        description="Cópia atual da tabela de oportunidades para compatibilidade",
        sql=_get_maquinas_atual_sql(),
        frequency=TransformationFrequency.DAILY,
        timeout_seconds=300,
        dependencies=["tb_oportunidades_base"],  # Depende da transformação anterior
        grants=["GRANT SELECT ON TABLE {table_name} TO bq_vitor_barros_u"],
        tags=["syonet", "maquinas", "compatibilidade"],
        validate_result=True
    ))

    # bi_maquinas - View BIMaquinas migrada do SQL Server (OTIMIZADA)
    transformations.append(SilverTransformation(
        name="bi_maquinas",
        description="View BIMaquinas migrada do SQL Server com classificação de clientes (OTIMIZADA para PostgreSQL)",
        sql=_get_bi_maquinas_sql(),
        frequency=TransformationFrequency.DAILY,
        timeout_seconds=600,
        dependencies=["tb_maquinas_atual"],  # Depende da transformação anterior
        grants=["GRANT SELECT ON TABLE {table_name} TO bq_vitor_barros_u"],
        tags=["syonet", "bi", "maquinas", "classificacao"],
        validate_result=True
    ))

    # bi_maquinas_pivotada - View BIMaquinasPivotada com dados pivotados (OTIMIZADA)
    transformations.append(SilverTransformation(
        name="bi_maquinas_pivotada",
        description="View BIMaquinasPivotada com dados pivotados usando UNNEST PostgreSQL (OTIMIZADA)",
        sql=_get_bi_maquinas_pivotada_sql(),
        frequency=TransformationFrequency.DAILY,
        timeout_seconds=600,
        dependencies=["bi_maquinas"],  # Depende da transformação anterior
        grants=["GRANT SELECT ON TABLE {table_name} TO bq_vitor_barros_u"],
        tags=["syonet", "bi", "maquinas", "pivotada"],
        validate_result=True
    ))

    return transformations


def _get_oportunidades_base_sql() -> str:
    """SQL da transformação tb_oportunidades_base (EXATO do V3)"""
    return """
    CREATE TABLE dbdwcorporativo.silver_syonet_tb_oportunidades_base AS
    WITH evt_base AS (
        SELECT id_evento, id_cliente, id_tipoevento, dt_inc, dt_conclusao, dt_proximaacao,
               ds_formacontato, ds_midia, ds_assunto, ds_resultado, id_statusevento,
               ds_temperatura, id_componente
        FROM dbdwcorporativo.bronze_syonet_syo_evento
        WHERE id_tipoevento IN ('DVM LEAD','DVM OPORTUNIDADE','DVM LEAD RELACIONAMENTO','DVM RELACIONAMENTO','DVM LICITACAO')
          AND dt_inc >= 1672531200000  -- 2023-01-01 em milliseconds (otimizado)
    ),
    acao_stats AS (
        SELECT id_evento,
               MAX(dt_alt) as dt_alt_last,
               MIN(dt_alt) as dt_alt_first,
               MAX(CASE WHEN rn_desc = 1 THEN id_motivoresultado END) as id_motivoresultado
        FROM (
            SELECT id_evento, dt_alt, id_motivoresultado,
                   ROW_NUMBER() OVER (PARTITION BY id_evento ORDER BY id_acao DESC) as rn_desc
            FROM dbdwcorporativo.bronze_syonet_syo_acao
            WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
        ) t
        WHERE rn_desc <= 1
        GROUP BY id_evento
    ),
    ri_last AS (
        SELECT DISTINCT ON (id_evento) id_evento, id_registrointerface
        FROM dbdwcorporativo.bronze_syonet_syo_registrointerface
        WHERE COALESCE(ic_comentario,'0')='0'
        ORDER BY id_evento, id_registrointerface DESC
    ),
    rp AS (
        SELECT id_registrointerface,
               MAX(CASE WHEN ds_etiqueta='Modelo de Interesse'     THEN ds_valor END) AS _MODELO,
               MAX(CASE WHEN ds_etiqueta='Modelo de Interesse 2'   THEN ds_valor END) AS _MODELO2,
               MAX(CASE WHEN ds_etiqueta='Modelo de Interesse 3'   THEN ds_valor END) AS _MODELO3,
               MAX(CASE WHEN ds_etiqueta='Modelo de Interesse 4'   THEN ds_valor END) AS _MODELO4,
               MAX(CASE WHEN ds_etiqueta='Modelo de Interesse 5'   THEN ds_valor END) AS _MODELO5,
               MAX(CASE WHEN ds_etiqueta='Quantidade'              THEN ds_valor END) AS QTD1,
               MAX(CASE WHEN ds_etiqueta='Quantidade 2'            THEN ds_valor END) AS QTD2,
               MAX(CASE WHEN ds_etiqueta='Quantidade 3'            THEN ds_valor END) AS QTD3,
               MAX(CASE WHEN ds_etiqueta='Quantidade 4'            THEN ds_valor END) AS QTD4,
               MAX(CASE WHEN ds_etiqueta='Quantidade 5'            THEN ds_valor END) AS QTD5,
               MAX(CASE WHEN ds_etiqueta='Valor'                   THEN ds_valor END) AS VALOR1,
               MAX(CASE WHEN ds_etiqueta='Valor 2'                 THEN ds_valor END) AS VALOR2,
               MAX(CASE WHEN ds_etiqueta='Valor 3'                 THEN ds_valor END) AS VALOR3,
               MAX(CASE WHEN ds_etiqueta='Valor 4'                 THEN ds_valor END) AS VALOR4,
               MAX(CASE WHEN ds_etiqueta='Valor 5'                 THEN ds_valor END) AS VALOR5,
               MAX(CASE WHEN ds_etiqueta='Versão'                  THEN
                   CASE WHEN TRIM(ds_valor) IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO,
               MAX(CASE WHEN ds_etiqueta='Versão 2'                THEN
                   CASE WHEN TRIM(ds_valor) IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO2,
               MAX(CASE WHEN ds_etiqueta='Versão 3'                THEN
                   CASE WHEN TRIM(ds_valor) IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO3,
               MAX(CASE WHEN ds_etiqueta='Versão 4'                THEN
                   CASE WHEN TRIM(ds_valor) IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO4,
               MAX(CASE WHEN ds_etiqueta='Versão 5'                THEN
                   CASE WHEN TRIM(ds_valor) IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO5,
               MAX(CASE WHEN ds_etiqueta='Previsão de Faturamento' THEN ds_valor END) AS PREV_FAT,
               MAX(CASE WHEN ds_etiqueta='Data do faturamento'     THEN ds_valor END) AS DT_FAT,
               MAX(CASE WHEN ds_etiqueta='FATURA ESSE MÊS?'        THEN ds_valor END) AS FATURA_MES
        FROM dbdwcorporativo.bronze_syonet_syo_camposregistrointerface
        GROUP BY id_registrointerface
    ),
    hef_agg AS (
        SELECT id_evento,
               'MAQUINA FATURADA' AS Faturada,
               MIN(TO_TIMESTAMP(dt_inc/1000) - INTERVAL '3 hours') AS dt_inc
        FROM dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento
        WHERE id_etapafunil IN (6,9,17,99,20,45,153,144,154)
        GROUP BY id_evento
    )
    SELECT
        EVT.id_cliente,
        CLI.nm_cliente         AS "Cliente",
        CLI.no_cpfcnpj         AS "CNPJ/CPF",
        COALESCE(CLI.nm_cidadecom, CLI.nm_cidaderes) AS "Cidade",
        COALESCE(CLI.sg_ufcom,    CLI.sg_ufres)      AS "UF",
        EVT.id_tipoevento,
        EMP.ap_empresa         AS "Filial",
        EVT.id_evento          AS "Nº Evento",
        EVT.ds_formacontato    AS "Origem",
        EVT.ds_midia           AS "Midia",
        EVT.ds_assunto         AS "Assunto",
        -- OTIMIZAÇÃO: Conversões de timestamp otimizadas
        CASE WHEN EVT.dt_conclusao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_conclusao/1000) - INTERVAL '3 hours' END AS "Data Fechamento",
        TO_TIMESTAMP(EVT.dt_inc/1000) - INTERVAL '3 hours' AS "Data Inclusão",
        CASE WHEN acs.dt_alt_last IS NOT NULL THEN TO_TIMESTAMP(acs.dt_alt_last/1000) - INTERVAL '3 hours' END AS "Data U.Alteração",
        CASE WHEN acs.dt_alt_first IS NOT NULL THEN TO_TIMESTAMP(acs.dt_alt_first/1000) - INTERVAL '3 hours' END AS "Data P.Alteração",
        CASE WHEN EVT.dt_proximaacao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_proximaacao/1000) - INTERVAL '3 hours' END AS "Data P.Acao",
        COALESCE(EVT.ds_resultado,EVT.id_statusevento) AS "Status",
        rp._MODELO  AS "Modelo Interesse 1",
        rp._MODELO2 AS "Modelo Interesse 2",
        rp._MODELO3 AS "Modelo Interesse 3",
        rp._MODELO4 AS "Modelo Interesse 4",
        rp._MODELO5 AS "Modelo Interesse 5",
        mv1.id_versao AS "Máquina 1", mv2.id_versao AS "Máquina 2", mv3.id_versao AS "Máquina 3",
        mv4.id_versao AS "Máquina 4", mv5.id_versao AS "Máquina 5",
        rp.QTD1 AS "Quantidade 1", rp.QTD2 AS "Quantidade 2", rp.QTD3 AS "Quantidade 3",
        rp.QTD4 AS "Quantidade 4", rp.QTD5 AS "Quantidade 5",
        CONCAT(ETF.no_ordem::text, '- ', ETF.nm_etapafunil) AS "Etapa Funil",
        CASE WHEN EVT.id_statusevento = 'ANDAMENTO' THEN MR.ds_motivo END AS "Motivo de Andamento",
        CASE WHEN EVT.ds_resultado   = 'INSUCESSO'  THEN MR.ds_motivo END AS "Motivo da Perda",
        USU.nm_login AS "Usuário AJ",
        REPLACE(rp.VALOR1,'Não informado','0,00') AS "Valor 1",
        REPLACE(rp.VALOR2,'Não informado','0,00') AS "Valor 2",
        REPLACE(rp.VALOR3,'Não informado','0,00') AS "Valor 3",
        REPLACE(rp.VALOR4,'Não informado','0,00') AS "Valor 4",
        REPLACE(rp.VALOR5,'Não informado','0,00') AS "Valor 5",
        CASE WHEN rp.PREV_FAT IS NOT NULL
             AND TRIM(rp.PREV_FAT) != ''
             AND TRIM(rp.PREV_FAT) NOT ILIKE '%selecione%'
             AND TRIM(rp.PREV_FAT) ~ '^[0-9]+$'
             THEN TO_TIMESTAMP(TRIM(rp.PREV_FAT)::bigint/1000) - INTERVAL '3 hours' END AS "Previsão Faturamento",
        evt.ds_temperatura AS "Temperatura",
        EVT.id_componente  AS "Evento Anterior",
        hef_agg.Faturada,
        hef_agg.dt_inc        AS "Data Etapa",
        CASE WHEN rp.DT_FAT IS NOT NULL
             AND TRIM(rp.DT_FAT) != ''
             AND TRIM(rp.DT_FAT) NOT ILIKE '%selecione%'
             AND TRIM(rp.DT_FAT) ~ '^[0-9]+$'
             THEN TO_TIMESTAMP(TRIM(rp.DT_FAT)::bigint/1000) - INTERVAL '3 hours' END AS "Data do Faturamento",
        rp.FATURA_MES     AS "Fatura esse mês ?"
    FROM        evt_base EVT
    INNER JOIN  dbdwcorporativo.bronze_syonet_syo_encaminhamento ENC
               ON ENC.id_evento = EVT.id_evento AND ENC.id_statusagenteativo = 'S'
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_cliente CLI ON CLI.id_cliente = EVT.id_cliente
    INNER JOIN  dbdwcorporativo.bronze_syonet_syo_usuario USU ON USU.id_usuario = ENC.id_agente
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_empresa EMP ON EMP.id_empresa = USU.id_empresa
    LEFT JOIN   acao_stats acs ON acs.id_evento = EVT.id_evento
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_motivoresultado MR ON acs.id_motivoresultado = MR.id_motivoresultado
    LEFT JOIN   ri_last ril ON ril.id_evento = EVT.id_evento
    LEFT JOIN   rp ON rp.id_registrointerface = ril.id_registrointerface
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv1 ON mv1.id_modeloversao = rp._VERSAO
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv2 ON mv2.id_modeloversao = rp._VERSAO2
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv3 ON mv3.id_modeloversao = rp._VERSAO3
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv4 ON mv4.id_modeloversao = rp._VERSAO4
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv5 ON mv5.id_modeloversao = rp._VERSAO5
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento HEF ON EVT.id_evento = HEF.id_evento AND HEF.ic_etapaatual = '1'
    LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_etapafunil ETF ON HEF.id_etapafunil = ETF.id_etapafunil
    LEFT JOIN   hef_agg ON hef_agg.id_evento = EVT.id_evento
    """


def _get_im_assinatura_sql() -> str:
    """SQL da transformação tb_IM_Assinatura_original (das DAGs V3)"""
    return """
    CREATE TABLE dbdwcorporativo.silver_syonet_tb_IM_Assinatura_original AS
    WITH evt_base AS (
        SELECT id_evento, id_cliente, id_tipoevento, dt_inc, dt_conclusao, dt_proximaacao,
               ds_formacontato, ds_assunto, ds_resultado, id_statusevento, ds_temperatura, id_componente
        FROM dbdwcorporativo.bronze_syonet_syo_evento
        WHERE id_tipoevento IN ('INDICACAO ASSINATURA','DVA ASSINATURA','PRECIFICACAO ASSINATURA',
                                'DVA LEAD RELACIONAMENTO','DVA RELACIONAMENTO',
                                'PRECIFICACAO AVANT','DVA ASSINATURA AVANT')
          AND id_statusevento <> 'CANCELADO'
          AND dt_inc > 1704067200000  -- 2023-12-31 em milliseconds
    ),
    acao_stats AS (
        SELECT id_evento,
               MAX(dt_alt) as dt_alt_last,
               MIN(dt_alt) as dt_alt_first,
               MAX(CASE WHEN rn_desc = 1 THEN id_motivoresultado END) as id_motivoresultado
        FROM (
            SELECT id_evento, dt_alt, id_motivoresultado,
                   ROW_NUMBER() OVER (PARTITION BY id_evento ORDER BY id_acao DESC) as rn_desc
            FROM dbdwcorporativo.bronze_syonet_syo_acao
            WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
        ) t
        WHERE rn_desc <= 1
        GROUP BY id_evento
    ),
    ri_last AS (
        SELECT DISTINCT ON (id_evento) id_evento, id_registrointerface
        FROM dbdwcorporativo.bronze_syonet_syo_registrointerface
        WHERE COALESCE(ic_comentario,'0')='0'
          AND COALESCE(ic_excluido,'0')='0'
          AND COALESCE(ic_editado,'0')='0'
        ORDER BY id_evento, id_registrointerface DESC
    ),
    ri_pivot AS (
        SELECT id_registrointerface,
               MAX(CASE WHEN ds_etiqueta='_MODELO'  THEN ds_valor END) AS _MODELO,
               MAX(CASE WHEN ds_etiqueta='_MODELO2' THEN ds_valor END) AS _MODELO2,
               MAX(CASE WHEN ds_etiqueta='_MODELO3' THEN ds_valor END) AS _MODELO3,
               MAX(CASE WHEN ds_etiqueta='_MODELO4' THEN ds_valor END) AS _MODELO4,
               MAX(CASE WHEN ds_etiqueta='_MODELO5' THEN ds_valor END) AS _MODELO5,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO'  THEN ds_valor END) AS VALOR1,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 2' THEN ds_valor END) AS VALOR2,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 3' THEN ds_valor END) AS VALOR3,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 4' THEN ds_valor END) AS VALOR4,
               MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 5' THEN ds_valor END) AS VALOR5,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE'  THEN ds_valor END) AS QTD1,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE2' THEN ds_valor END) AS QTD2,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE3' THEN ds_valor END) AS QTD3,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE4' THEN ds_valor END) AS QTD4,
               MAX(CASE WHEN ds_etiqueta='_QUANTIDADE5' THEN ds_valor END) AS QTD5,
               -- Conversão numérica SEGURA
               MAX(CASE WHEN ds_etiqueta='_VERSAO'  THEN
                   CASE WHEN ds_valor IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO,
               MAX(CASE WHEN ds_etiqueta='_VERSAO2' THEN
                   CASE WHEN ds_valor IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO2,
               MAX(CASE WHEN ds_etiqueta='_VERSAO3' THEN
                   CASE WHEN ds_valor IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO3,
               MAX(CASE WHEN ds_etiqueta='_VERSAO4' THEN
                   CASE WHEN ds_valor IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO4,
               MAX(CASE WHEN ds_etiqueta='_VERSAO5' THEN
                   CASE WHEN ds_valor IS NOT NULL
                        AND TRIM(ds_valor) != ''
                        AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                        AND TRIM(ds_valor) ~ '^[0-9]+$'
                        THEN TRIM(ds_valor)::bigint ELSE NULL END
               END) AS _VERSAO5
        FROM dbdwcorporativo.bronze_syonet_syo_camposregistrointerface
        GROUP BY id_registrointerface
    )
    SELECT
        EVT.id_cliente                               AS id_cliente,
        CLI.nm_cliente                               AS Cliente,
        CLI.no_cpfcnpj                               AS CNPJ_CPF,
        EVT.id_tipoevento                            AS Tipo_Evento,
        EMP.ap_empresa                               AS Filial,
        EVT.id_evento                                AS Numero_Evento,
        EVT.ds_formacontato                          AS Origem,
        rp._MODELO                                   AS Modelo_Interesse_1,
        mv1.id_versao                                AS Maquina_1,
        rp.QTD1                                      AS Quantidade_1,
        REPLACE(rp.VALOR1,'Não informado','0,00')    AS Valor_1,
        rp._MODELO2                                  AS Modelo_Interesse_2,
        mv2.id_versao                                AS Maquina_2,
        rp.QTD2                                      AS Quantidade_2,
        REPLACE(rp.VALOR2,'Não informado','0,00')    AS Valor_2,
        rp._MODELO3                                  AS Modelo_Interesse_3,
        mv3.id_versao                                AS Maquina_3,
        rp.QTD3                                      AS Quantidade_3,
        REPLACE(rp.VALOR3,'Não informado','0,00')    AS Valor_3,
        rp._MODELO4                                  AS Modelo_Interesse_4,
        mv4.id_versao                                AS Maquina_4,
        rp.QTD4                                      AS Quantidade_4,
        REPLACE(rp.VALOR4,'Não informado','0,00')    AS Valor_4,
        rp._MODELO5                                  AS Modelo_Interesse_5,
        mv5.id_versao                                AS Maquina_5,
        rp.QTD5                                      AS Quantidade_5,
        REPLACE(rp.VALOR5,'Não informado','0,00')    AS Valor_5,
        EVT.ds_assunto                               AS Assunto,
        CASE WHEN EVT.dt_conclusao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_conclusao/1000) - INTERVAL '3 hours' END AS Data_Fechamento,
        TO_TIMESTAMP(EVT.dt_inc/1000) - INTERVAL '3 hours' AS Data_Inclusao,
        CASE WHEN acs.dt_alt_last IS NOT NULL THEN TO_TIMESTAMP(acs.dt_alt_last/1000) - INTERVAL '3 hours' END AS Data_U_Alteracao,
        CASE WHEN acs.dt_alt_first IS NOT NULL THEN TO_TIMESTAMP(acs.dt_alt_first/1000) - INTERVAL '3 hours' END AS Data_P_Alteracao,
        CASE WHEN EVT.dt_proximaacao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_proximaacao/1000) - INTERVAL '3 hours' END AS Data_P_Acao,
        COALESCE(EVT.ds_resultado,EVT.id_statusevento) AS Status,
        CONCAT(ETF.no_ordem::text, '- ', ETF.nm_etapafunil) AS Etapa_Funil,
        CASE WHEN EVT.id_statusevento='ANDAMENTO' THEN MR.ds_motivo END AS Motivo_de_Andamento,
        CASE WHEN EVT.ds_resultado='INSUCESSO' THEN MR.ds_motivo END    AS Motivo_da_Perda,
        USU.nm_login                                   AS Vendedor,
        EVT.ds_temperatura                             AS Temperatura,
        EVT1.id_tipoevento                             AS Tipo_Evento_Anterior,
        EVT1.cd_usuarioinc                             AS Indicante_Evento_Anterior
    FROM evt_base EVT
    INNER JOIN dbdwcorporativo.bronze_syonet_syo_encaminhamento ENC
            ON ENC.id_evento = EVT.id_evento AND ENC.id_statusagenteativo = 'S'
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_cliente CLI ON CLI.id_cliente = EVT.id_cliente
    INNER JOIN dbdwcorporativo.bronze_syonet_syo_empresa EMP ON EMP.id_empresa = ENC.id_empresa
    INNER JOIN dbdwcorporativo.bronze_syonet_syo_usuario USU ON USU.id_usuario = ENC.id_agente
    LEFT JOIN acao_stats acs ON acs.id_evento = EVT.id_evento
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_motivoresultado MR ON acs.id_motivoresultado = MR.id_motivoresultado
    LEFT JOIN ri_last ril ON ril.id_evento = EVT.id_evento
    LEFT JOIN ri_pivot rp ON rp.id_registrointerface = ril.id_registrointerface
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv1 ON mv1.id_modeloversao = rp._VERSAO
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv2 ON mv2.id_modeloversao = rp._VERSAO2
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv3 ON mv3.id_modeloversao = rp._VERSAO3
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv4 ON mv4.id_modeloversao = rp._VERSAO4
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv5 ON mv5.id_modeloversao = rp._VERSAO5
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento HEF ON EVT.id_evento = HEF.id_evento AND HEF.ic_etapaatual = '1'
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_etapafunil ETF ON HEF.id_etapafunil = ETF.id_etapafunil
    LEFT JOIN dbdwcorporativo.bronze_syonet_syo_evento EVT1 ON EVT1.id_evento = EVT.id_componente
    """


def _get_maquinas_semana_passada_sql() -> str:
    """SQL da transformação tb_maquinas_semana_passada (das DAGs V3)"""
    return """
    CREATE TABLE dbdwcorporativo.silver_syonet_tb_maquinas_semana_passada AS
    SELECT
        'SEMANA_PASSADA' as periodo,
        COUNT(*) as total_maquinas,
        CURRENT_TIMESTAMP as dt_processamento
    FROM dbdwcorporativo.bronze_syonet_syo_evento
    WHERE dt_inc >= EXTRACT(EPOCH FROM (CURRENT_DATE - INTERVAL '7 days')) * 1000
      AND dt_inc < EXTRACT(EPOCH FROM CURRENT_DATE) * 1000
    """


def _get_maquinas_atual_sql() -> str:
    """SQL da transformação tb_maquinas_atual (das DAGs V3)"""
    return """
    CREATE TABLE dbdwcorporativo.silver_syonet_tb_maquinas_atual AS
    SELECT * FROM dbdwcorporativo.silver_syonet_tb_oportunidades_base
    """


def _get_bi_maquinas_sql() -> str:
    """SQL da transformação bi_maquinas (das DAGs V3) - OTIMIZADA para PostgreSQL"""
    return """
    CREATE TABLE dbdwcorporativo.silver_syonet_bi_maquinas AS
    WITH base_maquinas AS (
        SELECT DISTINCT ON (MQAT."Nº Evento")
            MQAT."Cliente",
            MQAT."CNPJ/CPF",
            MQAT."Cidade",
            MQAT."UF",
            MQAT."id_tipoevento",
            MQAT."Filial",
            MQAT."Nº Evento",
            MQAT."Data Fechamento",
            MQAT."Data Inclusão",
            MQAT."Data P.Acao",
            MQAT."Data U.Alteração",
            MQAT."Data P.Alteração",
            MQAT."Data Fechamento" as "Hora Fechamento",
            MQAT."Data Inclusão" as "Hora Inclusão",
            MQAT."Data U.Alteração" as "Hora U.Alteração",
            MQAT."Data P.Alteração" as "Hora P.Alteração",
            MQAT."Status",
            MQAT."Modelo Interesse 1",
            MQAT."Máquina 1",
            MQAT."Quantidade 1",
            MQAT."Modelo Interesse 2",
            MQAT."Máquina 2",
            MQAT."Quantidade 2",
            MQAT."Modelo Interesse 3",
            MQAT."Máquina 3",
            MQAT."Quantidade 3",
            MQAT."Modelo Interesse 4",
            MQAT."Máquina 4",
            MQAT."Quantidade 4",
            MQAT."Modelo Interesse 5",
            MQAT."Máquina 5",
            MQAT."Quantidade 5",
            MQAT."Valor 1",
            MQAT."Valor 2",
            MQAT."Valor 3",
            MQAT."Valor 4",
            MQAT."Valor 5",
            MQAT."Etapa Funil",
            MQAT."Motivo de Andamento",
            MQAT."Motivo da Perda",
            MQAT."Usuário AJ",
            MQAT."Temperatura",
            MQAT."Evento Anterior",
            MQAT.faturada,
            MQAT."Data Etapa",
            MQAT."Data do Faturamento",
            MQAT."Fatura esse mês ?",
            MQAT."Previsão Faturamento"
        FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual MQAT
        ORDER BY MQAT."Nº Evento", MQAT."Data Inclusão" DESC
    )
    SELECT
        "Cliente",
        "CNPJ/CPF",
        "Cidade",
        "UF",
        "id_tipoevento",
        "Filial",
        "Nº Evento",
        "Data Fechamento",
        "Data Inclusão",
        "Data P.Acao",
        "Data U.Alteração",
        "Data P.Alteração",
        "Hora Fechamento",
        "Hora Inclusão",
        "Hora U.Alteração",
        "Hora P.Alteração",
        "Status",
        "Modelo Interesse 1",
        "Máquina 1",
        "Quantidade 1",
        "Modelo Interesse 2",
        "Máquina 2",
        "Quantidade 2",
        "Modelo Interesse 3",
        "Máquina 3",
        "Quantidade 3",
        "Modelo Interesse 4",
        "Máquina 4",
        "Quantidade 4",
        "Modelo Interesse 5",
        "Máquina 5",
        "Quantidade 5",
        "Valor 1",
        "Valor 2",
        "Valor 3",
        "Valor 4",
        "Valor 5",
        "Etapa Funil",
        "Motivo de Andamento",
        "Motivo da Perda",
        "Usuário AJ",
        "Temperatura",
        "Evento Anterior",
        faturada,
        "Data Etapa",
        "Data do Faturamento",
        "Fatura esse mês ?",
        "Previsão Faturamento",
        -- Classificação de clientes baseada em regras de negócio
        CASE
            WHEN "Status" = 'SUCESSO' AND faturada = 'MAQUINA FATURADA' THEN 'CLIENTE_ATIVO'
            WHEN "Status" = 'SUCESSO' THEN 'CLIENTE_POTENCIAL'
            WHEN "Status" = 'ANDAMENTO' THEN 'PROSPECT'
            ELSE 'LEAD'
        END as "Classificacao_Cliente",
        -- Métricas calculadas
        EXTRACT(YEAR FROM "Data Inclusão") as "Ano_Inclusao",
        EXTRACT(MONTH FROM "Data Inclusão") as "Mes_Inclusao",
        CASE
            WHEN "Data Fechamento" IS NOT NULL
            THEN EXTRACT(EPOCH FROM ("Data Fechamento" - "Data Inclusão"))/86400
            ELSE NULL
        END as "Dias_Para_Fechamento"
    FROM base_maquinas
    """


def _get_bi_maquinas_pivotada_sql() -> str:
    """SQL da transformação bi_maquinas_pivotada (das DAGs V3) - OTIMIZADA para PostgreSQL"""
    return """
    CREATE TABLE dbdwcorporativo.silver_syonet_bi_maquinas_pivotada AS
    WITH unpivot_modelos AS (
        SELECT
            "Nº Evento",
            modelo_interesse AS "Modelo Interesse",
            ROW_NUMBER() OVER (PARTITION BY "Nº Evento" ORDER BY ordem) as ordem
        FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual
        CROSS JOIN LATERAL (
            SELECT unnest(ARRAY["Modelo Interesse 1", "Modelo Interesse 2", "Modelo Interesse 3", "Modelo Interesse 4", "Modelo Interesse 5"]) as modelo_interesse,
                   unnest(ARRAY[1, 2, 3, 4, 5]) as ordem
        ) AS modelos
        WHERE modelo_interesse IS NOT NULL AND TRIM(modelo_interesse) != ''
    ),
    unpivot_maquinas AS (
        SELECT
            "Nº Evento",
            maquina AS "Máquina",
            ROW_NUMBER() OVER (PARTITION BY "Nº Evento" ORDER BY ordem) as ordem
        FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual
        CROSS JOIN LATERAL (
            SELECT unnest(ARRAY["Máquina 1", "Máquina 2", "Máquina 3", "Máquina 4", "Máquina 5"]) as maquina,
                   unnest(ARRAY[1, 2, 3, 4, 5]) as ordem
        ) AS maquinas
        WHERE maquina IS NOT NULL AND TRIM(maquina) != ''
    ),
    unpivot_quantidades AS (
        SELECT
            "Nº Evento",
            quantidade AS "Quantidade",
            ROW_NUMBER() OVER (PARTITION BY "Nº Evento" ORDER BY ordem) as ordem
        FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual
        CROSS JOIN LATERAL (
            SELECT unnest(ARRAY["Quantidade 1", "Quantidade 2", "Quantidade 3", "Quantidade 4", "Quantidade 5"]) as quantidade,
                   unnest(ARRAY[1, 2, 3, 4, 5]) as ordem
        ) AS quantidades
        WHERE quantidade IS NOT NULL AND TRIM(quantidade) != ''
    ),
    unpivot_valores AS (
        SELECT
            "Nº Evento",
            valor AS "Valor",
            ROW_NUMBER() OVER (PARTITION BY "Nº Evento" ORDER BY ordem) as ordem
        FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual
        CROSS JOIN LATERAL (
            SELECT unnest(ARRAY["Valor 1", "Valor 2", "Valor 3", "Valor 4", "Valor 5"]) as valor,
                   unnest(ARRAY[1, 2, 3, 4, 5]) as ordem
        ) AS valores
        WHERE valor IS NOT NULL AND TRIM(valor) != '' AND valor != 'Não informado'
    )
    SELECT DISTINCT
        MQAT."Cliente",
        MQAT."CNPJ/CPF",
        MQAT."Cidade",
        MQAT."UF",
        MQAT."id_tipoevento",
        MQAT."Filial",
        MQAT."Nº Evento",
        MQAT."Data Fechamento",
        MQAT."Data Inclusão",
        MQAT."Data P.Acao",
        MQAT."Data U.Alteração",
        MQAT."Data P.Alteração",
        MQAT."Status",
        MQAT."Etapa Funil",
        MQAT."Motivo de Andamento",
        MQAT."Motivo da Perda",
        MQAT."Usuário AJ",
        MQAT."Temperatura",
        MQAT."Evento Anterior",
        MQAT.faturada,
        MQAT."Data Etapa",
        MQAT."Data do Faturamento",
        MQAT."Fatura esse mês ?",
        MQAT."Previsão Faturamento",
        -- Dados pivotados
        UM."Modelo Interesse",
        UMA."Máquina",
        UQ."Quantidade",
        UV."Valor",
        -- Métricas adicionais
        EXTRACT(YEAR FROM MQAT."Data Inclusão") as "Ano",
        EXTRACT(MONTH FROM MQAT."Data Inclusão") as "Mes",
        EXTRACT(DAY FROM MQAT."Data Inclusão") as "Dia"
    FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual MQAT
    LEFT JOIN unpivot_modelos UM ON UM."Nº Evento" = MQAT."Nº Evento"
    LEFT JOIN unpivot_maquinas UMA ON UMA."Nº Evento" = MQAT."Nº Evento" AND UMA.ordem = UM.ordem
    LEFT JOIN unpivot_quantidades UQ ON UQ."Nº Evento" = MQAT."Nº Evento" AND UQ.ordem = UM.ordem
    LEFT JOIN unpivot_valores UV ON UV."Nº Evento" = MQAT."Nº Evento" AND UV.ordem = UM.ordem
    WHERE UM."Modelo Interesse" IS NOT NULL
    """
