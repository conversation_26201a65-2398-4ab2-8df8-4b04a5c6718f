"""
Silver Transformation - ETL Framework V4 (Refatorado)

Define a estrutura de transformações Silver sem SQL hardcoded.
Todo SQL é carregado de arquivos .sql via SqlLoader.
"""

from dataclasses import dataclass, field
from typing import List, Dict, Optional
from enum import Enum


class TransformationFrequency(Enum):
    """Frequência de execução das transformações"""
    DAILY = "daily"
    WEEKLY = "weekly"
    MONTHLY = "monthly"
    ON_DEMAND = "on_demand"


@dataclass
class SilverTransformation:
    """
    Representa uma transformação Silver no Data Warehouse.
    
    SQL é carregado de arquivos externos, não hardcoded aqui.
    """
    name: str
    description: str
    sql: str  # Carregado de arquivo .sql externo
    frequency: TransformationFrequency = TransformationFrequency.DAILY
    timeout_seconds: int = 600
    dependencies: List[str] = field(default_factory=list)
    grants: List[str] = field(default_factory=list)
    tags: List[str] = field(default_factory=list)
    owner: str = "data-engineering"
    drop_if_exists: bool = True
    
    @property
    def task_id(self) -> str:
        """ID único da task no Airflow"""
        return f"silver_{self.name}"
    
    @property
    def table_name(self) -> str:
        """Nome completo da tabela no DW"""
        # Será definido pelo SqlLoader baseado no sistema
        return f"silver_{self.name}"
    
    def get_bronze_dependencies(self) -> List[str]:
        """
        Extrai dependências bronze do SQL.
        
        Returns:
            Lista de tabelas bronze referenciadas
        """
        import re
        bronze_tables = []
        
        # Padrão para encontrar tabelas bronze
        pattern = r'bronze_\w+_(\w+)'
        matches = re.findall(pattern, self.sql.lower())
        
        for match in matches:
            table_name = match
            if table_name not in bronze_tables:
                bronze_tables.append(table_name)
        
        return bronze_tables
    
    def get_silver_dependencies(self) -> List[str]:
        """
        Retorna apenas dependências silver (entre transformações).

        Dependências bronze são implícitas e gerenciadas automaticamente.
        """
        # Filtra apenas dependências que começam com 'silver_' ou estão na lista de transformações
        return [dep for dep in self.dependencies if not dep.startswith('bronze_')]

    def should_execute_today(self) -> bool:
        """
        Verifica se a transformação deve ser executada hoje baseada na frequência.

        Returns:
            True se deve executar hoje, False caso contrário
        """
        from datetime import datetime

        if self.frequency == TransformationFrequency.DAILY:
            return True
        elif self.frequency == TransformationFrequency.ON_DEMAND:
            return True
        elif self.frequency == TransformationFrequency.WEEKLY:
            # Executa apenas às segundas-feiras (weekday() == 0)
            return datetime.now().weekday() == 0
        elif self.frequency == TransformationFrequency.MONTHLY:
            # Executa apenas no primeiro dia do mês
            return datetime.now().day == 1
        else:
            # Por padrão, executa (para compatibilidade)
            return True
    
    def validate(self) -> List[str]:
        """
        Valida a transformação.
        
        Returns:
            Lista de erros encontrados
        """
        errors = []
        
        if not self.name:
            errors.append("Nome da transformação é obrigatório")
        
        if not self.sql:
            errors.append("SQL da transformação é obrigatório")
        
        if self.timeout_seconds < 60:
            errors.append("Timeout deve ser pelo menos 60 segundos")
        
        # Valida que SQL contém CREATE TABLE
        if "create table" not in self.sql.lower():
            errors.append("SQL deve conter CREATE TABLE")
        
        return errors
    
    def __repr__(self) -> str:
        return f"SilverTransformation(name='{self.name}', deps={len(self.dependencies)})"


class TransformationRegistry:
    """
    Registro central de transformações Silver.
    
    Substitui as funções create_*_transformations() por um registro dinâmico.
    """
    
    def __init__(self):
        self._transformations: Dict[str, List[SilverTransformation]] = {}
    
    def register_system_transformations(self, system_name: str, transformations: List[SilverTransformation]):
        """Registra transformações de um sistema"""
        self._transformations[system_name] = transformations
    
    def get_system_transformations(self, system_name: str) -> List[SilverTransformation]:
        """Obtém transformações de um sistema"""
        return self._transformations.get(system_name, [])
    
    def get_all_systems(self) -> List[str]:
        """Lista todos os sistemas registrados"""
        return list(self._transformations.keys())
    
    def clear(self):
        """Limpa o registro (útil para testes)"""
        self._transformations.clear()


# Instância global do registro
transformation_registry = TransformationRegistry()


def validate_transformation_dependencies(transformations: List[SilverTransformation]) -> List[str]:
    """
    Valida dependências entre transformações.
    
    Args:
        transformations: Lista de transformações
        
    Returns:
        Lista de erros encontrados
    """
    errors = []
    transformation_names = {t.name for t in transformations}
    
    for transformation in transformations:
        for dep in transformation.get_silver_dependencies():
            if dep not in transformation_names:
                errors.append(
                    f"Transformação '{transformation.name}' depende de '{dep}' que não existe"
                )
    
    # Detecta dependências circulares
    for transformation in transformations:
        if _has_circular_dependency(transformation, transformations):
            errors.append(
                f"Transformação '{transformation.name}' tem dependência circular"
            )
    
    return errors


def _has_circular_dependency(
    transformation: SilverTransformation, 
    all_transformations: List[SilverTransformation],
    visited: Optional[set] = None
) -> bool:
    """Detecta dependências circulares"""
    if visited is None:
        visited = set()
    
    if transformation.name in visited:
        return True
    
    visited.add(transformation.name)
    
    # Mapeia transformações por nome
    trans_map = {t.name: t for t in all_transformations}
    
    for dep in transformation.get_silver_dependencies():
        if dep in trans_map:
            if _has_circular_dependency(trans_map[dep], all_transformations, visited.copy()):
                return True
    
    return False


def sort_transformations_by_dependencies(transformations: List[SilverTransformation]) -> List[SilverTransformation]:
    """
    Ordena transformações respeitando dependências.
    
    Args:
        transformations: Lista de transformações
        
    Returns:
        Lista ordenada por dependências
    """
    sorted_transformations = []
    remaining = transformations.copy()
    
    while remaining:
        # Encontra transformações sem dependências pendentes
        ready = []
        for t in remaining:
            silver_deps = t.get_silver_dependencies()
            if all(dep in [st.name for st in sorted_transformations] for dep in silver_deps):
                ready.append(t)
        
        if not ready:
            # Dependência circular ou erro
            print(f"Aviso: Possível dependência circular. Adicionando restantes: {[t.name for t in remaining]}")
            sorted_transformations.extend(remaining)
            break
        
        # Adiciona as prontas
        sorted_transformations.extend(ready)
        for t in ready:
            remaining.remove(t)
    
    return sorted_transformations