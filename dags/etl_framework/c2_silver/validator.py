"""
Silver Validator - ETL Framework

Validador para verificar integridade de transformações silver.
"""

import logging
from typing import Dict, Any, <PERSON><PERSON>, List
from etl_framework.connections.postgres import PostgreSQLConnection
from etl_framework.config.system_config import SystemConfig
from etl_framework.config.dw_config import DWConfig
from etl_framework.utils.logger import ETLLogger


class SilverValidator:
    """
    Validador para tabelas Silver
    
    Verifica:
    - Contagem de registros das transformações
    - Integridade de dados transformados
    - Dependências entre tabelas silver
    """
    
    def __init__(self, 
                 dw_connection: PostgreSQLConnection,
                 system_config: SystemConfig,
                 dw_config: DWConfig):
        
        self.dw_connection = dw_connection
        self.system_config = system_config
        self.dw_config = dw_config
        self.logger = ETLLogger(f"SilverValidator_{system_config.name}")
    
    def validate_transformation(self, transformation_name: str) -> Tuple[bool, Dict[str, Any]]:
        """
        Valida uma transformação silver
        
        Returns:
            Tuple[bool, Dict]: (is_valid, validation_info)
        """
        try:
            # Conta registros na tabela silver
            silver_count = self._count_silver_records(transformation_name)
            
            # Validação básica
            is_valid = silver_count > 0
            
            validation_info = {
                'silver_count': silver_count,
                'has_data': silver_count > 0,
                'transformation_name': transformation_name
            }
            
            return is_valid, validation_info
            
        except Exception as e:
            self.logger.error(f"Erro na validação de {transformation_name}: {str(e)}")
            return False, {'error': str(e)}
    
    def _count_silver_records(self, transformation_name: str) -> int:
        """Conta registros na tabela silver"""
        try:
            silver_table = f"{self.dw_config.silver_schema}.{self.system_config.silver_prefix}{transformation_name}"
            query = f"SELECT COUNT(*) FROM {silver_table}"
            
            with self.dw_connection.get_cursor() as (cursor, conn):
                cursor.execute(query)
                result = cursor.fetchone()
                return result[0] if result else 0
                
        except Exception as e:
            self.logger.warning(f"Erro ao contar registros silver para {transformation_name}: {str(e)}")
            return 0
    
    def validate_dependencies(self, transformation_name: str, dependencies: List[str]) -> bool:
        """
        Valida se as dependências de uma transformação existem e têm dados
        """
        try:
            for dep in dependencies:
                dep_count = self._count_silver_records(dep)
                if dep_count == 0:
                    self.logger.warning(f"Dependência {dep} para {transformation_name} está vazia")
                    return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"Erro na validação de dependências para {transformation_name}: {str(e)}")
            return False