"""
<PERSON><PERSON><PERSON><PERSON> Silver - ETL Framework

Processamento da camada Silver (transformações CREATE TABLE AS dentro do DW).

A camada Silver é responsável por:
- Executar transformações complexas dentro do Data Warehouse
- Criar tabelas enriquecidas usando CREATE TABLE AS
- Aplicar regras de negócio específicas de cada sistema
- Consolidar dados de múltiplas tabelas bronze
- Preparar dados para consumo analítico

Características:
- Execução dentro do DW (PostgreSQL)
- Queries SQL complexas com CTEs, JOINs, agregações
- Dependências entre tabelas silver
- Validação de resultados
- Aplicação de permissões automáticas

Componentes:
- SilverProcessor: Processador principal da camada silver
- SilverTransformation: Definição de transformações
- SilverValidator: Validação de dados transformados
"""

from etl_framework.c2_silver.processor import SilverProcessor, create_silver_processor
from etl_framework.c2_silver.transformation import SilverTransformation
from etl_framework.c2_silver.validator import SilverValidator

__all__ = [
    'SilverProcessor',
    'create_silver_processor',  # Factory function
    'SilverTransformation',
    'SilverValidator'
]
