"""
ETL Framework - Sistema de ETL Corporativo

Este framework abstrai a lógica de ETL das DAGs V3 para criar um sistema reutilizável
que pode ser aplicado a diferentes sistemas de origem (Oracle, PostgreSQL, SQL Server)
com destino único no Data Warehouse Corporativo.

Arquitetura:
- Bronze: Extração bruta dos sistemas de origem para o DW
- Silver: Transformações CREATE TABLE AS dentro do DW
- Target único: Data Warehouse Corporativo (PostgreSQL)
- Sources múltiplos: Diferentes SGBDs com sintaxes específicas

Estrutura:
- connections/: Gerenciamento de conexões para diferentes SGBDs de origem
- bronze/: Processamento de camada bronze (extração bruta)
- silver/: Processamento de camada silver (transformações no DW)
- config/: Sistema de configuração flexível por sistema
- utils/: Utilitários diversos (logs, validação, etc.)
- systems/: Configurações específicas por sistema de origem

Autor: Data Engineering Team
Versão: 2.0.0
"""

__version__ = "1.0.0"
__author__ = "Data Engineering Team"

# Imports principais para facilitar uso - SEMPRE usar importações absolutas
from etl_framework.connections.base import DatabaseConnection
from etl_framework.c1_bronze.processor import BronzeProcessor, create_bronze_processor
from etl_framework.c2_silver.processor import SilverProcessor, create_silver_processor
from etl_framework.config.system_config import SystemConfig
from etl_framework.config.dw_config import DWConfig, create_corporate_dw_config
from etl_framework.utils.logger import ETLLogger

# Factory functions para facilitar uso
from etl_framework.systems.syonet.config import create_syonet_system_config

__all__ = [
    # Classes principais
    'DatabaseConnection',
    'BronzeProcessor',
    'SilverProcessor',
    'SystemConfig',
    'DWConfig',
    'ETLLogger',

    # Factory functions (API padronizada)
    'create_bronze_processor',
    'create_silver_processor',
    'create_corporate_dw_config',
    'create_syonet_system_config'
]
