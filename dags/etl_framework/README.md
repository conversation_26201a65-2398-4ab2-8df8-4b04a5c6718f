# ETL Framework V4

## Visão Geral

Sistema ETL corporativo que automatiza extração, transformação e carregamento de dados de múltiplos sistemas para um Data Warehouse único.

**Problema Resolvido**: Elimina 2000+ linhas de código duplicado por sistema, gerando DAGs automaticamente.

## Arquitetura

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Syonet        │    │   Oracle ERP    │    │   Sistema X     │
│  (SQL Server)   │    │   (Oracle)      │    │ (PostgreSQL)    │
└─────────┬───────┘    └─────────┬───────┘    └─────────┬───────┘
          │                      │                      │
          └──────────────────────┼──────────────────────┘
                                 ▼
                    ┌─────────────────────────┐
                    │   Data Warehouse        │
                    │   Corporativo           │
                    │   (PostgreSQL)          │
                    │                         │
                    │  Bronze → Silver        │
                    └─────────────────────────┘
```

**Características**:
- **Múltiplas Origens**: Suporta SQL Server, Oracle, PostgreSQL
- **Destino Único**: Data Warehouse Corporativo (PostgreSQL)
- **Bronze**: Dados brutos extraídos sem transformação
- **Silver**: Transformações CREATE TABLE AS dentro do DW

## Como Funciona

### 1. DAGs Geradas Automaticamente

```python
# Arquivo: v4_bronze.py e v4_silver.py
SYSTEMS_CONFIG = {
    'syonet': {
        'description': 'Sistema Syonet - CRM/Vendas',
        'schedule_interval': '0 6 * * *',
        'tags': ['syonet', 'bronze', 'sqlserver']
    }
    # Adicionar novos sistemas aqui gera DAGs automaticamente
}
```

**Resultado**: DAGs `V4_BRONZE_SYONET` e `V4_SILVER_SYONET` criadas automaticamente

### 2. Processamento Paralelo

- **Bronze**: Todas as tabelas processadas em paralelo
- **Silver**: Respeita dependências entre transformações

### 3. Estratégias ETL Automáticas

- **Tabelas Pequenas**: Full Load (drop/create)
- **Tabelas Grandes**: Smart Incremental com fallback automático
- **Fallback**: Se incremental falha, executa full load

## Estrutura do Projeto

```
etl_framework/
├── bronze/                     # Camada Bronze (extração)
├── silver/                     # Camada Silver (transformações)
├── config/                     # Configurações centralizadas
├── connections/                # Conexões com SGBDs
├── strategies/                 # Estratégias de ETL
├── systems/                    # Configurações específicas
└── utils/                      # Utilitários

# DAGs Principais
v4_bronze.py                    # Gera DAGs bronze
v4_silver.py                    # Gera DAGs silver
```

## Como Usar

### 1. Sistema Existente (Syonet)
```bash
# DAGs já configuradas:
# - V4_BRONZE_SYONET (6h da manhã)
# - V4_SILVER_SYONET (8h da manhã)
```

### 2. Adicionar Novo Sistema

**Passo 1**: Criar configuração do sistema
```python
# etl_framework/systems/oracle_erp/config.py
def create_oracle_erp_system_config(dw_config: DWConfig) -> SystemConfig:
    # Configurar conexão e tabelas
    return system_config
```

**Passo 2**: Adicionar aos geradores de DAG
```python
# v4_bronze.py e v4_silver.py
SYSTEMS_CONFIG = {
    'syonet': { ... },
    'oracle_erp': {
        'description': 'Sistema Oracle ERP',
        'schedule_interval': '0 7 * * *',
        'tags': ['oracle', 'bronze', 'erp']
    }
}
```

**Resultado**: DAGs geradas automaticamente

### 3. Validar Framework
```bash
cd /home/<USER>/projects/airflow-v1/dags
python3 etl_framework/validate_framework.py
```

## Benefícios

1. **Escalabilidade**: Um framework para N sistemas
2. **Manutenção Centralizada**: Correção em um lugar beneficia todos
3. **Configuração Declarativa**: Apenas configuração, sem código
4. **Performance**: Processamento paralelo automático

## Métricas de Sucesso

- **Redução de código**: 90% (2000 → 200 linhas por DAG)
- **Tempo de desenvolvimento**: 95% redução para novos sistemas
- **Performance**: 10x mais rápido com processamento paralelo
- **Manutenibilidade**: Centralizada em um framework

## Status Atual

**Implementado e Funcionando**:
- Framework V4 completo
- Sistema Syonet configurado (35 tabelas bronze, 4 transformações silver)
- DAGs V4_BRONZE_SYONET e V4_SILVER_SYONET funcionais
- Processamento paralelo implementado
- Validação automática

**Próximos Sistemas**:
- Oracle ERP (estrutura pronta, apenas configurar)
- Sistema X PostgreSQL (estrutura pronta, apenas configurar)