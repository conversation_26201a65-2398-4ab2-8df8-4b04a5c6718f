"""
Gerenciador de Sessões PostgreSQL - ETL Framework

Módulo responsável por detectar e matar automaticamente sessões que estejam
impedindo a execução das rotinas ETL.
"""

import logging
from typing import List, Dict, Any, Optional
from datetime import datetime, timedelta
from ..connections.postgres import PostgreSQLConnection
from ..config.database_config import DatabaseConfig

logger = logging.getLogger(__name__)


class SessionManager:
    """
    Gerenciador de sessões PostgreSQL com capacidade de matar sessões problemáticas
    """
    
    def __init__(self, db_connection: PostgreSQLConnection):
        self.db_connection = db_connection
        self.etl_user = 'bq_dwcorporativo_u'  # Usuário das DAGs ETL
        
    def get_active_etl_sessions(self) -> List[Dict[str, Any]]:
        """
        Retorna todas as sessões ativas do usuário ETL
        
        Returns:
            List[Dict]: Lista de sessões ativas com informações detalhadas
        """
        query = """
        SELECT 
            pid,
            usename,
            application_name,
            client_addr,
            state,
            now() - query_start AS duration,
            now() - state_change AS state_duration,
            wait_event_type,
            wait_event,
            LEFT(query, 200) as query_preview,
            query_start,
            state_change
        FROM pg_stat_activity 
        WHERE usename = %s
          AND state != 'idle'
          AND pid != pg_backend_pid()
        ORDER BY query_start ASC
        """
        
        try:
            with self.db_connection.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, (self.etl_user,))
                    columns = [desc[0] for desc in cursor.description]
                    results = cursor.fetchall()
                    
                    sessions = []
                    for row in results:
                        session = dict(zip(columns, row))
                        sessions.append(session)
                    
                    return sessions
                    
        except Exception as e:
            logger.error(f"Erro ao obter sessões ativas: {str(e)}")
            return []
    
    def get_blocked_sessions(self) -> List[Dict[str, Any]]:
        """
        Retorna sessões que estão bloqueadas por locks
        
        Returns:
            List[Dict]: Lista de sessões bloqueadas
        """
        query = """
        SELECT 
            blocked_locks.pid AS blocked_pid,
            blocked_activity.usename AS blocked_user,
            blocking_locks.pid AS blocking_pid,
            blocking_activity.usename AS blocking_user,
            blocked_locks.locktype,
            blocked_locks.mode AS blocked_mode,
            blocking_locks.mode AS blocking_mode,
            now() - blocked_activity.query_start AS blocked_duration,
            LEFT(blocked_activity.query, 150) AS blocked_query,
            LEFT(blocking_activity.query, 150) AS blocking_query
        FROM pg_catalog.pg_locks blocked_locks
        JOIN pg_catalog.pg_stat_activity blocked_activity ON blocked_activity.pid = blocked_locks.pid
        JOIN pg_catalog.pg_locks blocking_locks 
            ON blocking_locks.locktype = blocked_locks.locktype
            AND blocking_locks.database IS NOT DISTINCT FROM blocked_locks.database
            AND blocking_locks.relation IS NOT DISTINCT FROM blocked_locks.relation
            AND blocking_locks.pid != blocked_locks.pid
        JOIN pg_catalog.pg_stat_activity blocking_activity ON blocking_activity.pid = blocking_locks.pid
        WHERE NOT blocked_locks.granted
          AND (blocked_activity.usename = %s OR blocking_activity.usename = %s)
        ORDER BY blocked_duration DESC
        """
        
        try:
            with self.db_connection.get_connection() as conn:
                with conn.cursor() as cursor:
                    cursor.execute(query, (self.etl_user, self.etl_user))
                    columns = [desc[0] for desc in cursor.description]
                    results = cursor.fetchall()
                    
                    blocked_sessions = []
                    for row in results:
                        session = dict(zip(columns, row))
                        blocked_sessions.append(session)
                    
                    return blocked_sessions
                    
        except Exception as e:
            logger.error(f"Erro ao obter sessões bloqueadas: {str(e)}")
            return []
    
    def identify_problematic_sessions(self, 
                                    max_duration_minutes: int = 10,
                                    max_duplicate_queries: int = 2) -> List[Dict[str, Any]]:
        """
        Identifica sessões problemáticas que devem ser mortas
        
        Args:
            max_duration_minutes: Duração máxima permitida para uma sessão (padrão: 10 min)
            max_duplicate_queries: Número máximo de consultas duplicadas permitidas (padrão: 2)
            
        Returns:
            List[Dict]: Lista de sessões problemáticas
        """
        active_sessions = self.get_active_etl_sessions()
        blocked_sessions = self.get_blocked_sessions()
        problematic_sessions = []
        
        # 1. Sessões rodando há muito tempo
        max_duration = timedelta(minutes=max_duration_minutes)
        for session in active_sessions:
            if session['duration'] and session['duration'] > max_duration:
                session['problem_type'] = 'LONG_RUNNING'
                session['problem_description'] = f"Rodando há {session['duration']}"
                problematic_sessions.append(session)
        
        # 2. Sessões bloqueadas
        for session in blocked_sessions:
            session['problem_type'] = 'BLOCKED'
            session['problem_description'] = f"Bloqueada por PID {session['blocking_pid']}"
            session['pid'] = session['blocked_pid']  # Padronizar campo pid
            problematic_sessions.append(session)
        
        # 3. Consultas duplicadas (múltiplas execuções da mesma query)
        query_counts = {}
        for session in active_sessions:
            query_key = self._normalize_query(session.get('query_preview', ''))
            if query_key:
                if query_key not in query_counts:
                    query_counts[query_key] = []
                query_counts[query_key].append(session)
        
        for query_key, sessions in query_counts.items():
            if len(sessions) > max_duplicate_queries:
                # Manter apenas a sessão mais antiga, marcar as outras como problemáticas
                sessions_sorted = sorted(sessions, key=lambda x: x['query_start'] or datetime.min)
                for session in sessions_sorted[1:]:  # Pular a primeira (mais antiga)
                    session['problem_type'] = 'DUPLICATE'
                    session['problem_description'] = f"Consulta duplicada ({len(sessions)} execuções)"
                    problematic_sessions.append(session)
        
        return problematic_sessions
    
    def _normalize_query(self, query: str) -> str:
        """
        Normaliza query para identificar duplicatas
        
        Args:
            query: Query SQL
            
        Returns:
            str: Query normalizada
        """
        if not query:
            return ""
        
        # Remove espaços extras e converte para minúsculo
        normalized = ' '.join(query.lower().split())
        
        # Identifica queries das DAGs específicas
        if 'tb_oportunidades_base' in normalized:
            return 'tb_oportunidades_base'
        elif 'tb_im_assinatura' in normalized:
            return 'tb_im_assinatura'
        elif 'silver_syonet' in normalized:
            return 'silver_syonet_generic'
        
        # Para outras queries, usa os primeiros 50 caracteres
        return normalized[:50]
    
    def kill_session(self, pid: int, reason: str = "") -> bool:
        """
        Mata uma sessão específica usando a função dbdwcorporativo.kill_backend
        
        Args:
            pid: PID da sessão a ser morta
            reason: Motivo para matar a sessão
            
        Returns:
            bool: True se a sessão foi morta com sucesso
        """
        try:
            with self.db_connection.get_connection() as conn:
                with conn.cursor() as cursor:
                    # Usar a função específica fornecida pelo administrador
                    cursor.execute("SELECT dbdwcorporativo.kill_backend(%s)", (pid,))
                    result = cursor.fetchone()
                    
                    success = result[0] if result else False
                    
                    if success:
                        logger.warning(f"Sessão PID {pid} morta com sucesso. Motivo: {reason}")
                    else:
                        logger.error(f"Falha ao matar sessão PID {pid}. Motivo: {reason}")
                    
                    return success
                    
        except Exception as e:
            logger.error(f"Erro ao matar sessão PID {pid}: {str(e)}")
            return False
    
    def kill_problematic_sessions(self, 
                                max_duration_minutes: int = 10,
                                max_duplicate_queries: int = 2,
                                dry_run: bool = False) -> Dict[str, Any]:
        """
        Identifica e mata automaticamente sessões problemáticas
        
        Args:
            max_duration_minutes: Duração máxima permitida para uma sessão
            max_duplicate_queries: Número máximo de consultas duplicadas permitidas
            dry_run: Se True, apenas identifica mas não mata as sessões
            
        Returns:
            Dict: Relatório das ações executadas
        """
        logger.info("Iniciando verificação de sessões problemáticas...")
        
        problematic_sessions = self.identify_problematic_sessions(
            max_duration_minutes=max_duration_minutes,
            max_duplicate_queries=max_duplicate_queries
        )
        
        report = {
            'total_problematic': len(problematic_sessions),
            'killed_sessions': [],
            'failed_kills': [],
            'dry_run': dry_run,
            'timestamp': datetime.now().isoformat()
        }
        
        if not problematic_sessions:
            logger.info("Nenhuma sessão problemática encontrada")
            return report
        
        logger.warning(f"Encontradas {len(problematic_sessions)} sessões problemáticas")
        
        for session in problematic_sessions:
            pid = session.get('pid')
            problem_type = session.get('problem_type', 'UNKNOWN')
            problem_desc = session.get('problem_description', 'Sem descrição')
            
            if not pid:
                continue
            
            reason = f"{problem_type}: {problem_desc}"
            
            if dry_run:
                logger.info(f"[DRY RUN] Mataria sessão PID {pid} - {reason}")
                report['killed_sessions'].append({
                    'pid': pid,
                    'reason': reason,
                    'action': 'DRY_RUN'
                })
            else:
                logger.warning(f"Matando sessão PID {pid} - {reason}")
                
                if self.kill_session(pid, reason):
                    report['killed_sessions'].append({
                        'pid': pid,
                        'reason': reason,
                        'action': 'KILLED'
                    })
                else:
                    report['failed_kills'].append({
                        'pid': pid,
                        'reason': reason,
                        'action': 'FAILED'
                    })
        
        logger.info(f"Relatório: {len(report['killed_sessions'])} mortas, {len(report['failed_kills'])} falharam")
        
        return report


def create_session_manager(dw_config) -> SessionManager:
    """
    Factory function para criar SessionManager
    
    Args:
        dw_config: Configuração do Data Warehouse
        
    Returns:
        SessionManager: Instância configurada
    """
    from ..connections.postgres import PostgreSQLConnection
    
    postgres_conn = PostgreSQLConnection(dw_config.connection)
    return SessionManager(postgres_conn)
