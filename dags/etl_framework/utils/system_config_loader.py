"""
SystemConfigLoader - ETL Framework V4

Carrega configurações de sistema a partir de system_config.yaml.
Especializado em conexões de banco, metadados e configurações globais.
Separado de bronze/silver para manter responsabilidades claras.
"""

import yaml
import os
from typing import Dict, Any, Optional
from dataclasses import dataclass
from ..config.database_config import DatabaseConfig, DatabaseType


@dataclass
class SystemMetadata:
    """Metadados do sistema"""
    name: str
    description: str
    tags: list
    default_timeout_seconds: int
    max_parallel_tables: int
    enable_smart_incremental: bool
    enable_validation: bool
    max_retries: int
    retry_delay_minutes: int


@dataclass
class PerformanceConfig:
    """Configurações de performance"""
    chunk_size: int
    connection_pool_size: int
    enable_connection_sharing: bool
    enable_lazy_loading: bool
    validation_cache_ttl: int


@dataclass
class ETLSettings:
    """Configurações de ETL específicas"""
    default_incremental_filters: Dict[str, str]
    default_validation: Dict[str, Any]


@dataclass
class SchedulingConfig:
    """Configurações de agendamento"""
    bronze_schedule: str
    silver_schedule: str
    timezone: str


@dataclass
class SystemConfigData:
    """Dados completos de configuração do sistema"""
    metadata: SystemMetadata
    source_database: Dict[str, Any]
    target_database: Dict[str, str]
    performance: PerformanceConfig
    etl_settings: ETLSettings
    scheduling: SchedulingConfig


class SystemConfigLoader:
    """
    Loader especializado para configurações de sistema.
    
    Responsável por:
    - Carregar system_config.yaml
    - Criar DatabaseConfig para conexões
    - Fornecer metadados e configurações globais
    - Manter separação clara de responsabilidades
    """
    
    def __init__(self):
        self.systems_path = "/home/<USER>/projects/airflow-v1/dags/etl_framework/systems"
    
    def load_system_config(self, system_name: str) -> SystemConfigData:
        """
        Carrega configuração completa do sistema.
        
        Args:
            system_name: Nome do sistema (e.g., 'syonet')
            
        Returns:
            SystemConfigData com todas as configurações
        """
        config_path = os.path.join(self.systems_path, system_name, "system_config.yaml")
        
        if not os.path.exists(config_path):
            raise FileNotFoundError(f"system_config.yaml não encontrado para sistema '{system_name}' em {config_path}")
        
        with open(config_path, 'r', encoding='utf-8') as file:
            config_data = yaml.safe_load(file)
        
        return self._parse_system_config(config_data)
    
    def create_source_database_config(self, system_name: str) -> DatabaseConfig:
        """
        Cria DatabaseConfig para a base de dados origem.
        
        Args:
            system_name: Nome do sistema
            
        Returns:
            DatabaseConfig configurado para a origem
        """
        system_config = self.load_system_config(system_name)
        db_config = system_config.source_database
        
        # Mapear string para enum
        db_type_map = {
            'sqlserver': DatabaseType.SQLSERVER,
            'oracle': DatabaseType.ORACLE,
            'postgresql': DatabaseType.POSTGRESQL
        }
        
        return DatabaseConfig(
            name=db_config['name'],
            db_type=db_type_map[db_config['type'].lower()],
            host=db_config['host'],
            port=db_config['port'],
            database=db_config['database'],
            user=db_config['user'],
            password=db_config['password'],
            timeout=db_config.get('default_timeout', 300)
        )
    
    def get_system_metadata(self, system_name: str) -> SystemMetadata:
        """
        Obtém metadados do sistema.
        
        Args:
            system_name: Nome do sistema
            
        Returns:
            SystemMetadata com configurações do sistema
        """
        system_config = self.load_system_config(system_name)
        return system_config.metadata
    
    def get_performance_config(self, system_name: str) -> PerformanceConfig:
        """
        Obtém configurações de performance.
        
        Args:
            system_name: Nome do sistema
            
        Returns:
            PerformanceConfig com configurações de performance
        """
        system_config = self.load_system_config(system_name)
        return system_config.performance
    
    def get_etl_settings(self, system_name: str) -> ETLSettings:
        """
        Obtém configurações de ETL.
        
        Args:
            system_name: Nome do sistema
            
        Returns:
            ETLSettings com configurações de ETL
        """
        system_config = self.load_system_config(system_name)
        return system_config.etl_settings
    
    def get_scheduling_config(self, system_name: str) -> SchedulingConfig:
        """
        Obtém configurações de agendamento.
        
        Args:
            system_name: Nome do sistema
            
        Returns:
            SchedulingConfig com configurações de agendamento
        """
        system_config = self.load_system_config(system_name)
        return system_config.scheduling
    
    def get_target_schema_info(self, system_name: str) -> Dict[str, str]:
        """
        Obtém informações do schema de destino.
        
        Args:
            system_name: Nome do sistema
            
        Returns:
            Dict com schema e prefix do destino
        """
        system_config = self.load_system_config(system_name)
        return system_config.target_database
    
    def get_schema_mapping(self, system_name: str) -> Dict[str, str]:
        """
        Obtém mapeamento de schemas entre origem e destino.
        
        Args:
            system_name: Nome do sistema
            
        Returns:
            Dict com mapeamento de schemas
        """
        system_config = self.load_system_config(system_name)
        source_db = system_config.source_database
        
        return {
            'source_schema': source_db.get('default_schema', 'dbo'),
            'openquery_schema': source_db.get('openquery_schema', 'public'),
            'target_schema': system_config.target_database['schema'],
            'table_prefix': system_config.target_database['table_prefix']
        }
    
    def _parse_system_config(self, config_data: Dict[str, Any]) -> SystemConfigData:
        """
        Parse dos dados do YAML para dataclasses.
        
        Args:
            config_data: Dados do YAML
            
        Returns:
            SystemConfigData parseado
        """
        system_data = config_data['system']
        
        # Parse metadata
        metadata = SystemMetadata(
            name=system_data['name'],
            description=system_data['description'],
            tags=system_data['tags'],
            default_timeout_seconds=system_data['default_timeout_seconds'],
            max_parallel_tables=system_data['max_parallel_tables'],
            enable_smart_incremental=system_data['enable_smart_incremental'],
            enable_validation=system_data['enable_validation'],
            max_retries=system_data['max_retries'],
            retry_delay_minutes=system_data['retry_delay_minutes']
        )
        
        # Parse performance
        perf_data = config_data['performance']
        performance = PerformanceConfig(
            chunk_size=perf_data['chunk_size'],
            connection_pool_size=perf_data['connection_pool_size'],
            enable_connection_sharing=perf_data['enable_connection_sharing'],
            enable_lazy_loading=perf_data['enable_lazy_loading'],
            validation_cache_ttl=perf_data['validation_cache_ttl']
        )
        
        # Parse ETL settings
        etl_data = config_data['etl_settings']
        etl_settings = ETLSettings(
            default_incremental_filters=etl_data['default_incremental_filters'],
            default_validation=etl_data['default_validation']
        )
        
        # Parse scheduling
        sched_data = config_data['scheduling']
        scheduling = SchedulingConfig(
            bronze_schedule=sched_data['bronze_schedule'],
            silver_schedule=sched_data['silver_schedule'],
            timezone=sched_data['timezone']
        )
        
        return SystemConfigData(
            metadata=metadata,
            source_database=config_data['source_database'],
            target_database=config_data['target_database'],
            performance=performance,
            etl_settings=etl_settings,
            scheduling=scheduling
        )


def create_system_config_loader() -> SystemConfigLoader:
    """Factory function para criar SystemConfigLoader"""
    return SystemConfigLoader()