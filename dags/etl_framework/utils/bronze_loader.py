"""
Bronze Loader - ETL Framework V4

Utilitário especializado para carregar APENAS configurações de tabelas bronze.
Responsabilidades de conexão foram movidas para SystemConfigLoader.
Mantém separação clara de responsabilidades.
"""

import os
import yaml
from typing import Dict, List, Optional
from pathlib import Path
from dataclasses import dataclass
from ..config.table_config import TableConfig, TableType, IncrementalMode


@dataclass
class BronzeTablesConfig:
    """Configuração de tabelas bronze (sem dados de conexão)"""
    tables: Dict[str, Dict]


class BronzeLoader:
    """
    Carregador especializado para configurações de tabelas bronze.
    
    Responsável APENAS por:
    - Carregar bronze_config.yaml 
    - Criar TableConfig objects
    - Não gerencia conexões (isso fica com SystemConfigLoader)
    
    Estrutura esperada:
    systems/[system_name]/
    ├── system_config.yaml      # Configurações de sistema/conexão
    ├── bronze_config.yaml      # Configurações de tabelas bronze
    └── config.py               # Factory usando ambos loaders
    """
    
    def __init__(self, base_path: str = None):
        if base_path is None:
            # Automaticamente detecta o caminho base do framework
            current_dir = Path(__file__).parent.parent
            self.base_path = current_dir / "systems"
        else:
            self.base_path = Path(base_path)
    
    def load_bronze_tables_config(self, system_name: str) -> BronzeTablesConfig:
        """
        Carrega APENAS configurações de tabelas bronze.
        
        Args:
            system_name: Nome do sistema (ex: 'syonet', 'oracle_erp')
            
        Returns:
            BronzeTablesConfig com configurações de tabelas
        """
        system_path = self.base_path / system_name
        config_file = system_path / "bronze_config.yaml"
        
        if not config_file.exists():
            raise FileNotFoundError(f"Arquivo bronze_config.yaml não encontrado em {system_path}")
        
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)
            
            return BronzeTablesConfig(
                tables=config_data.get('tables', {})
            )
            
        except Exception as e:
            raise ValueError(f"Erro ao carregar bronze_config.yaml do sistema {system_name}: {e}")
    
    def create_table_configs(self, system_name: str, schema_mapping: Dict[str, str] = None) -> List[TableConfig]:
        """
        Cria lista de TableConfig usando informações de schema externa.
        
        Args:
            system_name: Nome do sistema
            schema_mapping: Mapeamento de schemas (vem do SystemConfigLoader)
            
        Returns:
            Lista de TableConfig configuradas
        """
        # Carrega configurações de tabelas
        bronze_config = self.load_bronze_tables_config(system_name)
        
        # Schema padrão se não informado
        if schema_mapping is None:
            schema_mapping = {
                'source_schema': 'public',
                'target_schema': 'bronze',
                'table_prefix': f'bronze_{system_name}_'
            }
        
        return self._create_table_configs_from_data(bronze_config.tables, schema_mapping)
    
    def _create_table_configs_from_data(self, tables_data: Dict[str, Dict], schema_mapping: Dict[str, str]) -> List[TableConfig]:
        """
        Cria lista de TableConfig a partir dos dados de tabelas.
        
        Args:
            tables_data: Dados das tabelas do YAML
            schema_mapping: Mapeamento de schemas
            
        Returns:
            Lista de TableConfig configuradas
        """
        table_configs = []
        
        # Mapeia strings para enums
        type_map = {
            'small': TableType.SMALL,
            'large': TableType.LARGE,
            'custom': TableType.CUSTOM
        }
        
        mode_map = {
            'full_only': IncrementalMode.FULL_ONLY,
            'smart_incremental': IncrementalMode.SMART,
            'daily': IncrementalMode.DAILY,
            'seven_days': IncrementalMode.SEVEN_DAYS
        }
        
        for table_name, table_config in tables_data.items():
            # Mapeia configuração
            table_type = type_map.get(table_config.get('type', 'small'), TableType.SMALL)
            incremental_mode = mode_map.get(table_config.get('mode', 'full_only'), IncrementalMode.FULL_ONLY)
            
            # Cria configuração da tabela
            config = TableConfig(
                name=table_name,
                table_type=table_type,
                incremental_mode=incremental_mode,
                id_field=table_config.get('id_field'),
                source_table=table_config.get('source_table'),  # Nome diferente na origem
                select_fields=table_config.get('select_fields'),  # SELECT customizado
                source_schema=schema_mapping.get('source_schema', 'public'),
                target_schema=schema_mapping.get('target_schema', 'bronze'),
                table_prefix=schema_mapping.get('table_prefix', 'bronze_'),
                max_gap_percentage=table_config.get('max_gap_percentage', 20.0),
                max_abs_diff=table_config.get('max_abs_diff', 1000),
                timeout_seconds=table_config.get('timeout_seconds', 600),
                custom_sql=table_config.get('custom_sql'),  # SQL customizado para casos especiais
                daily_filter=None,  # Framework V4 usa DELETE baseado em IDs, não filtros de data
                seven_days_filter=None  # Framework V4 usa DELETE baseado em IDs, não filtros de data
            )
            
            table_configs.append(config)
        
        return table_configs
    
    def create_system_tables_from_yaml(self, system_name: str, schema_mapping: Dict[str, str] = None) -> List[TableConfig]:
        """
        Função principal: Carrega todas as tabelas de um sistema do YAML.
        
        Args:
            system_name: Nome do sistema
            schema_mapping: Mapeamento de schemas (opcional)
            
        Returns:
            Lista de TableConfig prontas para uso
        """
        return self.create_table_configs(system_name, schema_mapping)
    
    def get_available_systems(self) -> List[str]:
        """
        Retorna lista de sistemas disponíveis (que têm bronze_config.yaml)
        
        Returns:
            Lista de nomes de sistemas
        """
        systems = []
        
        if not self.base_path.exists():
            return systems
        
        for system_dir in self.base_path.iterdir():
            if system_dir.is_dir():
                config_file = system_dir / "bronze_config.yaml"
                if config_file.exists():
                    systems.append(system_dir.name)
        
        return sorted(systems)


def create_bronze_loader() -> BronzeLoader:
    """Factory function para criar BronzeLoader"""
    return BronzeLoader()