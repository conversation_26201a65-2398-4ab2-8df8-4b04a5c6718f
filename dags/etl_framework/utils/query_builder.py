"""
Construtor de Queries - ETL Framework

Constrói queries SQL para diferentes estratégias e SGBDs baseado na lógica das DAGs V3.
"""

from typing import Dict, Any, Optional, List
from enum import Enum
from etl_framework.config.table_config import TableConfig
from etl_framework.config.filter_config import FilterConfig


class QueryType(Enum):
    """Tipos de query suportados"""
    FULL_LOAD = "full_load"
    INCREMENTAL_DAILY = "incremental_daily"
    INCREMENTAL_SEVEN_DAYS = "incremental_seven_days"
    CUSTOM = "custom"


class ConnectionType(Enum):
    """Tipos de conexão para construção de queries"""
    DIRECT = "direct"           # Conexão direta
    OPENQUERY = "openquery"     # SQL Server OPENQUERY
    DBLINK = "dblink"          # Oracle Database Link


class QueryBuilder:
    """
    Construtor de queries SQL para ETL
    
    Implementa:
    - Queries para diferentes estratégias
    - Suporte a diferentes tipos de conexão
    - Filtros incrementais flexíveis
    - Queries customizadas
    """
    
    def __init__(self, connection_type: ConnectionType = ConnectionType.DIRECT):
        self.connection_type = connection_type
    
    def build_extract_query(self, table_config: TableConfig, query_type: QueryType, 
                          filter_config: Optional[FilterConfig] = None, **kwargs) -> str:
        """
        Constrói query de extração baseada na configuração
        """
        # Se tem SQL customizado, usa ele
        if table_config.has_custom_sql(query_type.value):
            return table_config.get_custom_sql(query_type.value)
        
        # Constrói query baseada no tipo
        if query_type == QueryType.FULL_LOAD:
            return self._build_full_load_query(table_config)
        elif query_type == QueryType.INCREMENTAL_DAILY:
            return self._build_incremental_query(table_config, "daily", filter_config)
        elif query_type == QueryType.INCREMENTAL_SEVEN_DAYS:
            return self._build_incremental_query(table_config, "seven_days", filter_config)
        else:
            raise ValueError(f"Tipo de query não suportado: {query_type}")
    
    def _build_full_load_query(self, table_config: TableConfig) -> str:
        """Constrói query de full load"""
        source_table = table_config.source_table or table_config.name
        select_fields = table_config.select_fields or "*"
        
        # Para OPENQUERY, sempre usar schema 'public' (PostgreSQL)
        schema_for_query = "public" if self.connection_type == ConnectionType.OPENQUERY else table_config.source_schema
        base_query = f"SELECT {select_fields} FROM {schema_for_query}.{source_table}"
        
        return self._wrap_query_for_connection(base_query, select_fields, schema_for_query, source_table)
    
    def _build_incremental_query(self, table_config: TableConfig, period: str, 
                                filter_config: Optional[FilterConfig] = None) -> str:
        """Constrói query incremental"""
        source_table = table_config.source_table or table_config.name
        select_fields = table_config.select_fields or "*"
        
        # Determina filtro a usar
        if filter_config:
            filter_condition = filter_config.build_filter_condition()
        elif period == "daily":
            filter_condition = table_config.daily_filter
        elif period == "seven_days":
            filter_condition = table_config.seven_days_filter
        else:
            raise ValueError(f"Período não suportado: {period}")
        
        # Para OPENQUERY, sempre usar schema 'public' (PostgreSQL)
        schema_for_query = "public" if self.connection_type == ConnectionType.OPENQUERY else table_config.source_schema
        
        base_query = f"""
            SELECT {select_fields} 
            FROM {schema_for_query}.{source_table} 
            WHERE {filter_condition}
        """
        
        return self._wrap_query_for_connection(base_query, select_fields, schema_for_query, source_table)
    
    def _wrap_query_for_connection(self, base_query: str, select_fields: str, 
                                 schema: str, table: str) -> str:
        """Envolve query baseada no tipo de conexão"""
        if self.connection_type == ConnectionType.DIRECT:
            return base_query
        elif self.connection_type == ConnectionType.OPENQUERY:
            return self._wrap_openquery(base_query)
        elif self.connection_type == ConnectionType.DBLINK:
            return self._wrap_dblink(base_query)
        else:
            return base_query
    
    def _wrap_openquery(self, query: str) -> str:
        """Envolve query em OPENQUERY (SQL Server)"""
        # Remove quebras de linha e espaços extras para OPENQUERY
        clean_query = " ".join(query.split())
        # Escapa aspas simples
        escaped_query = clean_query.replace("'", "''")
        
        return f"SELECT * FROM OPENQUERY(POSTGRES, '{escaped_query}')"
    
    def _wrap_dblink(self, query: str, dblink_name: str = "POSTGRES_LINK") -> str:
        """Envolve query em Database Link (Oracle)"""
        clean_query = " ".join(query.split())
        return f"SELECT * FROM ({clean_query})@{dblink_name}"
    
    def build_delete_query(self, table_config: TableConfig, filter_config: Optional[FilterConfig] = None,
                          custom_condition: Optional[str] = None) -> str:
        """
        Constrói query DELETE para reprocessamento incremental
        """
        full_table_name = table_config.get_full_table_name()
        
        if custom_condition:
            condition = custom_condition
        elif filter_config:
            condition = filter_config.build_filter_condition()
        else:
            # Usa filtro diário padrão
            condition = table_config.daily_filter
        
        return f"DELETE FROM {full_table_name} WHERE {condition}"
    
    def build_count_query(self, table_config: TableConfig, is_source: bool = True) -> str:
        """Constrói query de contagem"""
        if is_source:
            schema = table_config.source_schema
            table_name = table_config.source_table or table_config.name
        else:
            schema = table_config.target_schema
            table_name = f"{table_config.table_prefix}{table_config.name}"
        
        base_query = f"SELECT COUNT(*) FROM {schema}.{table_name}"
        
        if is_source and self.connection_type != ConnectionType.DIRECT:
            return self._wrap_query_for_connection(base_query, "COUNT(*)", schema, table_name)
        
        return base_query
    
    def build_validation_query(self, table_config: TableConfig, validation_type: str = "basic") -> str:
        """Constrói query de validação"""
        full_table_name = table_config.get_full_table_name()
        
        if validation_type == "basic":
            return f"SELECT COUNT(*) as total_records FROM {full_table_name}"
        elif validation_type == "null_analysis":
            return f"""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(*) - COUNT({table_config.id_field}) as null_ids
                FROM {full_table_name}
            """
        elif validation_type == "duplicate_analysis" and table_config.id_field:
            id_fields = [f'"{field.strip()}"' for field in table_config.id_field.split(',')]
            return f"""
                SELECT 
                    COUNT(*) as total_records,
                    COUNT(DISTINCT {','.join(id_fields)}) as unique_records,
                    COUNT(*) - COUNT(DISTINCT {','.join(id_fields)}) as duplicates
                FROM {full_table_name}
            """
        else:
            return f"SELECT COUNT(*) FROM {full_table_name}"
    
    def build_sample_query(self, table_config: TableConfig, sample_size: int = 1000, 
                          is_source: bool = True) -> str:
        """Constrói query para amostra de dados"""
        if is_source:
            schema = table_config.source_schema
            table_name = table_config.source_table or table_config.name
        else:
            schema = table_config.target_schema
            table_name = f"{table_config.table_prefix}{table_config.name}"
        
        select_fields = table_config.select_fields or "*"
        
        base_query = f"""
            SELECT {select_fields} 
            FROM {schema}.{table_name} 
            LIMIT {sample_size}
        """
        
        if is_source and self.connection_type != ConnectionType.DIRECT:
            return self._wrap_query_for_connection(base_query, select_fields, schema, table_name)
        
        return base_query
    
    def build_schema_query(self, table_config: TableConfig, is_source: bool = True) -> str:
        """Constrói query para obter schema da tabela"""
        if is_source:
            schema = table_config.source_schema
            table_name = table_config.source_table or table_config.name
        else:
            schema = table_config.target_schema
            table_name = f"{table_config.table_prefix}{table_config.name}"
        
        return f"""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_schema = '{schema}' 
            AND table_name = '{table_name}'
            ORDER BY ordinal_position
        """
    
    def build_custom_query(self, template: str, **parameters) -> str:
        """
        Constrói query customizada baseada em template
        
        Args:
            template: Template da query com placeholders {param}
            **parameters: Parâmetros para substituir no template
        """
        query = template
        
        for param, value in parameters.items():
            placeholder = f"{{{param}}}"
            query = query.replace(placeholder, str(value))
        
        return query
    
    def build_complex_join_query(self, main_table: TableConfig, 
                                join_configs: List[Dict[str, Any]]) -> str:
        """
        Constrói query com JOINs complexos
        
        Args:
            main_table: Configuração da tabela principal
            join_configs: Lista de configurações de JOIN
                Formato: [{'table': TableConfig, 'type': 'LEFT', 'on': 'condition'}]
        """
        main_select = main_table.select_fields or "*"
        main_source = f"{main_table.source_schema}.{main_table.source_table or main_table.name}"
        
        query_parts = [f"SELECT {main_select} FROM {main_source} main"]
        
        for i, join_config in enumerate(join_configs):
            join_table = join_config['table']
            join_type = join_config.get('type', 'LEFT')
            join_condition = join_config['on']
            
            join_source = f"{join_table.source_schema}.{join_table.source_table or join_table.name}"
            alias = f"t{i+1}"
            
            query_parts.append(f"{join_type} JOIN {join_source} {alias} ON {join_condition}")
        
        base_query = " ".join(query_parts)
        
        if self.connection_type != ConnectionType.DIRECT:
            return self._wrap_query_for_connection(base_query, main_select, main_table.source_schema, main_table.source_table)
        
        return base_query
