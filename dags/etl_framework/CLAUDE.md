# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is an **ETL Framework V4** for Apache Airflow that automates data extraction, transformation, and loading from multiple source systems to a single Corporate Data Warehouse. The framework eliminates 2000+ lines of duplicated code per system by generating DAGs automatically.

**Architecture**: Multiple sources (SQL Server, Oracle, PostgreSQL) → Single destination (PostgreSQL Data Warehouse)
- **Bronze Layer**: Raw data extraction without transformation
- **Silver Layer**: Transformations using CREATE TABLE AS within the DW

## Common Development Commands

### Testing Framework
```bash
# Run import validation tests
cd /home/<USER>/projects/airflow-v1/dags
python3 etl_framework/tests/test_imports.py

# Comprehensive framework validation
python3 etl_framework/validate_framework.py
```

### Quick Development Validation
```bash
# Verify DAGs are generated correctly
python3 -c "
import v4_bronze, v4_silver
print('Bronze DAGs:', [name for name in dir(v4_bronze) if 'V4_BRONZE' in name])
print('Silver DAGs:', [name for name in dir(v4_silver) if 'V4_SILVER' in name])
"

# Test framework components
python3 -c "
from etl_framework.c1_bronze.processor import create_bronze_processor
from etl_framework.config.dw_config import create_corporate_dw_config
dw_config = create_corporate_dw_config()
processor = create_bronze_processor('syonet', dw_config)
print(f'✅ Framework OK - {len(processor.get_bronze_tables_for_parallel_processing())} tables configured')
processor.cleanup()
"
```

## Architecture Overview

### Core Framework Components

**Configuration Layer** (`config/`):
- `SystemConfig`: Defines a source system (Syonet, Oracle ERP, etc.) with its tables and transformations
- `DWConfig`: Corporate Data Warehouse connection and schema configuration  
- `TableConfig`: Individual table processing configuration (Full Load vs Smart Incremental)
- `DatabaseConfig`: Database connection parameters for various SGBD types

**Processing Layer**:
- `BronzeProcessor` (`c1_bronze/processor.py`): Handles data extraction with parallel processing
- `SilverProcessor` (`c2_silver/processor.py`): Manages transformations with dependency resolution
- **Strategy Pattern** (`strategies/`): ETL execution strategies (Full Load, Smart Incremental, etc.)

**Connection Layer** (`connections/`):
- Supports SQL Server, Oracle, and PostgreSQL via unified interface
- Automatic connection pooling and error handling

### DAG Generation Pattern

The framework automatically generates DAGs based on configuration:

```python
# File: v4_bronze.py and v4_silver.py
SYSTEMS_CONFIG = {
    'syonet': {
        'description': 'Sistema Syonet - CRM/Vendas', 
        'schedule_interval': '0 6 * * *',
        'tags': ['syonet', 'bronze', 'sqlserver']
    }
    # Adding new systems here automatically creates DAGs
}
```

Result: `V4_BRONZE_SYONET` and `V4_SILVER_SYONET` DAGs created automatically.

### ETL Strategy Selection

The framework automatically chooses optimal ETL strategies:
- **Small tables**: Full Load (drop/create)
- **Large tables**: Smart Incremental with automatic fallback to Full Load
- **Parallel Processing**: All bronze tables processed in parallel, silver respects dependencies

## Adding New Systems

### Step 1: Create System Configuration
```python
# etl_framework/systems/new_system/config.py
def create_new_system_config(dw_config: DWConfig) -> SystemConfig:
    # Configure source database connection
    source_config = DatabaseConfig(
        name="new_system",
        db_type=DatabaseType.ORACLE,  # or SQLSERVER, POSTGRESQL
        host="server", port=1521, database="db", user="user", password="pass"
    )
    
    # Create system configuration
    system_config = SystemConfig(
        name="new_system",
        description="New System -> Corporate DW",
        source_db_config=source_config
    )
    
    # Add tables and transformations
    system_config.add_bronze_table(TableConfig(...))
    system_config.add_silver_transformation(SilverTransformation(...))
    
    return system_config
```

### Step 2: Register in DAG Generators
Add the new system to `SYSTEMS_CONFIG` in both `v4_bronze.py` and `v4_silver.py`:
```python
'new_system': {
    'description': 'New System Description',
    'schedule_interval': '0 7 * * *',
    'tags': ['new_system', 'bronze']
}
```

## Important Development Notes

### Framework Factory Pattern
Use factory functions for creating processors:
- `create_bronze_processor(system_name, dw_config)` → BronzeProcessor (from `c1_bronze.processor`)
- `create_silver_processor(system_name, dw_config)` → SilverProcessor (from `c2_silver.processor`)  
- `create_corporate_dw_config()` → DWConfig

### Database Connections
- All connections use the unified `DatabaseConnection` interface
- Automatic connection cleanup via context managers
- Built-in timeout and retry mechanisms via `TimeoutManager`

### Current Systems
**Syonet System** (SQL Server): 35 bronze tables, 4 silver transformations
- Configuration: `etl_framework/systems/syonet/config.py`
- Migration data: `systems/syonet/tables_config_v3_migrated.py`

### Key Files for Understanding
- `etl_framework/config/system_config.py:15` - SystemConfig class definition
- `etl_framework/strategies/base.py:15` - ETL strategy interface
- `c1_bronze/processor.py` - Core bronze processing logic
- `c2_silver/processor.py` - Transformation processing with dependencies
- `v4_bronze.py` and `v4_silver.py` - DAG generators

### Performance Considerations
- Bronze processing: All tables in parallel (configurable via `max_parallel_tables`)
- Silver processing: Respects transformation dependencies
- Smart Incremental: Automatic gap detection and fallback to Full Load
- Built-in validation: Data count verification and quality checks