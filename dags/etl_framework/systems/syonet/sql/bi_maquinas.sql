-- Transformação: bi_maquinas
-- Sistema: syonet
-- Descrição: View BIMaquinas migrada do SQL Server com classificação de clientes (OTIMIZADA para PostgreSQL)
WITH base_maquinas AS (
    SELECT DISTINCT ON (MQAT."Nº Evento")
        MQAT."Cliente",
        MQAT."CNPJ/CPF",
        MQAT."Cidade",
        MQAT."UF",
        MQAT."id_tipoevento",
        MQAT."Filial",
        MQAT."Nº Evento",
        MQAT."Data Fechamento",
        MQAT."Data Inclusão",
        MQAT."Data P.Acao",
        MQAT."Data U.Alteração",
        MQAT."Data P.Alteração",
        MQAT."Data Fechamento" as "Hora Fechamento",
        MQAT."Data Inclusão" as "Hora Inclusão",
        MQAT."Data U.Alteração" as "Hora U.Alteração",
        MQAT."Data P.Alteração" as "Hora P.Alteração",
        MQAT."Status",
        MQAT."Modelo Interesse 1",
        MQAT."Máquina 1",
        MQAT."Quantidade 1",
        MQAT."Modelo Interesse 2",
        MQAT."Máquina 2",
        MQAT."Quantidade 2",
        MQAT."Modelo Interesse 3",
        MQAT."Máquina 3",
        MQAT."Quantidade 3",
        MQAT."Modelo Interesse 4",
        MQAT."Máquina 4",
        MQAT."Quantidade 4",
        MQAT."Modelo Interesse 5",
        MQAT."Máquina 5",
        MQAT."Quantidade 5",
        MQAT."Valor 1",
        MQAT."Valor 2",
        MQAT."Valor 3",
        MQAT."Valor 4",
        MQAT."Valor 5",
        MQAT."Etapa Funil",
        MQAT."Motivo de Andamento",
        MQAT."Motivo da Perda",
        MQAT."Usuário AJ",
        MQAT."Temperatura",
        MQAT."Evento Anterior",
        MQAT.faturada,
        MQAT."Data Etapa",
        MQAT."Data do Faturamento",
        MQAT."Fatura esse mês ?",
        MQAT."Previsão Faturamento"
    FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual MQAT
    ORDER BY MQAT."Nº Evento", MQAT."Data Inclusão" DESC
)
SELECT
    "Cliente",
    "CNPJ/CPF",
    "Cidade",
    "UF",
    "id_tipoevento",
    "Filial",
    "Nº Evento",
    "Data Fechamento",
    "Data Inclusão",
    "Data P.Acao",
    "Data U.Alteração",
    "Data P.Alteração",
    "Hora Fechamento",
    "Hora Inclusão",
    "Hora U.Alteração",
    "Hora P.Alteração",
    "Status",
    "Modelo Interesse 1",
    "Máquina 1",
    "Quantidade 1",
    "Modelo Interesse 2",
    "Máquina 2",
    "Quantidade 2",
    "Modelo Interesse 3",
    "Máquina 3",
    "Quantidade 3",
    "Modelo Interesse 4",
    "Máquina 4",
    "Quantidade 4",
    "Modelo Interesse 5",
    "Máquina 5",
    "Quantidade 5",
    "Valor 1",
    "Valor 2",
    "Valor 3",
    "Valor 4",
    "Valor 5",
    "Etapa Funil",
    "Motivo de Andamento",
    "Motivo da Perda",
    "Usuário AJ",
    "Temperatura",
    "Evento Anterior",
    faturada,
    "Data Etapa",
    "Data do Faturamento",
    "Fatura esse mês ?",
    "Previsão Faturamento",
    -- Classificação de clientes baseada em regras de negócio
    CASE
        WHEN "Status" = 'SUCESSO' AND faturada = 'MAQUINA FATURADA' THEN 'CLIENTE_ATIVO'
        WHEN "Status" = 'SUCESSO' THEN 'CLIENTE_POTENCIAL'
        WHEN "Status" = 'ANDAMENTO' THEN 'PROSPECT'
        ELSE 'LEAD'
    END as "Classificacao_Cliente",
    -- Métricas calculadas
    EXTRACT(YEAR FROM "Data Inclusão") as "Ano_Inclusao",
    EXTRACT(MONTH FROM "Data Inclusão") as "Mes_Inclusao",
    CASE
        WHEN "Data Fechamento" IS NOT NULL
        THEN EXTRACT(EPOCH FROM ("Data Fechamento" - "Data Inclusão"))/86400
        ELSE NULL
    END as "Dias_Para_Fechamento"
FROM base_maquinas