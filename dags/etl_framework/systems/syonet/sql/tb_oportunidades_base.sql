-- Transformação: tb_oportunidades_base
-- Sistema: syonet
-- Descrição: Tabela consolidada de oportunidades do Syonet com dados enriquecidos (OTIMIZADA: SELECT específico + CTE combinada)
WITH evt_base AS (
    SELECT id_evento, id_cliente, id_tipoevento, dt_inc, dt_conclusao, dt_proximaacao,
           ds_formacontato, ds_midia, ds_assunto, ds_resultado, id_statusevento,
           ds_temperatura, id_componente
    FROM dbdwcorporativo.bronze_syonet_syo_evento
    WHERE id_tipoevento IN ('DVM LEAD','DVM OPORTUNIDADE','DVM LEAD RELACIONAMENTO','DVM RELACIONAMENTO','DVM LICITACAO')
      AND dt_inc >= 1672531200000  -- 2023-01-01 em milliseconds (otimizado)
),
acao_stats AS (
    SELECT id_evento,
           MAX(dt_alt) as dt_alt_last,
           MIN(dt_alt) as dt_alt_first,
           MAX(CASE WHEN rn_desc = 1 THEN id_motivoresultado END) as id_motivoresultado
    FROM (
        SELECT id_evento, dt_alt, id_motivoresultado,
               ROW_NUMBER() OVER (PARTITION BY id_evento ORDER BY id_acao DESC) as rn_desc
        FROM dbdwcorporativo.bronze_syonet_syo_acao
        WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
    ) t
    WHERE rn_desc <= 1
    GROUP BY id_evento
),
ri_last AS (
    SELECT DISTINCT ON (id_evento) id_evento, id_registrointerface
    FROM dbdwcorporativo.bronze_syonet_syo_registrointerface
    WHERE COALESCE(ic_comentario,'0')='0'
    ORDER BY id_evento, id_registrointerface DESC
),
rp AS (
    SELECT id_registrointerface,
           MAX(CASE WHEN ds_etiqueta='Modelo de Interesse'     THEN ds_valor END) AS _MODELO,
           MAX(CASE WHEN ds_etiqueta='Modelo de Interesse 2'   THEN ds_valor END) AS _MODELO2,
           MAX(CASE WHEN ds_etiqueta='Modelo de Interesse 3'   THEN ds_valor END) AS _MODELO3,
           MAX(CASE WHEN ds_etiqueta='Modelo de Interesse 4'   THEN ds_valor END) AS _MODELO4,
           MAX(CASE WHEN ds_etiqueta='Modelo de Interesse 5'   THEN ds_valor END) AS _MODELO5,
           MAX(CASE WHEN ds_etiqueta='Quantidade'              THEN ds_valor END) AS QTD1,
           MAX(CASE WHEN ds_etiqueta='Quantidade 2'            THEN ds_valor END) AS QTD2,
           MAX(CASE WHEN ds_etiqueta='Quantidade 3'            THEN ds_valor END) AS QTD3,
           MAX(CASE WHEN ds_etiqueta='Quantidade 4'            THEN ds_valor END) AS QTD4,
           MAX(CASE WHEN ds_etiqueta='Quantidade 5'            THEN ds_valor END) AS QTD5,
           MAX(CASE WHEN ds_etiqueta='Valor'                   THEN ds_valor END) AS VALOR1,
           MAX(CASE WHEN ds_etiqueta='Valor 2'                 THEN ds_valor END) AS VALOR2,
           MAX(CASE WHEN ds_etiqueta='Valor 3'                 THEN ds_valor END) AS VALOR3,
           MAX(CASE WHEN ds_etiqueta='Valor 4'                 THEN ds_valor END) AS VALOR4,
           MAX(CASE WHEN ds_etiqueta='Valor 5'                 THEN ds_valor END) AS VALOR5,
           MAX(CASE WHEN ds_etiqueta='Versão'                  THEN
               CASE WHEN TRIM(ds_valor) IS NOT NULL
                    AND TRIM(ds_valor) != ''
                    AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                    AND TRIM(ds_valor) ~ '^[0-9]+$'
                    THEN TRIM(ds_valor)::bigint ELSE NULL END
           END) AS _VERSAO,
           MAX(CASE WHEN ds_etiqueta='Versão 2'                THEN
               CASE WHEN TRIM(ds_valor) IS NOT NULL
                    AND TRIM(ds_valor) != ''
                    AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                    AND TRIM(ds_valor) ~ '^[0-9]+$'
                    THEN TRIM(ds_valor)::bigint ELSE NULL END
           END) AS _VERSAO2,
           MAX(CASE WHEN ds_etiqueta='Versão 3'                THEN
               CASE WHEN TRIM(ds_valor) IS NOT NULL
                    AND TRIM(ds_valor) != ''
                    AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                    AND TRIM(ds_valor) ~ '^[0-9]+$'
                    THEN TRIM(ds_valor)::bigint ELSE NULL END
           END) AS _VERSAO3,
           MAX(CASE WHEN ds_etiqueta='Versão 4'                THEN
               CASE WHEN TRIM(ds_valor) IS NOT NULL
                    AND TRIM(ds_valor) != ''
                    AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                    AND TRIM(ds_valor) ~ '^[0-9]+$'
                    THEN TRIM(ds_valor)::bigint ELSE NULL END
           END) AS _VERSAO4,
           MAX(CASE WHEN ds_etiqueta='Versão 5'                THEN
               CASE WHEN TRIM(ds_valor) IS NOT NULL
                    AND TRIM(ds_valor) != ''
                    AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                    AND TRIM(ds_valor) ~ '^[0-9]+$'
                    THEN TRIM(ds_valor)::bigint ELSE NULL END
           END) AS _VERSAO5,
           MAX(CASE WHEN ds_etiqueta='Previsão de Faturamento' THEN ds_valor END) AS PREV_FAT,
           MAX(CASE WHEN ds_etiqueta='Data do faturamento'     THEN ds_valor END) AS DT_FAT,
           MAX(CASE WHEN ds_etiqueta='FATURA ESSE MÊS?'        THEN ds_valor END) AS FATURA_MES
    FROM dbdwcorporativo.bronze_syonet_syo_camposregistrointerface
    GROUP BY id_registrointerface
),
hef_agg AS (
    SELECT id_evento,
           'MAQUINA FATURADA' AS Faturada,
           MIN(TO_TIMESTAMP(dt_inc/1000) - INTERVAL '3 hours') AS dt_inc
    FROM dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento
    WHERE id_etapafunil IN (6,9,17,99,20,45,153,144,154)
    GROUP BY id_evento
)
SELECT
    EVT.id_cliente,
    CLI.nm_cliente         AS "Cliente",
    CLI.no_cpfcnpj         AS "CNPJ/CPF",
    COALESCE(CLI.nm_cidadecom, CLI.nm_cidaderes) AS "Cidade",
    COALESCE(CLI.sg_ufcom,    CLI.sg_ufres)      AS "UF",
    EVT.id_tipoevento,
    EMP.ap_empresa         AS "Filial",
    EVT.id_evento          AS "Nº Evento",
    EVT.ds_formacontato    AS "Origem",
    EVT.ds_midia           AS "Midia",
    EVT.ds_assunto         AS "Assunto",
    -- OTIMIZAÇÃO: Conversões de timestamp otimizadas
    CASE WHEN EVT.dt_conclusao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_conclusao/1000) - INTERVAL '3 hours' END AS "Data Fechamento",
    TO_TIMESTAMP(EVT.dt_inc/1000) - INTERVAL '3 hours' AS "Data Inclusão",
    CASE WHEN acs.dt_alt_last IS NOT NULL THEN TO_TIMESTAMP(acs.dt_alt_last/1000) - INTERVAL '3 hours' END AS "Data U.Alteração",
    CASE WHEN acs.dt_alt_first IS NOT NULL THEN TO_TIMESTAMP(acs.dt_alt_first/1000) - INTERVAL '3 hours' END AS "Data P.Alteração",
    CASE WHEN EVT.dt_proximaacao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_proximaacao/1000) - INTERVAL '3 hours' END AS "Data P.Acao",
    COALESCE(EVT.ds_resultado,EVT.id_statusevento) AS "Status",
    rp._MODELO  AS "Modelo Interesse 1",
    rp._MODELO2 AS "Modelo Interesse 2",
    rp._MODELO3 AS "Modelo Interesse 3",
    rp._MODELO4 AS "Modelo Interesse 4",
    rp._MODELO5 AS "Modelo Interesse 5",
    mv1.id_versao AS "Máquina 1", mv2.id_versao AS "Máquina 2", mv3.id_versao AS "Máquina 3",
    mv4.id_versao AS "Máquina 4", mv5.id_versao AS "Máquina 5",
    rp.QTD1 AS "Quantidade 1", rp.QTD2 AS "Quantidade 2", rp.QTD3 AS "Quantidade 3",
    rp.QTD4 AS "Quantidade 4", rp.QTD5 AS "Quantidade 5",
    CONCAT(ETF.no_ordem::text, '- ', ETF.nm_etapafunil) AS "Etapa Funil",
    CASE WHEN EVT.id_statusevento = 'ANDAMENTO' THEN MR.ds_motivo END AS "Motivo de Andamento",
    CASE WHEN EVT.ds_resultado   = 'INSUCESSO'  THEN MR.ds_motivo END AS "Motivo da Perda",
    USU.nm_login AS "Usuário AJ",
    REPLACE(rp.VALOR1,'Não informado','0,00') AS "Valor 1",
    REPLACE(rp.VALOR2,'Não informado','0,00') AS "Valor 2",
    REPLACE(rp.VALOR3,'Não informado','0,00') AS "Valor 3",
    REPLACE(rp.VALOR4,'Não informado','0,00') AS "Valor 4",
    REPLACE(rp.VALOR5,'Não informado','0,00') AS "Valor 5",
    CASE WHEN rp.PREV_FAT IS NOT NULL
         AND TRIM(rp.PREV_FAT) != ''
         AND TRIM(rp.PREV_FAT) NOT ILIKE '%selecione%'
         AND TRIM(rp.PREV_FAT) ~ '^[0-9]+$'
         THEN TO_TIMESTAMP(TRIM(rp.PREV_FAT)::bigint/1000) - INTERVAL '3 hours' END AS "Previsão Faturamento",
    evt.ds_temperatura AS "Temperatura",
    EVT.id_componente  AS "Evento Anterior",
    hef_agg.Faturada,
    hef_agg.dt_inc        AS "Data Etapa",
    CASE WHEN rp.DT_FAT IS NOT NULL
         AND TRIM(rp.DT_FAT) != ''
         AND TRIM(rp.DT_FAT) NOT ILIKE '%selecione%'
         AND TRIM(rp.DT_FAT) ~ '^[0-9]+$'
         THEN TO_TIMESTAMP(TRIM(rp.DT_FAT)::bigint/1000) - INTERVAL '3 hours' END AS "Data do Faturamento",
    rp.FATURA_MES     AS "Fatura esse mês ?"
FROM        evt_base EVT
INNER JOIN  dbdwcorporativo.bronze_syonet_syo_encaminhamento ENC
           ON ENC.id_evento = EVT.id_evento AND ENC.id_statusagenteativo = 'S'
LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_cliente CLI ON CLI.id_cliente = EVT.id_cliente
INNER JOIN  dbdwcorporativo.bronze_syonet_syo_usuario USU ON USU.id_usuario = ENC.id_agente
LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_empresa EMP ON EMP.id_empresa = USU.id_empresa
LEFT JOIN   acao_stats acs ON acs.id_evento = EVT.id_evento
LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_motivoresultado MR ON acs.id_motivoresultado = MR.id_motivoresultado
LEFT JOIN   ri_last ril ON ril.id_evento = EVT.id_evento
LEFT JOIN   rp ON rp.id_registrointerface = ril.id_registrointerface
LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv1 ON mv1.id_modeloversao = rp._VERSAO
LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv2 ON mv2.id_modeloversao = rp._VERSAO2
LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv3 ON mv3.id_modeloversao = rp._VERSAO3
LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv4 ON mv4.id_modeloversao = rp._VERSAO4
LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_modeloversao mv5 ON mv5.id_modeloversao = rp._VERSAO5
LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento HEF ON EVT.id_evento = HEF.id_evento AND HEF.ic_etapaatual = '1'
LEFT JOIN   dbdwcorporativo.bronze_syonet_syo_etapafunil ETF ON HEF.id_etapafunil = ETF.id_etapafunil
LEFT JOIN   hef_agg ON hef_agg.id_evento = EVT.id_evento