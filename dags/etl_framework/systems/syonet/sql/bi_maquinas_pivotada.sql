-- Transformação: bi_maquinas_pivotada
-- Sistema: syonet
-- Descrição: View BIMaquinasPivotada com dados pivotados usando UNNEST PostgreSQL (OTIMIZADA)
WITH unpivot_modelos AS (
    SELECT
        "Nº Evento",
        modelo_interesse AS "Modelo Interesse",
        ROW_NUMBER() OVER (PARTITION BY "Nº Evento" ORDER BY ordem) as ordem
    FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual
    CROSS JOIN LATERAL (
        SELECT unnest(ARRAY["Modelo Interesse 1", "Modelo Interesse 2", "Modelo Interesse 3", "Modelo Interesse 4", "Modelo Interesse 5"]) as modelo_interesse,
               unnest(ARRAY[1, 2, 3, 4, 5]) as ordem
    ) AS modelos
    WHERE modelo_interesse IS NOT NULL AND TRIM(modelo_interesse) != ''
),
unpivot_maquinas AS (
    SELECT
        "Nº Evento",
        maquina AS "Máquina",
        ROW_NUMBER() OVER (PARTITION BY "Nº Evento" ORDER BY ordem) as ordem
    FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual
    CROSS JOIN LATERAL (
        SELECT unnest(ARRAY["Máquina 1", "Máquina 2", "Máquina 3", "Máquina 4", "Máquina 5"]) as maquina,
               unnest(ARRAY[1, 2, 3, 4, 5]) as ordem
    ) AS maquinas
    WHERE maquina IS NOT NULL AND TRIM(maquina) != ''
),
unpivot_quantidades AS (
    SELECT
        "Nº Evento",
        quantidade AS "Quantidade",
        ROW_NUMBER() OVER (PARTITION BY "Nº Evento" ORDER BY ordem) as ordem
    FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual
    CROSS JOIN LATERAL (
        SELECT unnest(ARRAY["Quantidade 1", "Quantidade 2", "Quantidade 3", "Quantidade 4", "Quantidade 5"]) as quantidade,
               unnest(ARRAY[1, 2, 3, 4, 5]) as ordem
    ) AS quantidades
    WHERE quantidade IS NOT NULL AND TRIM(quantidade) != ''
),
unpivot_valores AS (
    SELECT
        "Nº Evento",
        valor AS "Valor",
        ROW_NUMBER() OVER (PARTITION BY "Nº Evento" ORDER BY ordem) as ordem
    FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual
    CROSS JOIN LATERAL (
        SELECT unnest(ARRAY["Valor 1", "Valor 2", "Valor 3", "Valor 4", "Valor 5"]) as valor,
               unnest(ARRAY[1, 2, 3, 4, 5]) as ordem
    ) AS valores
    WHERE valor IS NOT NULL AND TRIM(valor) != '' AND valor != 'Não informado'
)
SELECT DISTINCT
    MQAT."Cliente",
    MQAT."CNPJ/CPF",
    MQAT."Cidade",
    MQAT."UF",
    MQAT."id_tipoevento",
    MQAT."Filial",
    MQAT."Nº Evento",
    MQAT."Data Fechamento",
    MQAT."Data Inclusão",
    MQAT."Data P.Acao",
    MQAT."Data U.Alteração",
    MQAT."Data P.Alteração",
    MQAT."Status",
    MQAT."Etapa Funil",
    MQAT."Motivo de Andamento",
    MQAT."Motivo da Perda",
    MQAT."Usuário AJ",
    MQAT."Temperatura",
    MQAT."Evento Anterior",
    MQAT.faturada,
    MQAT."Data Etapa",
    MQAT."Data do Faturamento",
    MQAT."Fatura esse mês ?",
    MQAT."Previsão Faturamento",
    -- Dados pivotados
    UM."Modelo Interesse",
    UMA."Máquina",
    UQ."Quantidade",
    UV."Valor",
    -- Métricas adicionais
    EXTRACT(YEAR FROM MQAT."Data Inclusão") as "Ano",
    EXTRACT(MONTH FROM MQAT."Data Inclusão") as "Mes",
    EXTRACT(DAY FROM MQAT."Data Inclusão") as "Dia"
FROM dbdwcorporativo.silver_syonet_tb_maquinas_atual MQAT
LEFT JOIN unpivot_modelos UM ON UM."Nº Evento" = MQAT."Nº Evento"
LEFT JOIN unpivot_maquinas UMA ON UMA."Nº Evento" = MQAT."Nº Evento" AND UMA.ordem = UM.ordem
LEFT JOIN unpivot_quantidades UQ ON UQ."Nº Evento" = MQAT."Nº Evento" AND UQ.ordem = UM.ordem
LEFT JOIN unpivot_valores UV ON UV."Nº Evento" = MQAT."Nº Evento" AND UV.ordem = UM.ordem
WHERE UM."Modelo Interesse" IS NOT NULL