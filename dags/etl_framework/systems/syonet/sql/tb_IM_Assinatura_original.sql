-- Transformação: tb_IM_Assinatura_original
-- Sistema: syonet
-- Descrição: Tabela de assinaturas e indicações do Syonet (OTIMIZADA: SELECT específico + CTE combinada)
WITH evt_base AS (
    SELECT id_evento, id_cliente, id_tipoevento, dt_inc, dt_conclusao, dt_proximaacao,
           ds_formacontato, ds_assunto, ds_resultado, id_statusevento, ds_temperatura, id_componente
    FROM dbdwcorporativo.bronze_syonet_syo_evento
    WHERE id_tipoevento IN ('INDICACAO ASSINATURA','DVA ASSINATURA','PRECIFICACAO ASSINATURA',
                            'DVA LEAD RELACIONAMENTO','DVA RELACIONAMENTO',
                            'PRECIFICACAO AVANT','DVA ASSINATURA AVANT')
      AND id_statusevento <> 'CANCELADO'
      AND dt_inc > 1704067200000  -- 2023-12-31 em milliseconds
),
acao_stats AS (
    SELECT id_evento,
           MAX(dt_alt) as dt_alt_last,
           MIN(dt_alt) as dt_alt_first,
           MAX(CASE WHEN rn_desc = 1 THEN id_motivoresultado END) as id_motivoresultado
    FROM (
        SELECT id_evento, dt_alt, id_motivoresultado,
               ROW_NUMBER() OVER (PARTITION BY id_evento ORDER BY id_acao DESC) as rn_desc
        FROM dbdwcorporativo.bronze_syonet_syo_acao
        WHERE tp_acao <> 'ENCAMINHAMENTO' AND ds_resultado <> 'PENDENTE'
    ) t
    WHERE rn_desc <= 1
    GROUP BY id_evento
),
ri_last AS (
    SELECT DISTINCT ON (id_evento) id_evento, id_registrointerface
    FROM dbdwcorporativo.bronze_syonet_syo_registrointerface
    WHERE COALESCE(ic_comentario,'0')='0'
      AND COALESCE(ic_excluido,'0')='0'
      AND COALESCE(ic_editado,'0')='0'
    ORDER BY id_evento, id_registrointerface DESC
),
ri_pivot AS (
    SELECT id_registrointerface,
           MAX(CASE WHEN ds_etiqueta='_MODELO'  THEN ds_valor END) AS _MODELO,
           MAX(CASE WHEN ds_etiqueta='_MODELO2' THEN ds_valor END) AS _MODELO2,
           MAX(CASE WHEN ds_etiqueta='_MODELO3' THEN ds_valor END) AS _MODELO3,
           MAX(CASE WHEN ds_etiqueta='_MODELO4' THEN ds_valor END) AS _MODELO4,
           MAX(CASE WHEN ds_etiqueta='_MODELO5' THEN ds_valor END) AS _MODELO5,
           MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO'  THEN ds_valor END) AS VALOR1,
           MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 2' THEN ds_valor END) AS VALOR2,
           MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 3' THEN ds_valor END) AS VALOR3,
           MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 4' THEN ds_valor END) AS VALOR4,
           MAX(CASE WHEN ds_etiqueta='VALOR NEGOCIADO 5' THEN ds_valor END) AS VALOR5,
           MAX(CASE WHEN ds_etiqueta='_QUANTIDADE'  THEN ds_valor END) AS QTD1,
           MAX(CASE WHEN ds_etiqueta='_QUANTIDADE2' THEN ds_valor END) AS QTD2,
           MAX(CASE WHEN ds_etiqueta='_QUANTIDADE3' THEN ds_valor END) AS QTD3,
           MAX(CASE WHEN ds_etiqueta='_QUANTIDADE4' THEN ds_valor END) AS QTD4,
           MAX(CASE WHEN ds_etiqueta='_QUANTIDADE5' THEN ds_valor END) AS QTD5,
           -- Conversão numérica SEGURA
           MAX(CASE WHEN ds_etiqueta='_VERSAO'  THEN
               CASE WHEN ds_valor IS NOT NULL
                    AND TRIM(ds_valor) != ''
                    AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                    AND TRIM(ds_valor) ~ '^[0-9]+$'
                    THEN TRIM(ds_valor)::bigint ELSE NULL END
           END) AS _VERSAO,
           MAX(CASE WHEN ds_etiqueta='_VERSAO2' THEN
               CASE WHEN ds_valor IS NOT NULL
                    AND TRIM(ds_valor) != ''
                    AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                    AND TRIM(ds_valor) ~ '^[0-9]+$'
                    THEN TRIM(ds_valor)::bigint ELSE NULL END
           END) AS _VERSAO2,
           MAX(CASE WHEN ds_etiqueta='_VERSAO3' THEN
               CASE WHEN ds_valor IS NOT NULL
                    AND TRIM(ds_valor) != ''
                    AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                    AND TRIM(ds_valor) ~ '^[0-9]+$'
                    THEN TRIM(ds_valor)::bigint ELSE NULL END
           END) AS _VERSAO3,
           MAX(CASE WHEN ds_etiqueta='_VERSAO4' THEN
               CASE WHEN ds_valor IS NOT NULL
                    AND TRIM(ds_valor) != ''
                    AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                    AND TRIM(ds_valor) ~ '^[0-9]+$'
                    THEN TRIM(ds_valor)::bigint ELSE NULL END
           END) AS _VERSAO4,
           MAX(CASE WHEN ds_etiqueta='_VERSAO5' THEN
               CASE WHEN ds_valor IS NOT NULL
                    AND TRIM(ds_valor) != ''
                    AND TRIM(ds_valor) NOT ILIKE '%selecione%'
                    AND TRIM(ds_valor) ~ '^[0-9]+$'
                    THEN TRIM(ds_valor)::bigint ELSE NULL END
           END) AS _VERSAO5
    FROM dbdwcorporativo.bronze_syonet_syo_camposregistrointerface
    GROUP BY id_registrointerface
)
SELECT
    EVT.id_cliente                               AS id_cliente,
    CLI.nm_cliente                               AS Cliente,
    CLI.no_cpfcnpj                               AS CNPJ_CPF,
    EVT.id_tipoevento                            AS Tipo_Evento,
    EMP.ap_empresa                               AS Filial,
    EVT.id_evento                                AS Numero_Evento,
    EVT.ds_formacontato                          AS Origem,
    rp._MODELO                                   AS Modelo_Interesse_1,
    mv1.id_versao                                AS Maquina_1,
    rp.QTD1                                      AS Quantidade_1,
    REPLACE(rp.VALOR1,'Não informado','0,00')    AS Valor_1,
    rp._MODELO2                                  AS Modelo_Interesse_2,
    mv2.id_versao                                AS Maquina_2,
    rp.QTD2                                      AS Quantidade_2,
    REPLACE(rp.VALOR2,'Não informado','0,00')    AS Valor_2,
    rp._MODELO3                                  AS Modelo_Interesse_3,
    mv3.id_versao                                AS Maquina_3,
    rp.QTD3                                      AS Quantidade_3,
    REPLACE(rp.VALOR3,'Não informado','0,00')    AS Valor_3,
    rp._MODELO4                                  AS Modelo_Interesse_4,
    mv4.id_versao                                AS Maquina_4,
    rp.QTD4                                      AS Quantidade_4,
    REPLACE(rp.VALOR4,'Não informado','0,00')    AS Valor_4,
    rp._MODELO5                                  AS Modelo_Interesse_5,
    mv5.id_versao                                AS Maquina_5,
    rp.QTD5                                      AS Quantidade_5,
    REPLACE(rp.VALOR5,'Não informado','0,00')    AS Valor_5,
    EVT.ds_assunto                               AS Assunto,
    CASE WHEN EVT.dt_conclusao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_conclusao/1000) - INTERVAL '3 hours' END AS Data_Fechamento,
    TO_TIMESTAMP(EVT.dt_inc/1000) - INTERVAL '3 hours' AS Data_Inclusao,
    CASE WHEN acs.dt_alt_last IS NOT NULL THEN TO_TIMESTAMP(acs.dt_alt_last/1000) - INTERVAL '3 hours' END AS Data_U_Alteracao,
    CASE WHEN acs.dt_alt_first IS NOT NULL THEN TO_TIMESTAMP(acs.dt_alt_first/1000) - INTERVAL '3 hours' END AS Data_P_Alteracao,
    CASE WHEN EVT.dt_proximaacao IS NOT NULL THEN TO_TIMESTAMP(EVT.dt_proximaacao/1000) - INTERVAL '3 hours' END AS Data_P_Acao,
    COALESCE(EVT.ds_resultado,EVT.id_statusevento) AS Status,
    CONCAT(ETF.no_ordem::text, '- ', ETF.nm_etapafunil) AS Etapa_Funil,
    CASE WHEN EVT.id_statusevento='ANDAMENTO' THEN MR.ds_motivo END AS Motivo_de_Andamento,
    CASE WHEN EVT.ds_resultado='INSUCESSO' THEN MR.ds_motivo END    AS Motivo_da_Perda,
    USU.nm_login                                   AS Vendedor,
    EVT.ds_temperatura                             AS Temperatura,
    EVT1.id_tipoevento                             AS Tipo_Evento_Anterior,
    EVT1.cd_usuarioinc                             AS Indicante_Evento_Anterior
FROM evt_base EVT
INNER JOIN dbdwcorporativo.bronze_syonet_syo_encaminhamento ENC
        ON ENC.id_evento = EVT.id_evento AND ENC.id_statusagenteativo = 'S'
LEFT JOIN dbdwcorporativo.bronze_syonet_syo_cliente CLI ON CLI.id_cliente = EVT.id_cliente
INNER JOIN dbdwcorporativo.bronze_syonet_syo_empresa EMP ON EMP.id_empresa = ENC.id_empresa
INNER JOIN dbdwcorporativo.bronze_syonet_syo_usuario USU ON USU.id_usuario = ENC.id_agente
LEFT JOIN acao_stats acs ON acs.id_evento = EVT.id_evento
LEFT JOIN dbdwcorporativo.bronze_syonet_syo_motivoresultado MR ON acs.id_motivoresultado = MR.id_motivoresultado
LEFT JOIN ri_last ril ON ril.id_evento = EVT.id_evento
LEFT JOIN ri_pivot rp ON rp.id_registrointerface = ril.id_registrointerface
LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv1 ON mv1.id_modeloversao = rp._VERSAO
LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv2 ON mv2.id_modeloversao = rp._VERSAO2
LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv3 ON mv3.id_modeloversao = rp._VERSAO3
LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv4 ON mv4.id_modeloversao = rp._VERSAO4
LEFT JOIN dbdwcorporativo.bronze_syonet_syo_modeloversao mv5 ON mv5.id_modeloversao = rp._VERSAO5
LEFT JOIN dbdwcorporativo.bronze_syonet_syo_historicoetapafunilevento HEF ON EVT.id_evento = HEF.id_evento AND HEF.ic_etapaatual = '1'
LEFT JOIN dbdwcorporativo.bronze_syonet_syo_etapafunil ETF ON HEF.id_etapafunil = ETF.id_etapafunil
LEFT JOIN dbdwcorporativo.bronze_syonet_syo_evento EVT1 ON EVT1.id_evento = EVT.id_componente