"""
Configuração do Sistema Syonet - ETL Framework V4

Factory usando 3 loaders especializados:
- SystemConfigLoader: Conexões e metadados do sistema
- BronzeLoader: Configurações de tabelas bronze
- SqlLoader: Transformações silver (já implementado)

Nova arquitetura com separação clara de responsabilidades.
"""

from ...config.system_config import SystemConfig
from ...config.table_config import TableConfig, TableType, IncrementalMode
from ...config.database_config import DatabaseConfig, DatabaseType
from ...config.dw_config import DWConfig
from ...config.performance_config import get_global_performance_config
# 🆕 NOVOS LOADERS: Estrutura especializada e padronizada
from ...utils.system_config_loader import SystemConfigLoader
from ...utils.bronze_loader import BronzeLoader


def create_syonet_system_config(dw_config: DWConfig) -> SystemConfig:
    """
    Cria configuração completa do sistema Syonet usando loaders especializados.
    
    🆕 NOVA ARQUITETURA V4:
    - SystemConfigLoader: Metadados e conexões
    - BronzeLoader: Tabelas bronze
    - SqlLoader: Transformações silver (já implementado)
    """
    
    # 🚀 OTIMIZAÇÃO: Obtém configurações globais de performance
    perf_config = get_global_performance_config()
    optimized_settings = perf_config.get_optimized_settings()
    
    # 🆕 NOVOS LOADERS: Estrutura especializada
    system_loader = SystemConfigLoader()
    bronze_loader = BronzeLoader()
    
    # Carrega metadados e configuração de conexão
    system_metadata = system_loader.get_system_metadata('syonet')
    source_config = system_loader.create_source_database_config('syonet')
    schema_mapping = system_loader.get_schema_mapping('syonet')
    
    # 🚀 OTIMIZAÇÃO: Configuração do sistema usando dados do YAML + performance
    system_config = SystemConfig(
        name=system_metadata.name,
        description=f"{system_metadata.description} (OTIMIZADA V4)",
        source_db_config=source_config,
        default_chunk_size=optimized_settings['default_chunk_size'],
        default_timeout=system_metadata.default_timeout_seconds,
        max_parallel_tables=system_metadata.max_parallel_tables,
        enable_smart_incremental=system_metadata.enable_smart_incremental,
        enable_validation=system_metadata.enable_validation,
        # 🚀 Configurações dinâmicas de performance
        enable_production_mode=perf_config.is_production_mode(),
        validation_cache_ttl=optimized_settings['validation_cache_ttl'],
        enable_connection_sharing=False,
        enable_lazy_loading=perf_config.enable_lazy_loading,
        # 🚨 OTIMIZAÇÃO CRÍTICA: Usa configurações do YAML
        max_retries=system_metadata.max_retries,
        retry_delay_minutes=system_metadata.retry_delay_minutes
    )
    
    # 🆕 NOVO: Carrega tabelas bronze usando schema mapping
    bronze_tables = bronze_loader.create_table_configs('syonet', schema_mapping)
    for table_config in bronze_tables:
        system_config.add_bronze_table(table_config)

    # 🆕 NOVO: Carrega transformações silver de arquivos SQL organizados
    # Substitui as transformações hardcoded por arquivos SQL sustentáveis
    transformations_loaded = system_config.load_silver_transformations_from_sql()
    
    # Log para debugging
    print(f"✅ Syonet V4: {len(bronze_tables)} tabelas bronze carregadas do YAML")
    print(f"✅ Syonet V4: {transformations_loaded} transformações silver carregadas de arquivos SQL")

    return system_config


def create_syonet_source_config() -> DatabaseConfig:
    """
    DEPRECADO - Usa SystemConfigLoader.create_source_database_config('syonet')
    
    Mantido para compatibilidade temporária.
    """
    system_loader = SystemConfigLoader()
    return system_loader.create_source_database_config('syonet')
