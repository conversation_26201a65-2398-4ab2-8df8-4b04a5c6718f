# Configuração do Sistema Syonet - ETL Framework V4
# Sistema CRM com dados de vendas, clientes e eventos

system:
  name: "syonet"
  description: "Sistema CRM Syonet - Dados de vendas, clientes e eventos"
  
  # Configurações essenciais
  default_timeout_seconds: 900  # Timeout padrão (15 min)
  max_parallel_tables: 2  # Paralelismo de tabelas
  enable_validation: true  # Validação de dados
  max_retries: 2  # Tentativas em caso de falha

# Conexão origem (SQL Server)
source_database:
  type: "sqlserver"
  host: "***********"
  port: 50666
  database: "master"
  user: "bq_dwcorporativo_u"
  password: "N#OK+#{Yx*"  # Considere usar Airflow Variables
  
  # Schemas específicos do Syonet
  default_schema: "dbo"
  openquery_schema: "public"  # Para OPENQUERY PostgreSQL

# Destino (Data Warehouse)
target_database:
  schema: "dbdwcorporativo"
  table_prefix: "bronze_syonet_"

# Performance
performance:
  chunk_size: 50000  # Tamanho do batch de processamento

# Agendamento
scheduling:
  bronze_schedule: "0 6 * * *"  # Diário às 6h
  silver_schedule: "0 8 * * *"  # Diário às 8h
  timezone: "America/Sao_Paulo"

# Configurações de ETL específicas do sistema
etl_settings:
  # Filtros incrementais para OPENQUERY (PostgreSQL sintaxe)
  default_incremental_filters:
    daily: |
      (TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)
       OR TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP))

    seven_days: |
      (TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days''
       OR TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days'')
  
  # Configurações globais de validação (aplicadas a todas as tabelas)
  default_validation:
    max_gap_percentage: 10.0  # Diferença máxima percentual
    max_abs_diff: 10  # Diferença absoluta máxima
    
  # Estratégia incremental inteligente
  smart_incremental_settings:
    enable_gap_analysis: true  # Análise automática de diferenças
    fallback_strategy: "seven_days_then_full"  # incremental → 7 dias → full