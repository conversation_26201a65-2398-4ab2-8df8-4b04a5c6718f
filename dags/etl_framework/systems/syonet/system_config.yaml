# Configuração do Sistema Syonet - ETL Framework V4
# Este arquivo contém APENAS configurações de sistema, conexões e metadados
# Configurações de tabelas ficam em bronze_config.yaml e silver_config.yaml

system:
  name: "syonet"
  description: "Sistema CRM Syonet - Dados de vendas, clientes e eventos"
  tags: ["syonet", "crm", "vendas", "sqlserver"]
  
  # Configurações globais do sistema
  default_timeout_seconds: 900
  max_parallel_tables: 2
  enable_smart_incremental: true
  enable_validation: true
  max_retries: 2  # CORRIGIDO: V3 original usa 2 retries
  retry_delay_minutes: 1  # CORRIGIDO: V3 original usa 1 minuto

  # 🆕 ESTRATÉGIA INTELIGENTE V3: Configurações para incremental 7 dias
  seven_days_timeout_seconds: 300  # Timeout específico para incremental 7 dias
  enable_seven_days_fallback: true  # Habilita fallback inteligente
  seven_days_analysis_enabled: true  # Análise automática de gap

# Configuração da base de dados origem (SQL Server)
source_database:
  name: "syonet_source"
  type: "sqlserver"
  host: "***********"
  port: 50666
  database: "master"
  user: "bq_dwcorporativo_u"
  password: "N#OK+#{Yx*"
  default_timeout: 300
  description: "SQL Server Syonet - Sistema CRM"
  
  # Schema específico da origem
  default_schema: "dbo"  # Schema do SQL Server
  openquery_schema: "public"  # Schema usado no OPENQUERY (PostgreSQL)

# Configuração da base de dados destino (já vem do DWConfig)
target_database:
  schema: "dbdwcorporativo"
  table_prefix: "bronze_syonet_"
  
# Configurações de performance específicas do sistema
performance:
  chunk_size: 50000  # CORRIGIDO: V3 original usa 50000
  read_chunk_size: 50000  # Para leitura em chunks do SQL Server
  connection_pool_size: 5
  enable_connection_sharing: false
  enable_lazy_loading: true
  validation_cache_ttl: 3600

# Configurações de ETL específicas do sistema
etl_settings:
  # Filtros incrementais para OPENQUERY (PostgreSQL sintaxe) - CORRIGIDO: aspas duplas
  default_incremental_filters:
    daily: |
      (TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)
       OR TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP))

    seven_days: |
      (TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days''
       OR TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days'')

  # Configurações de validação padrão (baseadas no V3)
  default_validation:
    max_gap_percentage: 10.0  # MAX_GAP_PERCENTAGE do V3
    max_abs_diff: 10  # MAX_ABS_DIFF do V3
    enable_count_validation: true
    enable_freshness_validation: true

  # 🆕 ESTRATÉGIA INTELIGENTE V3: Configurações específicas para análise de gap
  smart_incremental_settings:
    enable_gap_analysis: true  # Análise automática de diferenças
    gap_analysis_timeout: 60  # Timeout para análise de gap (segundos)
    fallback_strategy: "seven_days_then_full"  # Estratégia: incremental → 7 dias → full
    enable_performance_logging: true  # Logs detalhados de performance

# Agendamento padrão (usado pelos DAG generators)
scheduling:
  bronze_schedule: "0 6 * * *"  # 6h da manhã
  silver_schedule: "0 8 * * *"  # 8h da manhã (após bronze)
  gold_schedule: "0 10 * * *"  # 10h da manhã (após silver)
  timezone: "America/Sao_Paulo"  # Timezone para agendamento