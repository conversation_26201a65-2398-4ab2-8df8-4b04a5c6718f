# Configuração do Sistema Syonet - ETL Framework V4
# Este arquivo contém APENAS configurações de sistema, conexões e metadados
# Configurações de tabelas ficam em bronze_config.yaml e silver_config.yaml

system:
  name: "syonet"
  description: "Sistema CRM Syonet - Dados de vendas, clientes e eventos"
  tags: ["syonet", "crm", "vendas", "sqlserver"]
  
  # Configurações globais do sistema
  default_timeout_seconds: 900
  max_parallel_tables: 2
  enable_smart_incremental: true
  enable_validation: true
  max_retries: 0
  retry_delay_minutes: 5

# Configuração da base de dados origem (SQL Server)
source_database:
  name: "syonet_source"
  type: "sqlserver"
  host: "***********"
  port: 50666
  database: "master"
  user: "bq_dwcorporativo_u"
  password: "N#OK+#{Yx*"
  default_timeout: 300
  description: "SQL Server Syonet - Sistema CRM"
  
  # Schema específico da origem
  default_schema: "dbo"  # Schema do SQL Server
  openquery_schema: "public"  # Schema usado no OPENQUERY (PostgreSQL)

# Configuração da base de dados destino (já vem do DWConfig)
target_database:
  schema: "dbdwcorporativo"
  table_prefix: "bronze_syonet_"
  
# Configurações de performance específicas do sistema
performance:
  chunk_size: 10000
  connection_pool_size: 5
  enable_connection_sharing: false
  enable_lazy_loading: true
  validation_cache_ttl: 3600

# Configurações de ETL específicas do sistema
etl_settings:
  # Filtros incrementais para OPENQUERY (PostgreSQL sintaxe)
  default_incremental_filters:
    daily: |
      (TIMESTAMP 'epoch' + dt_inc * INTERVAL '1 millisecond' >= DATE_TRUNC('day', CURRENT_TIMESTAMP)
       OR TIMESTAMP 'epoch' + dt_alt * INTERVAL '1 millisecond' >= DATE_TRUNC('day', CURRENT_TIMESTAMP))
    
    seven_days: |
      (TIMESTAMP 'epoch' + dt_inc * INTERVAL '1 millisecond' >= CURRENT_TIMESTAMP - INTERVAL '7 days'
       OR TIMESTAMP 'epoch' + dt_alt * INTERVAL '1 millisecond' >= CURRENT_TIMESTAMP - INTERVAL '7 days')

  # Configurações de validação padrão
  default_validation:
    max_gap_percentage: 10.0
    max_abs_diff: 10
    enable_count_validation: true
    enable_freshness_validation: true

# Agendamento padrão (usado pelos DAG generators)
scheduling:
  bronze_schedule: "0 6 * * *"  # 6h da manhã
  silver_schedule: "0 8 * * *"  # 8h da manhã (após bronze)
  timezone: "America/Sao_Paulo"