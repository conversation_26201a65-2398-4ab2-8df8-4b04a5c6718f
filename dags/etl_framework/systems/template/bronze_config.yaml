# Configuração Bronze - Sistema {NOME}
# Template para tabelas bronze - ajuste conforme necessário

tables:
  # ===== TABELAS PEQUENAS (cadastros, configurações) =====
  tabela_cadastro:
    type: small
    mode: full_only
    timeout_seconds: 300
    
  # ===== TABELAS GRANDES (transacionais) =====
  tabela_transacional:
    type: large
    mode: smart_incremental
    id_field: "id_campo"  # Campo chave primária
    timeout_seconds: 900
    
  # ===== TABELA COM MÚLTIPLAS EXECUÇÕES DIÁRIAS =====
  # Substitui times_per_day do V2
  tabela_frequente:
    type: large
    mode: smart_incremental
    id_field: "id"
    schedule_override: "0 */12 * * *"  # 2x ao dia (equivale times_per_day: 2)
    
  # ===== TABELA COM SQL CUSTOMIZADO =====
  tabela_custom:
    type: large
    mode: smart_incremental
    id_field: "id"
    sql_file: "nome_arquivo.sql"  # Arquivo em systems/{sistema}/sql/
    
  # ===== CONFIGURAÇÕES OPCIONAIS (use apenas se necessário) =====
  
  # Tabela com campos específicos (não todos)
  # tabela_campos_especificos:
  #   type: small
  #   select_fields: "id, nome, data_atualizacao"
  
  # Tabela que nunca faz full load
  # tabela_apenas_incremental:
  #   mode: incremental_only
  #   id_field: "id"
  
  # Tabela com validação customizada
  # tabela_validacao_custom:
  #   max_gap_percentage: 20.0  # Tolerância maior que o padrão (10%)
  #   enable_validation: false  # Ou desabilitar completamente