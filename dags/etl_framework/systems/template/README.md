# 📋 Template de Configuração - ETL Framework V4

## 🎯 Como Usar Este Template

1. **Copie a pasta template** para o nome do seu sistema:
   ```bash
   cp -r template/ meu_sistema/
   ```

2. **Edite os arquivos de configuração**:
   - `system_config.yaml` - Configurações gerais e conexões
   - `bronze_config.yaml` - Tabelas bronze (origem → DW)
   - `silver_config.yaml` - Transformações silver (opcional)

3. **Crie a pasta SQL** se necessário:
   ```bash
   mkdir meu_sistema/sql/
   ```

## 📊 Validação de Flexibilidade

### ✅ Sistemas Suportados
- **SQL Server** (ex: Syonet, Dealer)
- **Oracle** (ex: ERP, Financeiro)
- **PostgreSQL** (ex: Aplicações modernas)
- **MySQL** (ex: E-commerce, Web)

### ✅ Cenários Contemplados

#### 1. Sistema Simples (apenas cadastros)
```yaml
# bronze_config.yaml
tables:
  usuarios:
    type: small
    mode: full_only
    timeout_seconds: 300
    
  produtos:
    type: small
    mode: full_only
    timeout_seconds: 300
```

#### 2. Sistema Transacional Complexo
```yaml
# bronze_config.yaml
tables:
  vendas:
    type: large
    mode: smart_incremental
    id_field: "id_venda"
    
  pagamentos:
    type: large
    mode: smart_incremental
    id_field: "id_pagamento"
    schedule_override: "0 */6 * * *"  # 4x ao dia
```

#### 3. Sistema com SQL Customizado
```yaml
# bronze_config.yaml
tables:
  relatorio_complexo:
    type: large
    mode: smart_incremental
    id_field: "id"
    sql_file: "relatorio_customizado.sql"
```

#### 4. Sistema Legado com Peculiaridades
```yaml
# system_config.yaml
source_database:
  type: "oracle"
  port: 1521
  # Configurações específicas Oracle podem ser adicionadas

# bronze_config.yaml  
tables:
  tabela_sem_pk:
    type: large
    mode: full_only  # Sem PK, sempre full
    
  tabela_critica:
    type: large
    mode: incremental_only  # Nunca faz full load
    id_field: "seq_id"
```

## 🔧 Parâmetros Essenciais

### system_config.yaml
- **Mínimo necessário**: type, host, port, database, user, password
- **Opcional**: Tudo pode usar valores padrão

### bronze_config.yaml
- **Mínimo por tabela**: Apenas o nome (usa padrões)
- **Comum**: type, mode, id_field (para incremental)
- **Avançado**: schedule_override, sql_file, validações custom

### silver_config.yaml
- **Opcional**: Só crie se tiver transformações
- **Mínimo**: type, source_tables, sql_file

## 📈 Exemplos de Migração V2 → V4

### V2 (Hardcoded)
```python
Bronze('dealer').add_tables([
    'clientes',
    {'vendas': {'times_per_day': 2}}
])
```

### V4 (YAML)
```yaml
tables:
  clientes:
    type: small
    mode: full_only
    
  vendas:
    type: large
    mode: smart_incremental
    id_field: "id_venda"
    schedule_override: "0 */12 * * *"  # 2x ao dia
```

## ✨ Vantagens da Simplificação

1. **80% dos casos**: Use apenas configurações básicas
2. **Extensível**: Adicione parâmetros conforme necessidade
3. **Retrocompatível**: Suporta peculiaridades do V2
4. **Auto-documentado**: YAML é legível e claro
5. **Validação**: Framework valida configurações automaticamente