# Configuração Silver - Sistema {NOME}
# Este arquivo contém as configurações das transformações silver
# Copie para systems/{seu_sistema}/silver_config.yaml e ajuste conforme necessário

# Configurações das tabelas/views silver
tables:
  # ===== EXEMPLO: VIEW SIMPLES =====
  # View que faz join ou filtro básico
  
  vw_clientes_ativos:
    type: view  # Tipo view (não materializada)
    source_tables:  # Tabelas bronze origem
      - bronze_{sistema}_cliente
      - bronze_{sistema}_contrato
    refresh_mode: "always"  # Sempre recria a view
    sql_file: "vw_clientes_ativos.sql"  # SQL em systems/{sistema}/sql/
    
  # ===== EXEMPLO: TABELA MATERIALIZADA =====
  # Tabela física com dados transformados
  
  silver_vendas_agregadas:
    type: table  # Tabela materializada
    source_tables:
      - bronze_{sistema}_vendas
      - bronze_{sistema}_produtos
      - bronze_{sistema}_clientes
    refresh_mode: "incremental"  # Atualização incremental
    id_field: "dt_venda"  # Campo para controle incremental
    sql_file: "silver_vendas_agregadas.sql"
    indexes:  # Índices para performance
      - ["dt_venda", "id_cliente"]
      - ["id_produto"]
    
  # ===== EXEMPLO: AGREGAÇÃO COMPLEXA =====
  # Tabela com múltiplas agregações e cálculos
  
  silver_metricas_mensais:
    type: table
    source_tables:
      - bronze_{sistema}_vendas
      - bronze_{sistema}_devolucoes
      - bronze_{sistema}_custos
    refresh_mode: "full"  # Sempre recalcula tudo
    sql_file: "silver_metricas_mensais.sql"
    post_process:  # Processamento após insert
      - "ANALYZE silver_{sistema}_metricas_mensais"
      - "VACUUM silver_{sistema}_metricas_mensais"
    
  # ===== EXEMPLO: SNAPSHOT HISTÓRICO =====
  # Tabela que mantém histórico de mudanças
  
  silver_historico_precos:
    type: table
    source_tables:
      - bronze_{sistema}_produtos
      - bronze_{sistema}_tabela_precos
    refresh_mode: "snapshot"  # Mantém histórico
    snapshot_date_field: "dt_snapshot"  # Campo de data do snapshot
    sql_file: "silver_historico_precos.sql"
    retention_days: 365  # Mantém 1 ano de histórico
    
  # ===== EXEMPLO: TABELA COM QUALIDADE DE DADOS =====
  # Inclui métricas de qualidade
  
  silver_clientes_qualificados:
    type: table
    source_tables:
      - bronze_{sistema}_clientes
      - bronze_{sistema}_enderecos
      - bronze_{sistema}_telefones
    refresh_mode: "incremental"
    id_field: "id_cliente"
    sql_file: "silver_clientes_qualificados.sql"
    quality_checks:  # Validações de qualidade
      - check: "not_null"
        columns: ["nome", "cpf_cnpj"]
      - check: "unique"
        columns: ["cpf_cnpj"]
      - check: "range"
        column: "score_credito"
        min: 0
        max: 1000
    
  # ===== EXEMPLO: TABELA PARTICIONADA =====
  # Para grandes volumes com particionamento
  
  silver_eventos_particionado:
    type: table
    source_tables:
      - bronze_{sistema}_eventos
    refresh_mode: "incremental"
    partition_field: "dt_evento"  # Campo de particionamento
    partition_type: "range"  # Tipo: range, list
    partition_interval: "monthly"  # Intervalo: daily, monthly, yearly
    sql_file: "silver_eventos_particionado.sql"
    
  # ===== EXEMPLO: TABELA COM DEPENDÊNCIAS =====
  # Tabela que depende de outras silver
  
  silver_dashboard_executivo:
    type: table
    source_tables:
      - silver_{sistema}_vendas_agregadas
      - silver_{sistema}_metricas_mensais
      - bronze_{sistema}_metas
    depends_on:  # Dependências explícitas
      - silver_{sistema}_vendas_agregadas
      - silver_{sistema}_metricas_mensais
    refresh_mode: "full"
    sql_file: "silver_dashboard_executivo.sql"
    
  # ===== EXEMPLO: VIEW MATERIALIZADA =====
  # PostgreSQL materialized view
  
  mv_analise_comportamental:
    type: materialized_view
    source_tables:
      - bronze_{sistema}_navegacao
      - bronze_{sistema}_conversoes
    refresh_mode: "concurrent"  # Refresh sem bloquear
    sql_file: "mv_analise_comportamental.sql"
    indexes:
      - ["id_usuario", "dt_evento"]

# Configurações globais silver
global_settings:
  # Schema padrão para objetos silver
  target_schema: "dbdwcorporativo"
  table_prefix: "silver_{sistema}_"
  
  # Configurações de performance
  enable_parallel_refresh: true
  max_parallel_tables: 3
  
  # Configurações de qualidade
  enable_quality_checks: true
  quality_check_sample_size: 10000
  
  # Logs e monitoramento
  enable_lineage_tracking: true
  enable_execution_stats: true

# Grupos de processamento (ordem de execução)
processing_groups:
  # Grupo 1: Views e tabelas base
  - group: "base"
    tables:
      - vw_clientes_ativos
      - silver_vendas_agregadas
    parallel: true
    
  # Grupo 2: Agregações que dependem do grupo 1
  - group: "aggregations"
    tables:
      - silver_metricas_mensais
      - silver_historico_precos
    parallel: true
    depends_on: ["base"]
    
  # Grupo 3: Dashboard e relatórios finais
  - group: "reporting"
    tables:
      - silver_dashboard_executivo
    parallel: false
    depends_on: ["aggregations"]

# Configurações de manutenção
maintenance:
  # Vacuum automático
  auto_vacuum:
    enabled: true
    tables: ["silver_*"]
    schedule: "0 2 * * 0"  # Domingos às 2h
    
  # Analyze automático
  auto_analyze:
    enabled: true
    tables: ["silver_*"]
    schedule: "0 3 * * *"  # Diariamente às 3h
    
  # Limpeza de dados antigos
  data_retention:
    enabled: true
    policies:
      - table_pattern: "*_historico_*"
        retention_days: 365
      - table_pattern: "*_log_*"
        retention_days: 90