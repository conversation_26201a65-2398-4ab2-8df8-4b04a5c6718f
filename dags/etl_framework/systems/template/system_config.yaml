# Configuração do Sistema {NOME} - ETL Framework V4
# Template base para configuração de novos sistemas
# Copie para: systems/{seu_sistema}/system_config.yaml

system:
  name: "{sistema_nome}"  # Nome único do sistema (ex: dealer, syonet)
  description: "{descrição do sistema}"  # Descrição clara do sistema
  
  # Configurações essenciais
  default_timeout_seconds: 900  # Timeout padrão (15 min)
  max_parallel_tables: 2  # Paralelismo de tabelas
  enable_validation: true  # Validação de dados
  max_retries: 2  # Tentativas em caso de falha

# Conexão origem
source_database:
  type: "{tipo}"  # sqlserver | oracle | postgres | mysql
  host: "{host}"
  port: {porta}  # 1433 (SQL Server) | 1521 (Oracle) | 5432 (PostgreSQL)
  database: "{database}"
  user: "{usuário}"
  password: "{senha}"  # Use Airflow Variables em produção
  
# Destino (Data Warehouse)
target_database:
  schema: "dbdwcorporativo"
  table_prefix: "bronze_{sistema}_"

# Performance
performance:
  chunk_size: 50000  # Tamanho do batch de processamento

# Agendamento
scheduling:
  bronze_schedule: "0 6 * * *"  # Diário às 6h
  timezone: "America/Sao_Paulo"