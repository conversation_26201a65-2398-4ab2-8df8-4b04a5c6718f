# Configuração do Sistema {NOME} - ETL Framework V4
# Template base para configuração de novos sistemas
# Copie para: systems/{seu_sistema}/system_config.yaml

system:
  name: "{sistema_nome}"  # Nome único do sistema (ex: dealer, syonet)
  description: "{descrição do sistema}"  # Descrição clara do sistema
  
  # Configurações essenciais
  default_timeout_seconds: 900  # Timeout padrão (15 min)
  max_parallel_tables: 2  # Paralelismo de tabelas
  enable_validation: true  # Validação de dados
  max_retries: 2  # Tentativas em caso de falha

# Configuração da base de dados origem
source_database:
  name: "{nome_conexão}"  # Nome da conexão no Airflow
  type: "{tipo}"  # Tipo do banco: sqlserver, oracle, postgres, mysql
  host: "{host}"  # Endereço do servidor
  port: {porta}  # Porta do banco (ex: 1433, 1521, 5432)
  database: "{database}"  # Nome do banco de dados
  user: "{usuário}"  # Usuário de conexão
  password: "{senha}"  # Senha (considere usar Airflow Variables)
  default_timeout: 300  # Timeout padrão para queries
  description: "{descrição da origem}"  # Descrição da origem dos dados
  
  # Configurações específicas por tipo de banco (opcional)
  default_schema: "{schema}"  # Schema padrão (ex: dbo, public)
  
# Configuração da base de dados destino (PostgreSQL DW)
target_database:
  schema: "dbdwcorporativo"  # Schema no DW corporativo
  table_prefix: "bronze_{sistema}_"  # Prefixo para tabelas bronze
  
# Configurações de performance específicas do sistema
performance:
  chunk_size: 50000  # Tamanho do chunk para processamento
  read_chunk_size: 50000  # Tamanho do chunk para leitura
  connection_pool_size: 5  # Tamanho do pool de conexões
  enable_connection_sharing: false  # Compartilhar conexões entre tasks
  enable_lazy_loading: true  # Carregamento sob demanda
  validation_cache_ttl: 3600  # TTL do cache de validação (segundos)

# Configurações de ETL específicas do sistema
etl_settings:
  # Filtros incrementais padrão (ajuste conforme campos do sistema)
  default_incremental_filters:
    daily: |
      {campo_data} >= DATEADD(day, -1, GETDATE())
      
    seven_days: |
      {campo_data} >= DATEADD(day, -7, GETDATE())
  
  # Configurações de validação padrão
  default_validation:
    max_gap_percentage: 10.0  # Diferença máxima percentual aceitável
    max_abs_diff: 10  # Diferença absoluta máxima aceitável
    enable_count_validation: true  # Valida contagem de registros
    enable_freshness_validation: true  # Valida data dos dados

  # Configurações para estratégia incremental inteligente
  smart_incremental_settings:
    enable_gap_analysis: true  # Análise automática de diferenças
    gap_analysis_timeout: 60  # Timeout para análise (segundos)
    fallback_strategy: "seven_days_then_full"  # Estratégia de fallback
    enable_performance_logging: true  # Logs detalhados de performance

# Agendamento padrão (usado pelos DAG generators)
scheduling:
  bronze_schedule: "0 6 * * *"  # 6h da manhã
  silver_schedule: "0 8 * * *"  # 8h da manhã (após bronze)
  gold_schedule: "0 10 * * *"  # 10h da manhã (após silver)
  timezone: "America/Sao_Paulo"  # Timezone para agendamento

# Notificações e alertas (opcional)
notifications:
  email_on_failure: true  # Envia email em caso de falha
  email_on_retry: false  # Envia email em caso de retry
  email_list: []  # Lista de emails para notificação
  slack_webhook: ""  # Webhook do Slack (se configurado)

# Metadados adicionais
metadata:
  owner: "data-engineering"  # Time responsável
  sla_hours: 4  # SLA em horas
  priority: "medium"  # Prioridade: low, medium, high
  documentation_url: ""  # URL da documentação do sistema