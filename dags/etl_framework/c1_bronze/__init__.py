"""
Módulo Bronze - ETL Framework

Processamento da camada Bronze (extração bruta dos sistemas de origem para o DW).

A camada Bronze é responsável por:
- Extrair dados brutos dos sistemas de origem (Oracle, SQL Server, PostgreSQL, etc.)
- Aplicar estratégias incrementais inteligentes
- Transferir dados para o Data Warehouse Corporativo
- Manter estrutura original dos dados (sem transformações)
- Aplicar validações básicas de qualidade

Componentes:
- BronzeProcessor: Processador principal da camada bronze
- BronzeStrategy: Estratégias de extração (full load, incremental)
- BronzeValidator: Validação de dados extraídos
"""

from etl_framework.c1_bronze.processor import BronzeProcessor, create_bronze_processor
from etl_framework.strategies import ETLStrategy as BronzeStrategy, FullLoadStrategy, SmartIncrementalStrategy
from etl_framework.strategies.base import ETLStrategy as IncrementalStrategy
from etl_framework.c1_bronze.validator import BronzeValidator

__all__ = [
    'BronzeProcessor',
    'create_bronze_processor',  # Factory function
    'BronzeStrategy',
    'FullLoadStrategy',
    'IncrementalStrategy',
    'SmartIncrementalStrategy',
    'BronzeValidator'
]
