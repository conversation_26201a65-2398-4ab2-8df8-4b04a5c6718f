"""
Processador Bronze - ETL Framework

Processador genérico da camada Bronze que funciona para qualquer sistema de origem.
"""

import logging
import time
from typing import Dict, Any, List, Optional
from etl_framework.connections.base import DatabaseConnection
from etl_framework.connections.postgres import PostgreSQLConnection
from etl_framework.config.dw_config import DWConfig
from etl_framework.config.system_config import SystemConfig
from etl_framework.utils.logger import ETLLogger, LoggingMode, setup_production_logger
from etl_framework.utils.timeout_manager import BatchTimer
from etl_framework.strategies import SmartIncrementalStrategy, FullLoadStrategy
from etl_framework.strategies.factory import get_strategy_factory
from etl_framework.strategies.base import ETLMode
from etl_framework.c1_bronze.validator import BronzeValidator


class BronzeProcessor:
    """
    Processador genérico da camada Bronze
    
    Funciona para qualquer sistema de origem:
    - Syonet (SQL Server)
    - Oracle ERP (Oracle)
    - Sistema X (PostgreSQL)
    - etc.
    
    A lógica é a mesma, apenas muda:
    - Sintaxe das queries (por tipo de banco)
    - Campos incrementais (dt_inc/dt_alt vs created_date/updated_date)
    - Configurações específicas
    """
    
    def __init__(self, 
                 source_connection: DatabaseConnection,
                 system_config: SystemConfig,
                 dw_config: DWConfig):
        
        self.source_connection = source_connection
        self.system_config = system_config
        self.dw_config = dw_config

        # Conexão única com DW
        self.dw_connection = PostgreSQLConnection(dw_config.connection)

        # 🚀 OTIMIZAÇÃO: Logger configurado baseado no modo do sistema
        if system_config.enable_production_mode:
            self.logger = setup_production_logger(f"BronzeProcessor_{system_config.name}")
        else:
            self.logger = ETLLogger(f"BronzeProcessor_{system_config.name}", mode=LoggingMode.DEVELOPMENT)

        # ✅ Validação de configurações após inicializar logger
        self._validate_configurations(source_connection, system_config, dw_config)

        # 🚀 OTIMIZAÇÃO: Validador configurado com settings otimizados
        self.validator = BronzeValidator(
            source_connection=source_connection,
            dw_connection=self.dw_connection,
            system_config=system_config,
            dw_config=dw_config,
            enable_validation=system_config.enable_validation,
            cache_ttl=system_config.validation_cache_ttl
        )

        # Métricas
        self.batch_timer = BatchTimer(f"bronze_{system_config.name}")
        self.results = {}
    
    def process_all_tables(self) -> Dict[str, Any]:
        """
        Processa todas as tabelas bronze do sistema
        Lógica genérica que funciona para qualquer sistema
        """
        self.logger.log_dag_start(f"bronze_{self.system_config.name}")
        self.batch_timer.start_batch()
        
        try:
            # Obtém tabelas na ordem de processamento
            tables = self.system_config.get_bronze_tables()
            
            self.logger.info(f"🚀 Iniciando processamento bronze de {len(tables)} tabelas do sistema {self.system_config.name}")
            
            # Processa cada tabela
            for table_config in tables:
                try:
                    self.batch_timer.start_operation(table_config.name)
                    result = self.process_single_table(table_config)
                    self.results[table_config.name] = result
                    self.batch_timer.end_operation(table_config.name)
                    
                except Exception as e:
                    self.logger.log_table_error(table_config.name, str(e))
                    self.results[table_config.name] = {
                        'success': False,
                        'error': str(e),
                        'records_processed': 0
                    }
                    self.batch_timer.end_operation(table_config.name)
            
            # Finaliza processamento
            batch_stats = self.batch_timer.end_batch()
            successful_tables = sum(1 for r in self.results.values() if r.get('success', False))
            
            self.logger.log_dag_complete(f"bronze_{self.system_config.name}", successful_tables)
            
            return {
                'success': True,
                'system': self.system_config.name,
                'tables_processed': len(tables),
                'successful_tables': successful_tables,
                'failed_tables': len(tables) - successful_tables,
                'batch_stats': batch_stats,
                'table_results': self.results
            }
            
        except Exception as e:
            self.logger.log_dag_error(f"bronze_{self.system_config.name}", str(e))
            return {
                'success': False,
                'system': self.system_config.name,
                'error': str(e),
                'table_results': self.results
            }
    
    def process_single_table(self, table_config) -> Dict[str, Any]:
        """
        Processa uma única tabela bronze
        Lógica genérica que se adapta ao tipo de sistema
        """
        self.logger.log_table_start(table_config.name, table_config.incremental_mode.value)
        
        try:
            # 🚀 OTIMIZAÇÃO: Usa Strategy Factory para reutilização de instâncias
            factory = get_strategy_factory()
            
            if table_config.should_use_smart_incremental():
                strategy = factory.get_strategy(
                    mode=ETLMode.SMART_INCREMENTAL,
                    source_connection=self.source_connection,
                    target_connection=self.dw_connection,
                    chunk_size=table_config.chunk_size,
                    timeout_seconds=table_config.timeout_seconds,
                    max_gap_percentage=table_config.max_gap_percentage
                )
            else:
                # Tabelas pequenas ou full_only
                strategy = factory.get_strategy(
                    mode=ETLMode.FULL_LOAD,
                    source_connection=self.source_connection,
                    target_connection=self.dw_connection,
                    chunk_size=table_config.chunk_size,
                    timeout_seconds=table_config.timeout_seconds,
                    max_gap_percentage=table_config.max_gap_percentage
                )
            
            # Converte TableConfig para TableMetadata
            table_metadata = self._convert_to_table_metadata(table_config)

            # Executa ETL
            etl_result = strategy.execute(table_metadata)

            # Verifica se ETL foi bem-sucedido
            if not etl_result.success:
                error_msg = etl_result.error_message or "ETL falhou sem mensagem de erro específica"
                self.logger.log_table_error(table_config.name, error_msg)
                return {
                    'success': False,
                    'error': error_msg,  # ✅ Chave error sempre presente
                    'strategy_used': etl_result.mode_used.value,
                    'records_processed': etl_result.records_processed,
                    'execution_time': etl_result.execution_time,
                    'fallback_used': etl_result.fallback_used,
                    'source_count': etl_result.source_count,
                    'dest_count': etl_result.dest_count
                }

            # Validação (se habilitada)
            validation_result = None
            if table_config.enable_validation:
                is_valid, validation_info = self.validator.validate_table(table_config)
                validation_result = {
                    'is_valid': is_valid,
                    'validation_info': validation_info
                }

                self.logger.log_validation(
                    table_config.name,
                    validation_info.get('source_count', 0),
                    validation_info.get('dest_count', 0),
                    is_valid
                )

            # Log de sucesso
            self.logger.log_table_success(
                table_config.name,
                etl_result.records_processed,
                strategy=etl_result.mode_used.value,
                fallback_used=etl_result.fallback_used
            )

            return {
                'success': True,  # ✅ Explicitamente True para sucesso
                'strategy_used': etl_result.mode_used.value,
                'records_processed': etl_result.records_processed,
                'execution_time': etl_result.execution_time,
                'fallback_used': etl_result.fallback_used,
                'validation_result': validation_result,
                'source_count': etl_result.source_count,
                'dest_count': etl_result.dest_count
            }
            
        except Exception as e:
            self.logger.log_table_error(table_config.name, str(e))
            raise
    
    def get_processing_summary(self) -> Dict[str, Any]:
        """Retorna resumo do processamento"""
        if not self.results:
            return {'message': 'Nenhum processamento executado'}
        
        successful = [name for name, result in self.results.items() if result.get('success', False)]
        failed = [name for name, result in self.results.items() if not result.get('success', False)]
        
        total_records = sum(result.get('records_processed', 0) for result in self.results.values())
        
        return {
            'system': self.system_config.name,
            'total_tables': len(self.results),
            'successful_tables': len(successful),
            'failed_tables': len(failed),
            'success_rate': len(successful) / len(self.results) * 100,
            'total_records_processed': total_records,
            'successful_table_names': successful,
            'failed_table_names': failed
        }
    
    def cleanup(self):
        """Limpa recursos"""
        try:
            if hasattr(self.source_connection, 'close_pool'):
                self.source_connection.close_pool()
            if hasattr(self.dw_connection, 'close_pool'):
                self.dw_connection.close_pool()
            self.logger.info("🧹 Recursos limpos com sucesso")
        except Exception as e:
            self.logger.warning(f"⚠️ Erro na limpeza de recursos: {str(e)}")

    def get_bronze_tables_for_parallel_processing(self) -> List:
        """
        Retorna lista de tabelas bronze para processamento paralelo

        Returns:
            List[TableConfig]: Lista de configurações de tabela
        """
        return self.system_config.get_bronze_tables()

    def _convert_to_table_metadata(self, table_config):
        """
        Converte TableConfig para TableMetadata

        Args:
            table_config: Configuração da tabela

        Returns:
            TableMetadata: Metadados da tabela para estratégias ETL
        """
        from etl_framework.strategies.base import TableMetadata

        return TableMetadata(
            name=table_config.name,
            source_table=table_config.source_table or table_config.name,
            id_field=table_config.id_field,
            select_fields=table_config.select_fields,
            custom_sql=table_config.custom_sql,  # ✅ Agora suporta SQL customizado do V3
            incremental_field=table_config.incremental_fields or "dt_inc,dt_alt",
            schema=table_config.source_schema or "PUBLIC",  # ✅ Corrigido: usar PUBLIC maiúsculo como nas DAGs V3
            target_schema=table_config.target_schema or "dbdwcorporativo",  # ✅ Usar configuração da tabela
            table_prefix=table_config.table_prefix or f"bronze_{self.system_config.name}_"  # ✅ Usar configuração da tabela
        )

    def _validate_configurations(self, source_connection, system_config, dw_config):
        """
        Valida configurações antes de inicializar o processador

        Args:
            source_connection: Conexão com sistema de origem
            system_config: Configuração do sistema
            dw_config: Configuração do DW

        Raises:
            ValueError: Se configurações são inválidas
        """
        # Validar conexão de origem
        if not source_connection:
            raise ValueError("source_connection é obrigatório")

        if not hasattr(source_connection, 'config'):
            raise ValueError("source_connection deve ter atributo 'config'")

        # Validar system_config
        if not system_config:
            raise ValueError("system_config é obrigatório")

        if not hasattr(system_config, 'name'):
            raise ValueError("system_config deve ter atributo 'name'")

        if not hasattr(system_config, 'get_bronze_tables'):
            raise ValueError("system_config deve ter método 'get_bronze_tables'")

        # Validar dw_config
        if not dw_config:
            raise ValueError("dw_config é obrigatório")

        if not hasattr(dw_config, 'connection'):
            raise ValueError("dw_config deve ter atributo 'connection'")

        # Validar se há tabelas bronze configuradas
        bronze_tables = system_config.get_bronze_tables()
        if not bronze_tables:
            raise ValueError(f"Nenhuma tabela bronze configurada para sistema {system_config.name}")

        # Validar configurações das tabelas
        for table_config in bronze_tables:
            if not hasattr(table_config, 'name'):
                raise ValueError("Todas as tabelas devem ter atributo 'name'")

            if not table_config.name:
                raise ValueError("Nome da tabela não pode estar vazio")

        self.logger.info(f"✅ Configurações validadas para sistema {system_config.name}")
        self.logger.info(f"   - Tabelas bronze: {len(bronze_tables)}")
        self.logger.info(f"   - Conexão origem: {source_connection.config.host}")
        self.logger.info(f"   - Conexão DW: {dw_config.connection.host}")


# Factory function para criar processador baseado no sistema
def create_bronze_processor(system_name: str, dw_config: DWConfig) -> BronzeProcessor:
    """
    Factory para criar processador bronze baseado no sistema
    """
    # Import dinâmico da configuração do sistema
    if system_name.lower() == 'syonet':
        from etl_framework.systems.syonet.config import create_syonet_system_config
        from etl_framework.connections.sqlserver import SQLServerConnection

        system_config = create_syonet_system_config(dw_config)
        source_connection = SQLServerConnection(system_config.source_db_config)

    elif system_name.lower() == 'oracle_erp':
        from etl_framework.systems.oracle_erp.config import create_oracle_erp_system_config
        from etl_framework.connections.oracle import OracleConnection

        system_config = create_oracle_erp_system_config(dw_config)
        source_connection = OracleConnection(system_config.source_db_config)

    else:
        raise ValueError(f"Sistema não suportado: {system_name}")
    
    return BronzeProcessor(
        source_connection=source_connection,
        system_config=system_config,
        dw_config=dw_config
    )
