"""
Classe Base para Estratégias ETL - ETL Framework

Define interface comum para todas as estratégias de ETL.
"""

from abc import ABC, abstractmethod
from typing import Dict, Any, Optional, List, Tuple
from enum import Enum
import logging
import time
from dataclasses import dataclass


class ETLMode(Enum):
    """Modos de ETL disponíveis"""
    FULL_LOAD = "full_load"
    INCREMENTAL_DAILY = "incremental_daily"
    INCREMENTAL_SEVEN_DAYS = "incremental_seven_days"
    SMART_INCREMENTAL = "smart_incremental"


@dataclass
class ETLResult:
    """Resultado de uma operação ETL"""
    success: bool
    mode_used: ETLMode
    records_processed: int
    execution_time: float
    source_count: int
    dest_count: int
    error_message: Optional[str] = None
    fallback_used: bool = False
    analysis_info: Optional[Dict[str, Any]] = None


@dataclass
class TableMetadata:
    """Metadados de uma tabela para ETL"""
    name: str
    source_table: Optional[str] = None
    id_field: Optional[str] = None
    select_fields: Optional[str] = None
    custom_sql: Optional[Dict[str, str]] = None
    incremental_field: str = "dt_inc,dt_alt"  # Campos para incremental
    schema: str = "public"
    target_schema: str = "bronze"
    table_prefix: str = ""


class ETLStrategy(ABC):
    """
    Classe abstrata para estratégias de ETL
    
    Define interface comum para:
    - Extração de dados
    - Transformação (se necessário)
    - Carregamento
    - Validação
    - Fallback automático
    """
    
    def __init__(self, 
                 source_connection,
                 target_connection,
                 chunk_size: int = 50000,
                 timeout_seconds: int = 300,
                 max_gap_percentage: float = 10.0):
        self.source_connection = source_connection
        self.target_connection = target_connection
        self.chunk_size = chunk_size
        self.timeout_seconds = timeout_seconds
        self.max_gap_percentage = max_gap_percentage
        self.logger = logging.getLogger(f"{self.__class__.__name__}")
        
    @abstractmethod
    def get_mode(self) -> ETLMode:
        """Retorna o modo ETL desta estratégia"""
        pass
        
    @abstractmethod
    def build_extract_query(self, table_metadata: TableMetadata) -> str:
        """Constrói query de extração específica da estratégia"""
        pass
        
    @abstractmethod
    def should_use_strategy(self, table_metadata: TableMetadata, 
                          analysis_info: Optional[Dict[str, Any]] = None) -> bool:
        """Determina se esta estratégia deve ser usada"""
        pass
        
    @abstractmethod
    def prepare_target_table(self, table_metadata: TableMetadata, 
                           sample_data=None) -> bool:
        """Prepara tabela de destino (create, truncate, etc.)"""
        pass
        
    def execute(self, table_metadata: TableMetadata) -> ETLResult:
        """
        Executa estratégia ETL completa
        
        Fluxo:
        1. Análise prévia (se necessário)
        2. Construção da query
        3. Preparação da tabela destino
        4. Extração e carregamento
        5. Validação
        """
        start_time = time.time()
        
        try:
            self.logger.info(f"Iniciando ETL {self.get_mode().value} para {table_metadata.name}")
            
            # 1. Construir query de extração
            extract_query = self.build_extract_query(table_metadata)
            
            # 2. Executar ETL
            records_processed = self._execute_etl(table_metadata, extract_query)
            
            # 3. Validar resultado
            validation_result = self._validate_result(table_metadata)
            
            execution_time = time.time() - start_time
            
            if validation_result['success']:
                self.logger.info(f"ETL {self.get_mode().value} concluído: {records_processed} registros em {execution_time:.1f}s")
                return ETLResult(
                    success=True,
                    mode_used=self.get_mode(),
                    records_processed=records_processed,
                    execution_time=execution_time,
                    source_count=validation_result['source_count'],
                    dest_count=validation_result['dest_count']
                )
            else:
                raise Exception(f"Validação falhou: {validation_result['error']}")
                
        except Exception as e:
            execution_time = time.time() - start_time
            self.logger.error(f"Erro em ETL {self.get_mode().value}: {str(e)}")
            return ETLResult(
                success=False,
                mode_used=self.get_mode(),
                records_processed=0,
                execution_time=execution_time,
                source_count=0,
                dest_count=0,
                error_message=str(e)
            )
    
    @abstractmethod
    def _execute_etl(self, table_metadata: TableMetadata, extract_query: str) -> int:
        """Executa ETL específico da estratégia"""
        pass
        
    def _validate_result(self, table_metadata: TableMetadata) -> Dict[str, Any]:
        """Valida resultado do ETL usando DataValidator com lógica especial"""
        try:
            # ✅ Usar DataValidator com lógica especial para casos como syo_evento_obs
            from ..processors.data_validator import DataValidator

            validator = DataValidator(
                source_connection=self.source_connection,
                target_connection=self.target_connection,
                max_abs_diff=10,  # Tolerância padrão
                enable_validation=True
            )

            # Usar validação especializada
            is_valid, analysis_info = validator.validate_table(table_metadata, self.get_mode().value)

            return {
                'success': is_valid,
                'source_count': analysis_info.get('source_count', 0),
                'dest_count': analysis_info.get('dest_count', 0),
                'abs_diff': analysis_info.get('abs_diff', 0),
                'diff_percentage': analysis_info.get('diff_percentage', 0),
                'error': None if is_valid else f"Divergência: origem={analysis_info.get('source_count', 0)}, destino={analysis_info.get('dest_count', 0)}"
            }

        except Exception as e:
            return {
                'success': False,
                'source_count': 0,
                'dest_count': 0,
                'abs_diff': 0,
                'diff_percentage': 0,
                'error': str(e)
            }
    
    def get_full_table_name(self, table_metadata: TableMetadata) -> str:
        """Retorna nome completo da tabela de destino"""
        return f"{table_metadata.target_schema}.{table_metadata.table_prefix}{table_metadata.name}"
