"""
Estratégia Incremental 7 Dias - ETL Framework

Implementa carga incremental dos últimos 7 dias baseada na lógica das DAGs V3.
"""

from .base import ETLStrategy, ETLMode, TableMetadata, ETLResult
from typing import Dict, Any, Optional


class IncrementalSevenDaysStrategy(ETLStrategy):
    """
    Estratégia de Incremental 7 Dias
    
    Características:
    - Carrega dados dos últimos 7 dias
    - Usado como fallback quando incremental diário falha
    - Mais dados que diário, mas menos que full load
    - Tem timeout configurável
    """
    
    def get_mode(self) -> ETLMode:
        return ETLMode.INCREMENTAL_SEVEN_DAYS
    
    def should_use_strategy(self, table_metadata: TableMetadata, 
                          analysis_info: Optional[Dict[str, Any]] = None) -> bool:
        """
        Incremental 7 dias é usado quando:
        - Incremental diário falhou
        - Diferença entre origem/destino é pequena (≤ max_gap_percentage)
        - Tabela tem campo ID
        """
        if not table_metadata.id_field:
            return False
            
        if analysis_info:
            diff_percentage = analysis_info.get('diff_percentage', 0)
            should_use_seven_days = analysis_info.get('should_use_seven_days', False)
            
            return (diff_percentage <= self.max_gap_percentage and 
                   should_use_seven_days)
                   
        return True  # Pode ser usado como fallback
    
    def build_extract_query(self, table_metadata: TableMetadata) -> str:
        """Constrói query de extração incremental 7 dias"""
        source_table = table_metadata.source_table or table_metadata.name
        select_fields = table_metadata.select_fields or "*"
        
        # Se tem SQL customizado para 7 dias, usar
        if (table_metadata.custom_sql and 
            'seven_days' in table_metadata.custom_sql):
            return table_metadata.custom_sql['seven_days']
        
        # Filtro incremental 7 dias
        seven_days_filter = self._build_seven_days_filter()
        
        # Query baseada no tipo de conexão
        if hasattr(self.source_connection, 'execute_openquery'):
            # SQL Server com OPENQUERY - sempre usar schema 'public' do PostgreSQL
            return f"""
                SELECT {select_fields}
                FROM OPENQUERY(POSTGRES, 'SELECT {select_fields} 
                                          FROM public.{source_table} 
                                          WHERE {seven_days_filter}')
            """
        else:
            # Conexão direta
            return f"""
                SELECT {select_fields}
                FROM {table_metadata.schema}.{source_table}
                WHERE {seven_days_filter}
            """
    
    def _build_seven_days_filter(self) -> str:
        """Constrói filtro para incremental 7 dias (baseado nas DAGs V3)"""
        return """(TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days''
                  OR
                  TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days'')"""
    
    def prepare_target_table(self, table_metadata: TableMetadata, 
                           sample_data=None) -> bool:
        """Prepara tabela para incremental 7 dias"""
        try:
            full_table_name = self.get_full_table_name(table_metadata)
            
            # Verifica se tabela existe
            table_exists = self.target_connection.table_exists(
                table_metadata.target_schema,
                f"{table_metadata.table_prefix}{table_metadata.name}"
            )
            
            if not table_exists and sample_data is not None:
                # Cria tabela baseada no sample
                from ..processors.schema_manager import SchemaManager
                
                schema_manager = SchemaManager(self.target_connection)
                return schema_manager.create_table_from_dataframe(
                    table_metadata, sample_data
                )
            
            return True
            
        except Exception as e:
            self.logger.error(f"Erro ao preparar tabela para incremental 7 dias: {str(e)}")
            return False
    
    def _execute_etl(self, table_metadata: TableMetadata, extract_query: str) -> int:
        """Executa ETL incremental 7 dias com timeout"""
        from ..processors.data_processor import DataProcessor
        from ..utils.timeout_manager import TimeoutManager
        
        # Constrói condições de DELETE para reprocessamento
        delete_conditions = self._build_delete_conditions(table_metadata)
        
        processor = DataProcessor(
            source_connection=self.source_connection,
            target_connection=self.target_connection,
            chunk_size=self.chunk_size
        )
        
        # Executa com timeout
        timeout_manager = TimeoutManager(self.timeout_seconds)
        
        try:
            return timeout_manager.execute_with_timeout(
                processor.transfer_table_data,
                extract_query=extract_query,
                table_metadata=table_metadata,
                is_full_load=False,
                delete_conditions=delete_conditions
            )
        except TimeoutError:
            self.logger.warning(f"Timeout em incremental 7 dias para {table_metadata.name}")
            raise
    
    def _build_delete_conditions(self, table_metadata: TableMetadata) -> list:
        """Constrói condições de DELETE para reprocessamento 7 dias"""
        full_table_name = self.get_full_table_name(table_metadata)
        seven_days_filter = self._build_seven_days_filter()
        
        # DELETE baseado no filtro 7 dias
        delete_sql = f"""
            DELETE FROM {full_table_name}
            WHERE {seven_days_filter}
        """
        
        return [delete_sql]
