"""
Gerenciador de Schema - ETL Framework

Implementa criação e gerenciamento de schemas baseado na lógica das DAGs V3.
"""

import pandas as pd
import logging
from typing import Dict, Any
from etl_framework.strategies.base import TableMetadata


class SchemaManager:
    """
    Gerenciador de schemas de tabelas
    
    Implementa:
    - Criação automática de tabelas baseada em DataFrames
    - Mapeamento de tipos pandas para PostgreSQL
    - Aplicação de permissões automáticas
    """
    
    def __init__(self, target_connection):
        self.target_connection = target_connection
        self.logger = logging.getLogger(self.__class__.__name__)
        
        # Mapeamento de tipos pandas para PostgreSQL (baseado nas DAGs V3)
        self.type_mapping = {
            'object': 'TEXT',
            'int64': 'BIGINT',
            'int32': 'INTEGER',
            'float64': 'DOUBLE PRECISION',
            'float32': 'REAL',
            'bool': 'BOOLEAN',
            'datetime64[ns]': 'TIMESTAMP'
        }
    
    def create_table_from_dataframe(self, table_metadata: TableMetadata, df: pd.DataFrame) -> bool:
        """
        Cria tabela no PostgreSQL baseada no DataFrame
        Baseado na função create_table_schema_postgres das DAGs V3
        """
        try:
            full_table_name = f"{table_metadata.target_schema}.{table_metadata.table_prefix}{table_metadata.name}"
            
            with self.target_connection.get_cursor() as (cursor, conn):
                # Drop table if exists
                cursor.execute(f"DROP TABLE IF EXISTS {full_table_name}")
                
                # Construir CREATE TABLE
                columns = []
                for col_name, dtype in df.dtypes.items():
                    pg_type = self.type_mapping.get(str(dtype), 'TEXT')
                    # Sanitizar nome da coluna
                    clean_col_name = col_name.replace(' ', '_').replace('/', '_').lower()
                    columns.append(f'"{clean_col_name}" {pg_type}')
                
                create_sql = f"CREATE TABLE {full_table_name} ({', '.join(columns)})"
                cursor.execute(create_sql)
                
                self.logger.info(f"✅ Tabela criada: {full_table_name}")
                
                # Aplicar permissões automáticas
                self._apply_table_permissions(table_metadata, cursor)
                
                conn.commit()
                return True
        
        except Exception as e:
            self.logger.error(f"❌ Erro ao criar tabela {full_table_name}: {str(e)}")
            return False
    
    def _apply_table_permissions(self, table_metadata: TableMetadata, cursor):
        """
        Aplica permissões automáticas baseadas na tabela aux_permissions_tables
        Baseado na função apply_table_permissions das DAGs V3
        """
        try:
            full_table_name = f"{table_metadata.target_schema}.{table_metadata.table_prefix}{table_metadata.name}"
            
            self.logger.info(f"🔐 Aplicando permissões automáticas para {table_metadata.name}...")
            
            # Busca permissões da tabela aux_permissions_tables
            cursor.execute(f"""
                SELECT username, table_name, grant_type, active
                FROM {table_metadata.target_schema}.aux_permissions_tables
                WHERE (table_name = '*' OR lower('{full_table_name}') LIKE '%' || lower(table_name) || '%')
                AND active = true
            """)
            permissions = cursor.fetchall()
            
            grants_applied = 0
            for user, table, permission_type, active in permissions:
                try:
                    # Aplica grant
                    grant_sql = f"{permission_type} ON TABLE {full_table_name} TO {user}"
                    cursor.execute(grant_sql)
                    grants_applied += 1
                    self.logger.debug(f"✅ Grant aplicado: {permission_type} ON {full_table_name} TO {user}")
                except Exception as grant_error:
                    self.logger.warning(f"⚠️ Erro ao aplicar grant para {user}: {str(grant_error)}")
            
            self.logger.info(f"🔐 Total de grants aplicados: {grants_applied}")
            return True
        
        except Exception as e:
            self.logger.warning(f"⚠️ Erro ao aplicar permissões para {table_metadata.name}: {str(e)}")
            return False
    
    def table_exists(self, table_metadata: TableMetadata) -> bool:
        """Verifica se tabela existe"""
        return self.target_connection.table_exists(
            table_metadata.target_schema,
            f"{table_metadata.table_prefix}{table_metadata.name}"
        )
    
    def get_table_columns(self, table_metadata: TableMetadata) -> list:
        """Retorna colunas da tabela"""
        try:
            full_table_name = f"{table_metadata.target_schema}.{table_metadata.table_prefix}{table_metadata.name}"
            
            with self.target_connection.get_cursor() as (cursor, conn):
                cursor.execute(f"""
                    SELECT column_name, data_type 
                    FROM information_schema.columns 
                    WHERE table_schema = '{table_metadata.target_schema}' 
                    AND table_name = '{table_metadata.table_prefix}{table_metadata.name}'
                    ORDER BY ordinal_position
                """)
                return cursor.fetchall()
        
        except Exception as e:
            self.logger.error(f"❌ Erro ao obter colunas da tabela: {str(e)}")
            return []
    
    def alter_column_type(self, table_metadata: TableMetadata, column_name: str, new_type: str):
        """Altera tipo de uma coluna"""
        try:
            full_table_name = f"{table_metadata.target_schema}.{table_metadata.table_prefix}{table_metadata.name}"
            
            with self.target_connection.get_cursor() as (cursor, conn):
                alter_sql = f'ALTER TABLE {full_table_name} ALTER COLUMN "{column_name}" TYPE {new_type}'
                cursor.execute(alter_sql)
                conn.commit()
                
                self.logger.info(f"✅ Coluna {column_name} alterada para {new_type}")
                return True
        
        except Exception as e:
            self.logger.error(f"❌ Erro ao alterar coluna {column_name}: {str(e)}")
            return False
    
    def add_column(self, table_metadata: TableMetadata, column_name: str, column_type: str):
        """Adiciona nova coluna à tabela"""
        try:
            full_table_name = f"{table_metadata.target_schema}.{table_metadata.table_prefix}{table_metadata.name}"
            
            with self.target_connection.get_cursor() as (cursor, conn):
                add_sql = f'ALTER TABLE {full_table_name} ADD COLUMN "{column_name}" {column_type}'
                cursor.execute(add_sql)
                conn.commit()
                
                self.logger.info(f"✅ Coluna {column_name} ({column_type}) adicionada")
                return True
        
        except Exception as e:
            self.logger.error(f"❌ Erro ao adicionar coluna {column_name}: {str(e)}")
            return False
    
    def drop_table(self, table_metadata: TableMetadata):
        """Remove tabela"""
        try:
            full_table_name = f"{table_metadata.target_schema}.{table_metadata.table_prefix}{table_metadata.name}"
            
            with self.target_connection.get_cursor() as (cursor, conn):
                cursor.execute(f"DROP TABLE IF EXISTS {full_table_name}")
                conn.commit()
                
                self.logger.info(f"✅ Tabela {full_table_name} removida")
                return True
        
        except Exception as e:
            self.logger.error(f"❌ Erro ao remover tabela: {str(e)}")
            return False
