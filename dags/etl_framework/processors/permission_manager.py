"""
Gerenciador de Permissões - ETL Framework

Implementa gerenciamento de permissões baseado na lógica das DAGs V3.
"""

import logging
from typing import List, Dict, Any
from etl_framework.strategies.base import TableMetadata


class PermissionManager:
    """
    Gerenciador de permissões de tabelas
    
    Implementa:
    - Aplicação automática de permissões baseada em aux_permissions_tables
    - Grants customizados por usuário/grupo
    - Logs detalhados de permissões aplicadas
    """
    
    def __init__(self, target_connection):
        self.target_connection = target_connection
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def apply_table_permissions(self, table_metadata: TableMetadata) -> bool:
        """
        Aplica permissões automáticas baseadas na tabela aux_permissions_tables
        Baseado na função apply_table_permissions das DAGs V3
        """
        try:
            full_table_name = f"{table_metadata.target_schema}.{table_metadata.table_prefix}{table_metadata.name}"
            
            self.logger.info(f"🔐 Aplicando permissões automáticas para {table_metadata.name}...")
            
            with self.target_connection.get_cursor() as (cursor, conn):
                # Busca permissões da tabela aux_permissions_tables
                cursor.execute(f"""
                    SELECT username, table_name, grant_type, active
                    FROM {table_metadata.target_schema}.aux_permissions_tables
                    WHERE (table_name = '*' OR lower('{full_table_name}') LIKE '%' || lower(table_name) || '%')
                    AND active = true
                """)
                permissions = cursor.fetchall()
                
                grants_applied = 0
                grants_failed = 0
                
                for user, table_pattern, permission_type, active in permissions:
                    try:
                        # Aplica grant
                        grant_sql = f"{permission_type} ON TABLE {full_table_name} TO {user}"
                        cursor.execute(grant_sql)
                        grants_applied += 1
                        self.logger.debug(f"✅ Grant aplicado: {permission_type} ON {full_table_name} TO {user}")
                    except Exception as grant_error:
                        grants_failed += 1
                        self.logger.warning(f"⚠️ Erro ao aplicar grant para {user}: {str(grant_error)}")
                
                conn.commit()
                
                self.logger.info(f"🔐 Permissões aplicadas: {grants_applied} sucessos, {grants_failed} falhas")
                return grants_applied > 0
        
        except Exception as e:
            self.logger.error(f"❌ Erro ao aplicar permissões para {table_metadata.name}: {str(e)}")
            return False
    
    def apply_custom_grants(self, table_metadata: TableMetadata, grants: List[str]) -> bool:
        """
        Aplica grants customizados
        
        Args:
            table_metadata: Metadados da tabela
            grants: Lista de comandos GRANT (ex: ["GRANT SELECT ON TABLE ... TO user"])
        """
        try:
            full_table_name = f"{table_metadata.target_schema}.{table_metadata.table_prefix}{table_metadata.name}"
            
            self.logger.info(f"🔐 Aplicando {len(grants)} grants customizados para {table_metadata.name}...")
            
            with self.target_connection.get_cursor() as (cursor, conn):
                grants_applied = 0
                grants_failed = 0
                
                for grant_sql in grants:
                    try:
                        # Substitui placeholder se necessário
                        grant_sql_formatted = grant_sql.replace('{table_name}', full_table_name)
                        cursor.execute(grant_sql_formatted)
                        grants_applied += 1
                        self.logger.debug(f"✅ Grant customizado aplicado: {grant_sql_formatted}")
                    except Exception as grant_error:
                        grants_failed += 1
                        self.logger.warning(f"⚠️ Erro ao aplicar grant customizado: {str(grant_error)}")
                
                conn.commit()
                
                self.logger.info(f"🔐 Grants customizados: {grants_applied} sucessos, {grants_failed} falhas")
                return grants_applied > 0
        
        except Exception as e:
            self.logger.error(f"❌ Erro ao aplicar grants customizados: {str(e)}")
            return False
    
    def revoke_permissions(self, table_metadata: TableMetadata, users: List[str], permissions: List[str] = None) -> bool:
        """
        Revoga permissões de usuários específicos
        
        Args:
            table_metadata: Metadados da tabela
            users: Lista de usuários
            permissions: Lista de permissões a revogar (default: ['SELECT', 'INSERT', 'UPDATE', 'DELETE'])
        """
        try:
            if permissions is None:
                permissions = ['SELECT', 'INSERT', 'UPDATE', 'DELETE']
            
            full_table_name = f"{table_metadata.target_schema}.{table_metadata.table_prefix}{table_metadata.name}"
            
            self.logger.info(f"🔐 Revogando permissões para {len(users)} usuários em {table_metadata.name}...")
            
            with self.target_connection.get_cursor() as (cursor, conn):
                revokes_applied = 0
                revokes_failed = 0
                
                for user in users:
                    for permission in permissions:
                        try:
                            revoke_sql = f"REVOKE {permission} ON TABLE {full_table_name} FROM {user}"
                            cursor.execute(revoke_sql)
                            revokes_applied += 1
                            self.logger.debug(f"✅ Revoke aplicado: {revoke_sql}")
                        except Exception as revoke_error:
                            revokes_failed += 1
                            self.logger.warning(f"⚠️ Erro ao revogar {permission} de {user}: {str(revoke_error)}")
                
                conn.commit()
                
                self.logger.info(f"🔐 Revokes aplicados: {revokes_applied} sucessos, {revokes_failed} falhas")
                return revokes_applied > 0
        
        except Exception as e:
            self.logger.error(f"❌ Erro ao revogar permissões: {str(e)}")
            return False
    
    def get_table_permissions(self, table_metadata: TableMetadata) -> List[Dict[str, Any]]:
        """
        Lista permissões atuais da tabela
        """
        try:
            full_table_name = f"{table_metadata.target_schema}.{table_metadata.table_prefix}{table_metadata.name}"
            
            with self.target_connection.get_cursor() as (cursor, conn):
                cursor.execute(f"""
                    SELECT 
                        grantee,
                        privilege_type,
                        is_grantable
                    FROM information_schema.table_privileges 
                    WHERE table_schema = '{table_metadata.target_schema}' 
                    AND table_name = '{table_metadata.table_prefix}{table_metadata.name}'
                    ORDER BY grantee, privilege_type
                """)
                
                permissions = []
                for grantee, privilege_type, is_grantable in cursor.fetchall():
                    permissions.append({
                        'grantee': grantee,
                        'privilege_type': privilege_type,
                        'is_grantable': is_grantable == 'YES'
                    })
                
                self.logger.info(f"📋 Encontradas {len(permissions)} permissões para {table_metadata.name}")
                return permissions
        
        except Exception as e:
            self.logger.error(f"❌ Erro ao listar permissões: {str(e)}")
            return []
    
    def copy_permissions_from_table(self, source_table_metadata: TableMetadata, 
                                  target_table_metadata: TableMetadata) -> bool:
        """
        Copia permissões de uma tabela para outra
        """
        try:
            # Obtém permissões da tabela origem
            source_permissions = self.get_table_permissions(source_table_metadata)
            
            if not source_permissions:
                self.logger.warning(f"⚠️ Nenhuma permissão encontrada na tabela origem")
                return False
            
            # Aplica permissões na tabela destino
            target_full_name = f"{target_table_metadata.target_schema}.{target_table_metadata.table_prefix}{target_table_metadata.name}"
            
            with self.target_connection.get_cursor() as (cursor, conn):
                grants_applied = 0
                
                for perm in source_permissions:
                    try:
                        grant_sql = f"GRANT {perm['privilege_type']} ON TABLE {target_full_name} TO {perm['grantee']}"
                        cursor.execute(grant_sql)
                        grants_applied += 1
                        self.logger.debug(f"✅ Permissão copiada: {grant_sql}")
                    except Exception as grant_error:
                        self.logger.warning(f"⚠️ Erro ao copiar permissão: {str(grant_error)}")
                
                conn.commit()
                
                self.logger.info(f"🔐 Copiadas {grants_applied} permissões de {source_table_metadata.name} para {target_table_metadata.name}")
                return grants_applied > 0
        
        except Exception as e:
            self.logger.error(f"❌ Erro ao copiar permissões: {str(e)}")
            return False
