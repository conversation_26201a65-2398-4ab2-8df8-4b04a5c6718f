"""
Processador de Dados - ETL Framework

Implementa transferência de dados robusta baseada na lógica das DAGs V3.
"""

import pandas as pd
import numpy as np
import logging
import time
from io import StringIO
from typing import Optional, List
from etl_framework.strategies.base import TableMetadata


class DataProcessor:
    """
    Processador principal de dados
    
    Implementa:
    - Transferência em chunks
    - Tratamento robusto de erros
    - Inserção com fallbacks automáticos
    - Conversão de tipos
    """
    
    def __init__(self, source_connection, target_connection, chunk_size: int = 50000):
        self.source_connection = source_connection
        self.target_connection = target_connection
        self.chunk_size = chunk_size
        self.logger = logging.getLogger(self.__class__.__name__)
    
    def transfer_table_data(self, 
                          extract_query: str,
                          table_metadata: TableMetadata,
                          is_full_load: bool = False,
                          delete_conditions: Optional[List[str]] = None) -> int:
        """
        Transfere dados do source para target com lógica robusta
        Baseado na função transfer_table_data_chunked das DAGs V3
        """
        try:
            full_table_name = f"{table_metadata.target_schema}.{table_metadata.table_prefix}{table_metadata.name}"
            task_name = f"ETL {table_metadata.name}"
            
            self.logger.info(f"🔄 Iniciando {task_name}")
            self.logger.debug(f"Query: {extract_query}")
            
            with self.target_connection.get_cursor() as (pg_cursor, pg_conn):
                table_created = False
                total_rows_processed = 0
                
                # ✅ Correção sistêmica: Tratamento robusto para diferentes tipos de conexão
                sql_conn = None
                connection_created_directly = False

                try:
                    # Tenta conexão padrão primeiro
                    with self.source_connection.get_connection() as sql_conn:
                        pass  # Apenas testa se a conexão funciona
                except Exception as conn_error:
                    self.logger.warning(f"⚠️ Erro na conexão padrão: {str(conn_error)}")
                    self.logger.info(f"🔄 Tentando conexão robusta para SQL Server...")

                    # Fallback para SQL Server: usar conexão robusta diretamente
                    if hasattr(self.source_connection, 'config') and hasattr(self.source_connection.config, 'db_type'):
                        if self.source_connection.config.db_type.value == 'sqlserver':
                            from etl_framework.connections.sqlserver import _create_robust_sqlserver_connection
                            sql_conn = _create_robust_sqlserver_connection(self.source_connection.config)
                            connection_created_directly = True
                        else:
                            raise conn_error
                    else:
                        raise conn_error

                try:
                    # Se não criou conexão diretamente, usa context manager
                    if not connection_created_directly:
                        with self.source_connection.get_connection() as sql_conn:
                            total_rows_processed = self._process_data_with_connection(
                                sql_conn, extract_query, table_metadata, pg_cursor, pg_conn,
                                is_full_load, delete_conditions
                            )
                    else:
                        # Usa conexão criada diretamente
                        total_rows_processed = self._process_data_with_connection(
                            sql_conn, extract_query, table_metadata, pg_cursor, pg_conn,
                            is_full_load, delete_conditions
                        )
                finally:
                    # Fecha conexão se foi criada diretamente
                    if connection_created_directly and sql_conn:
                        try:
                            sql_conn.close()
                        except:
                            pass

            
            self.logger.info(f"✅ {task_name} concluído - {total_rows_processed} registros processados")
            return total_rows_processed

        except Exception as e:
            self.logger.error(f"❌ Erro em {task_name}: {str(e)}")
            raise

    def _process_data_with_connection(self, sql_conn, extract_query, table_metadata, pg_cursor, pg_conn,
                                    is_full_load, delete_conditions):
        """Processa dados com uma conexão específica"""
        table_created = False
        total_rows_processed = 0
        full_table_name = f"{table_metadata.target_schema}.{table_metadata.table_prefix}{table_metadata.name}"

        # Tenta extração em chunks primeiro
        try:
            self.logger.info(f"📦 Tentando extração em chunks de {self.chunk_size}")

            # ✅ Correção baseada na implementação das DAGs V2/V3 que funciona
            if hasattr(self.source_connection, 'config') and hasattr(self.source_connection.config, 'db_type'):
                if self.source_connection.config.db_type.value == 'sqlserver':
                    # Para SQL Server, usa pymssql.connect diretamente como nas DAGs V2/V3
                    import pymssql
                    config = self.source_connection.config

                    self.logger.info(f"🔗 Usando pymssql.connect diretamente (como DAGs V2/V3): {config.host}:{config.port}")

                    # Cria conexão pymssql direta (igual às DAGs V2/V3)
                    direct_conn = pymssql.connect(
                        server=config.host,
                        user=config.user,
                        password=config.password,
                        database=config.database,
                        port=config.port
                    )

                    chunk_reader = pd.read_sql_query(extract_query, direct_conn, chunksize=self.chunk_size)

                    # Marca para fechar conexão depois
                    self._direct_sql_conn = direct_conn
                else:
                    chunk_reader = pd.read_sql(extract_query, sql_conn, chunksize=self.chunk_size)
            else:
                chunk_reader = pd.read_sql(extract_query, sql_conn, chunksize=self.chunk_size)

            for chunk_num, df_chunk in enumerate(chunk_reader):
                self.logger.info(f"📦 Processando chunk {chunk_num + 1}: {len(df_chunk)} registros")

                # Primeira execução: preparar tabela
                if not table_created:
                    total_rows_processed += self._prepare_table_for_load(
                        df_chunk, full_table_name, table_metadata, is_full_load,  # ✅ Corrigido: usar parâmetro is_full_load
                        delete_conditions, pg_cursor, pg_conn
                    )
                    table_created = True

                # Inserir dados
                if len(df_chunk) > 0:
                    df_chunk = df_chunk.replace({np.nan: None})
                    self._insert_data_in_chunks(df_chunk, full_table_name, pg_cursor)
                    total_rows_processed += len(df_chunk)

                pg_conn.commit()

        except Exception as chunk_error:
            self.logger.warning(f"⚠️ Erro na leitura em chunks: {str(chunk_error)}")
            self.logger.info(f"🔄 Tentando leitura completa como fallback...")

            # Fallback: leitura completa
            # ✅ Correção baseada na implementação das DAGs V2/V3 que funciona
            if hasattr(self.source_connection, 'config') and hasattr(self.source_connection.config, 'db_type'):
                if self.source_connection.config.db_type.value == 'sqlserver':
                    # Para SQL Server, usa pymssql.connect diretamente como nas DAGs V2/V3
                    import pymssql
                    config = self.source_connection.config

                    # Usa conexão direta se já foi criada, senão cria nova
                    if hasattr(self, '_direct_sql_conn') and self._direct_sql_conn:
                        df = pd.read_sql_query(extract_query, self._direct_sql_conn)
                    else:
                        # Cria conexão pymssql direta (igual às DAGs V2/V3)
                        direct_conn = pymssql.connect(
                            server=config.host,
                            user=config.user,
                            password=config.password,
                            database=config.database,
                            port=config.port
                        )
                        df = pd.read_sql_query(extract_query, direct_conn)
                        self._direct_sql_conn = direct_conn
                else:
                    df = pd.read_sql(extract_query, sql_conn)
            else:
                df = pd.read_sql(extract_query, sql_conn)

            self.logger.info(f"📊 Extraídos {len(df)} registros (leitura completa)")

            # Preparar tabela se ainda não foi criada
            if not table_created:
                if len(df) > 0:
                    self._prepare_table_for_load(
                        df, full_table_name, table_metadata, is_full_load,  # ✅ Corrigido: usar parâmetro is_full_load
                        delete_conditions, pg_cursor, pg_conn
                    )
                else:
                    self._handle_empty_table(
                        extract_query, full_table_name, table_metadata, is_full_load,  # ✅ Corrigido: usar parâmetro is_full_load
                        pg_cursor, pg_conn, sql_conn
                    )
                table_created = True

            # Inserir dados
            if len(df) > 0:
                df = df.replace({np.nan: None})
                self._insert_data_in_chunks(df, full_table_name, pg_cursor)
                total_rows_processed = len(df)

            pg_conn.commit()

        # Limpa conexão direta se foi criada
        if hasattr(self, '_direct_sql_conn') and self._direct_sql_conn:
            try:
                self._direct_sql_conn.close()
                self._direct_sql_conn = None
            except:
                pass

        return total_rows_processed
    
    def _prepare_table_for_load(self, df_chunk, full_table_name, table_metadata, 
                              is_full_load, delete_conditions, pg_cursor, pg_conn):
        """Prepara tabela para carga (baseado nas DAGs V3)"""
        from .schema_manager import SchemaManager
        
        schema_manager = SchemaManager(self.target_connection)
        
        if is_full_load:
            # FULL LOAD: sempre dropar e recriar tabela
            self.logger.info(f"🔄 Full Load: Dropando e recriando tabela {full_table_name}")
            pg_cursor.execute(f"DROP TABLE IF EXISTS {full_table_name}")
            pg_conn.commit()
            
            self.logger.info(f"📋 Criando schema para {full_table_name}")
            if not schema_manager.create_table_from_dataframe(table_metadata, df_chunk):
                raise Exception(f"Falha ao criar schema para {full_table_name}")
            pg_conn.commit()
        else:
            # INCREMENTAL: executar DELETEs se necessário
            if delete_conditions:
                for delete_sql in delete_conditions:
                    self.logger.info(f"🗑️ Executando DELETE: {delete_sql}")
                    try:
                        pg_cursor.execute(delete_sql)
                    except Exception as e:
                        if "does not exist" in str(e).lower():
                            self.logger.warning(f"⚠️ Tabela ainda não existe, DELETE ignorado")
                        else:
                            raise
                pg_conn.commit()
            
            # Criar schema se tabela não existir
            table_exists = self.target_connection.table_exists(
                table_metadata.target_schema,
                f"{table_metadata.table_prefix}{table_metadata.name}"
            )
            
            if not table_exists:
                self.logger.info(f"📋 Criando schema para {full_table_name}")
                if not schema_manager.create_table_from_dataframe(table_metadata, df_chunk):
                    raise Exception(f"Falha ao criar schema para {full_table_name}")
                pg_conn.commit()
        
        return 0
    
    def _handle_empty_table(self, extract_query, full_table_name, table_metadata, 
                          is_full_load, pg_cursor, pg_conn, sql_conn):
        """Trata casos de tabelas vazias (baseado nas DAGs V3)"""
        from .schema_manager import SchemaManager
        
        self.logger.warning(f"⚠️ Tratando tabela vazia para {full_table_name}")
        
        schema_manager = SchemaManager(self.target_connection)
        
        if is_full_load:
            self.logger.info(f"🔄 Full Load: Dropando tabela existente {full_table_name}")
            pg_cursor.execute(f"DROP TABLE IF EXISTS {full_table_name}")
            pg_conn.commit()
        
        self.logger.info(f"🔄 Buscando estrutura da tabela para criar tabela vazia...")
        try:
            # Query modificada para obter apenas estrutura (com LIMIT 0)
            if "LIMIT 0" not in extract_query:
                structure_query = extract_query.rstrip(")'") + " LIMIT 0')"
            else:
                structure_query = extract_query
                
            df_structure = pd.read_sql(structure_query, sql_conn)
            
            if len(df_structure.columns) > 0:
                self.logger.info(f"📋 Estrutura obtida: {len(df_structure.columns)} colunas")
                if not schema_manager.create_table_from_dataframe(table_metadata, df_structure):
                    raise Exception(f"Falha ao criar schema para {full_table_name}")
            else:
                # Criar tabela genérica
                df_generic = pd.DataFrame({'temp_column': pd.Series([], dtype='object')})
                if not schema_manager.create_table_from_dataframe(table_metadata, df_generic):
                    raise Exception(f"Falha ao criar schema para {full_table_name}")
            
            pg_conn.commit()
            self.logger.info(f"📥 Tabela {full_table_name} criada sem registros (tabela vazia)")
                            
        except Exception as struct_e:
            self.logger.warning(f"⚠️ Erro ao obter estrutura ({str(struct_e)}), criando tabela genérica...")
            df_generic = pd.DataFrame({'temp_column': pd.Series([], dtype='object')})
            if not schema_manager.create_table_from_dataframe(table_metadata, df_generic):
                raise Exception(f"Falha ao criar schema para {full_table_name}")
            pg_conn.commit()
    
    def _insert_data_in_chunks(self, df, full_table_name, pg_cursor):
        """Insere dados em chunks com tratamento robusto de erros"""
        # Implementação será feita no próximo arquivo devido ao limite de linhas
        from .data_inserter import DataInserter
        
        inserter = DataInserter(self.target_connection)
        inserter.insert_dataframe(df, full_table_name, pg_cursor, self.chunk_size)
