# 📋 Plano de Migração V2 → V4 - Configurações YAML

## 🎯 Objetivo
Migrar todas as configurações hardcoded das DAGs V2 para arquivos YAML estruturados no framework V4, facilitando manutenção e escalabilidade.

## 📊 Análise da Estrutura Atual

### V2 - Configurações Hardcoded
```python
# V2_BRONZE.py
Bronze('dealer').add_tables([
    'atendimento',
    'comissao',
    {...
        "notafiscalitem": {
            "times_per_day": 2
        }
    }
])
```

### V4 - Configurações YAML
```yaml
# systems/{sistema}/bronze_config.yaml
tables:
  tabela_nome:
    type: small|large
    mode: full_only|smart_incremental
    timeout_seconds: 300
    id_field: "campo_id"
```

## 🗂️ Estrutura de Diretórios Proposta

```
etl_framework/
└── systems/
    ├── dealer/              # Sistema Dealer (V2)
    │   ├── system_config.yaml
    │   ├── bronze_config.yaml
    │   └── silver_config.yaml
    ├── syonet/              # Sistema Syonet (já existe)
    ├── oracle_erp/          # Futuro sistema
    └── template/            # Template para novos sistemas
        ├── system_config.yaml
        ├── bronze_config.yaml
        └── silver_config.yaml
```

## 📝 Templates de Configuração

### 1. system_config.yaml - Template Base
```yaml
# Configuração do Sistema {NOME} - ETL Framework V4
system:
  name: "{sistema_nome}"
  description: "{descrição do sistema}"
  tags: ["{tag1}", "{tag2}"]
  
  # Configurações globais
  default_timeout_seconds: 900
  max_parallel_tables: 2
  enable_smart_incremental: true
  enable_validation: true
  max_retries: 2
  retry_delay_minutes: 1

# Configuração da base de dados origem
source_database:
  name: "{nome_conexão}"
  type: "{sqlserver|oracle|postgres}"
  host: "{host}"
  port: {porta}
  database: "{database}"
  user: "{usuário}"
  password: "{senha}"
  default_timeout: 300
  description: "{descrição}"
  
# Configuração da base de dados destino
target_database:
  schema: "dbdwcorporativo"
  table_prefix: "bronze_{sistema}_"
  
# Configurações de performance
performance:
  chunk_size: 50000
  read_chunk_size: 50000
  connection_pool_size: 5
  enable_connection_sharing: false
  enable_lazy_loading: true
  validation_cache_ttl: 3600

# Agendamento
scheduling:
  bronze_schedule: "0 6 * * *"
  silver_schedule: "0 8 * * *"
  timezone: "America/Sao_Paulo"
```

### 2. bronze_config.yaml - Template Base
```yaml
# Configuração Bronze - Sistema {NOME}
tables:
  # Tabelas pequenas (sempre full load)
  {tabela_pequena}:
    type: small
    mode: full_only
    timeout_seconds: 300
    
  # Tabelas grandes (incremental inteligente)
  {tabela_grande}:
    type: large
    mode: smart_incremental
    id_field: "{campo_id}"
    timeout_seconds: 900
    seven_days_timeout_seconds: 300
    max_gap_percentage: 10.0
    max_abs_diff: 10
    
  # Tabela com configuração customizada (V2: times_per_day)
  {tabela_custom}:
    type: large
    mode: smart_incremental
    id_field: "{campo_id}"
    timeout_seconds: 600
    schedule_override: "0 */12 * * *"  # 2x ao dia
```

## 🔧 Script de Migração Automática

### 1. Extrator de Configurações V2
```python
# etl_framework/tools/v2_config_extractor.py
import yaml
import ast
import re

def extract_v2_configs(v2_file_path):
    """Extrai configurações das DAGs V2"""
    with open(v2_file_path, 'r') as f:
        content = f.read()
    
    # Regex para encontrar instâncias Bronze/Silver
    pattern = r"(\w+)\('(\w+)'\)\.add_tables\(\[(.*?)\]\)"
    
    configs = {}
    for match in re.finditer(pattern, content, re.DOTALL):
        layer = match.group(1).lower()
        system = match.group(2)
        tables_str = match.group(3)
        
        # Parse das tabelas
        tables = parse_tables(tables_str)
        configs[system] = {
            'layer': layer,
            'tables': tables
        }
    
    return configs

def generate_yaml_configs(v2_configs, output_dir):
    """Gera arquivos YAML a partir das configs V2"""
    for system, config in v2_configs.items():
        # Cria diretório do sistema
        system_dir = f"{output_dir}/{system}"
        os.makedirs(system_dir, exist_ok=True)
        
        # Gera bronze_config.yaml
        bronze_config = {
            'tables': {}
        }
        
        for table in config['tables']:
            if isinstance(table, dict):
                # Tabela com configuração especial
                table_name = list(table.keys())[0]
                table_config = table[table_name]
                
                bronze_config['tables'][table_name] = {
                    'type': 'large',
                    'mode': 'smart_incremental',
                    'timeout_seconds': 600,
                    'schedule_override': f"0 */{24//table_config.get('times_per_day', 1)} * * *"
                }
            else:
                # Tabela simples
                bronze_config['tables'][table] = {
                    'type': 'small',
                    'mode': 'full_only',
                    'timeout_seconds': 300
                }
        
        # Salva YAML
        with open(f"{system_dir}/bronze_config.yaml", 'w') as f:
            yaml.dump(bronze_config, f, default_flow_style=False)
```

## 📋 Checklist de Migração

### Fase 1: Preparação
- [ ] Criar diretório `systems/dealer/`
- [ ] Executar script de extração de configs V2
- [ ] Revisar configs geradas automaticamente
- [ ] Ajustar conexões de banco de dados

### Fase 2: Validação
- [ ] Comparar contagem de tabelas V2 vs V4
- [ ] Verificar mapeamento de `times_per_day` → `schedule_override`
- [ ] Validar configurações de timeout
- [ ] Testar conexões de banco

### Fase 3: Implementação
- [ ] Criar DAG v4_bronze_dealer.py
- [ ] Configurar orquestrador V4
- [ ] Executar em paralelo com V2 (validação)
- [ ] Comparar resultados

### Fase 4: Transição
- [ ] Desativar schedule V2
- [ ] Monitorar execuções V4
- [ ] Arquivar código V2
- [ ] Documentar mudanças

## 🔄 Mapeamento V2 → V4

| V2 Parameter | V4 YAML Field | Notas |
|-------------|---------------|-------|
| `times_per_day` | `schedule_override` | Converte para cron expression |
| `Bronze('sistema')` | `systems/{sistema}/` | Cria diretório dedicado |
| Lista de tabelas | `tables:` | Estrutura YAML |
| Hardcoded timeout | `timeout_seconds` | Configurável por tabela |
| N/A | `type: small\|large` | Classificação automática |
| N/A | `mode:` | Estratégia de carga |

## 📈 Benefícios da Migração

1. **Manutenibilidade**: Alterações sem modificar código Python
2. **Versionamento**: Configs em Git com histórico claro
3. **Escalabilidade**: Adicionar sistemas sem modificar framework
4. **Padronização**: Mesma estrutura para todos os sistemas
5. **Documentação**: YAML auto-documentado com comentários

## 🚀 Próximos Passos

1. **Imediato**: Criar template base para sistema dealer
2. **Curto prazo**: Executar script de migração automática
3. **Médio prazo**: Validar em ambiente de desenvolvimento
4. **Longo prazo**: Migrar todos os sistemas V2 existentes