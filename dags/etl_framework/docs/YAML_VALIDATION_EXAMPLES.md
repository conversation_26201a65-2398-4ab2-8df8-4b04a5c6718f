# 🔍 Exemplos de Validação - Templates YAML

## ✅ Flexibilidade Comprovada

### 1. **Sistema SQL Server (Dealer)**
```yaml
# system_config.yaml
system:
  name: "dealer"
  description: "Sistema de concessionárias"
  
source_database:
  type: "sqlserver"
  host: "**********"
  port: 1433
  database: "dealer_prod"
  user: "etl_user"
  password: "${DEALER_PASSWORD}"  # Airflow Variable

# bronze_config.yaml
tables:
  # 90+ tabelas como no V2
  atendimento:
    type: small
    mode: full_only
    
  notafiscalitem:
    type: large
    mode: smart_incremental
    id_field: "id_item"
    schedule_override: "0 */12 * * *"  # 2x/dia como V2
```

### 2. **Sistema Oracle (ERP)**
```yaml
# system_config.yaml
system:
  name: "oracle_erp"
  description: "ERP corporativo Oracle"
  
source_database:
  type: "oracle"
  host: "oracle.empresa.com"
  port: 1521
  database: "ERPDB"
  user: "etl_reader"
  password: "${ORACLE_ERP_PWD}"

# bronze_config.yaml
tables:
  # Tabelas com nomes Oracle típicos
  GL_LEDGERS:
    type: small
    mode: full_only
    
  GL_JE_LINES:
    type: large
    mode: smart_incremental
    id_field: "JE_LINE_ID"
    timeout_seconds: 1800  # 30min para volume grande
```

### 3. **Sistema PostgreSQL (Aplicação Web)**
```yaml
# system_config.yaml
system:
  name: "webapp"
  description: "Aplicação web moderna"
  
source_database:
  type: "postgres"
  host: "postgres.cloud.com"
  port: 5432
  database: "webapp_prod"
  user: "readonly"
  password: "${WEBAPP_DB_PWD}"

performance:
  chunk_size: 100000  # Chunks maiores para Postgres

# bronze_config.yaml
tables:
  users:
    type: large
    mode: smart_incremental
    id_field: "user_id"
    
  events:
    type: large
    mode: smart_incremental
    id_field: "event_id"
    schedule_override: "0 * * * *"  # Hourly
```

### 4. **Sistema MySQL (E-commerce)**
```yaml
# system_config.yaml
system:
  name: "ecommerce"
  description: "Plataforma e-commerce"
  
source_database:
  type: "mysql"
  host: "mysql.ecommerce.com"
  port: 3306
  database: "shop"
  user: "etl"
  password: "${MYSQL_PWD}"

# bronze_config.yaml
tables:
  orders:
    type: large
    mode: smart_incremental
    id_field: "order_id"
    schedule_override: "*/30 * * * *"  # A cada 30min
    
  order_items:
    type: large
    mode: smart_incremental
    id_field: "item_id"
    sql_file: "order_items_custom.sql"  # Query complexa
```

## 🎯 Casos Especiais Validados

### 1. **Tabela sem Chave Primária**
```yaml
log_eventos:
  type: large
  mode: full_only  # Sem PK = sempre full
  timeout_seconds: 600
```

### 2. **Tabela Gigante (bilhões de registros)**
```yaml
historico_transacoes:
  type: large
  mode: incremental_only  # NUNCA faz full
  id_field: "transaction_id"
  timeout_seconds: 3600  # 1 hora
  chunk_size: 200000  # Chunks grandes
```

### 3. **Tabela com Delete Complexo**
```yaml
relacionamentos:
  type: large
  mode: smart_incremental
  id_field: "id"
  sql_file: "relacionamentos_delete_complex.sql"
```

### 4. **Sistema com Múltiplos Schemas**
```yaml
# Adicione ao source_database se necessário
source_database:
  type: "postgres"
  default_schema: "public"
  additional_schemas: ["audit", "history"]
```

## 📊 Comparação: Código vs YAML

### Antes (V2 - Python)
```python
Bronze('dealer').add_tables([
    'atendimento',
    'comissao',
    {'notafiscalitem': {'times_per_day': 2}},
    {'titulo': {'times_per_day': 1}}
])
# 100+ linhas de código
# Modificação = editar código Python
# Deploy = reiniciar Airflow
```

### Depois (V4 - YAML)
```yaml
tables:
  atendimento:
    type: small
    
  comissao:
    type: small
    
  notafiscalitem:
    schedule_override: "0 */12 * * *"
    
  titulo:
    type: large
    mode: smart_incremental
    id_field: "id_titulo"
# Modificação = editar YAML
# Deploy = automático
```

## ✅ Conclusão

Os templates são:
- **Simples**: Configuração mínima funciona para 80% dos casos
- **Flexíveis**: Suportam todos os sistemas existentes
- **Robustos**: Validações automáticas previnem erros
- **Escaláveis**: Fácil adicionar novos parâmetros sem quebrar existentes