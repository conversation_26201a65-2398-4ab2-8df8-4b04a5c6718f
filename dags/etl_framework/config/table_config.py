"""
Configuração de Tabelas - ETL Framework

Sistema flexível de configuração de tabelas baseado na lógica das DAGs V3.
Implementa herança de configurações para evitar repetições.
"""

from dataclasses import dataclass, field
from typing import Dict, Any, Optional, List
from enum import Enum
import yaml
import os
from pathlib import Path


class TableType(Enum):
    """Tipos de tabela para estratégias automáticas"""
    SMALL = "small"          # Tabelas pequenas (sempre full load)
    LARGE = "large"          # Tabelas grandes (incremental com fallback)
    CUSTOM = "custom"        # Tabelas com lógica customizada


class IncrementalMode(Enum):
    """Modos de incremental disponíveis"""
    DAILY = "daily"          # Incremental diário
    SEVEN_DAYS = "seven_days"  # Incremental 7 dias
    SMART = "smart"          # Estratégia inteligente com fallback
    FULL_ONLY = "full_only"  # Apenas full load


@dataclass
class TableConfig:
    """
    Configuração completa de uma tabela para ETL
    
    Baseado na estrutura TABLES_CONFIG das DAGs V3 mas mais flexível
    """
    name: str
    table_type: TableType = TableType.LARGE
    incremental_mode: IncrementalMode = IncrementalMode.SMART
    
    # Configurações básicas
    source_table: Optional[str] = None
    id_field: Optional[str] = None
    select_fields: Optional[str] = None
    
    # Configurações de schema
    source_schema: str = "public"
    target_schema: str = "bronze"
    table_prefix: str = ""
    
    # Configurações incrementais
    incremental_fields: str = "dt_inc,dt_alt"
    daily_filter: Optional[str] = None
    seven_days_filter: Optional[str] = None
    
    # SQLs customizados
    custom_sql: Optional[Dict[str, str]] = None
    
    # Configurações de processamento
    chunk_size: int = 50000
    timeout_seconds: int = 300
    max_gap_percentage: float = 10.0
    
    # Configurações de validação
    enable_validation: bool = True
    max_abs_diff: int = 10
    
    # Permissões customizadas
    custom_grants: Optional[List[str]] = None
    
    # Metadados adicionais
    description: Optional[str] = None
    tags: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        """Validações e configurações automáticas após inicialização"""
        # Se não tem id_field, é tabela pequena
        if not self.id_field:
            self.table_type = TableType.SMALL
            self.incremental_mode = IncrementalMode.FULL_ONLY
        
        # Se não tem source_table, usa o próprio nome
        if not self.source_table:
            self.source_table = self.name
        
        # Configura filtros padrão apenas se não foram explicitamente definidos
        # IMPORTANTE: Framework V4 usa DELETE baseado em IDs, não filtros de data
        if self.daily_filter is None:
            # Não cria filtros padrão - Framework V4 não usa filtros para DELETE
            pass
        
        if self.seven_days_filter is None:
            # Não cria filtros padrão - Framework V4 não usa filtros para DELETE  
            pass
    
    def _build_default_daily_filter(self) -> str:
        """Constrói filtro diário padrão baseado nas DAGs V3"""
        return """(TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP)
                  OR
                  TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= DATE_TRUNC(''day'', CURRENT_TIMESTAMP))"""

    def _build_default_seven_days_filter(self) -> str:
        """Constrói filtro 7 dias padrão baseado nas DAGs V3"""
        return """(TIMESTAMP ''epoch'' + dt_inc * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days''
                  OR
                  TIMESTAMP ''epoch'' + dt_alt * INTERVAL ''1 millisecond'' >= CURRENT_TIMESTAMP - INTERVAL ''7 days'')"""
    
    def get_full_table_name(self) -> str:
        """Retorna nome completo da tabela de destino"""
        return f"{self.target_schema}.{self.table_prefix}{self.name}"
    
    def get_source_full_name(self) -> str:
        """Retorna nome completo da tabela de origem"""
        return f"{self.source_schema}.{self.source_table}"
    
    def is_small_table(self) -> bool:
        """Verifica se é tabela pequena (sempre full load)"""
        return self.table_type == TableType.SMALL or not self.id_field
    
    def has_custom_sql(self, sql_type: str = None) -> bool:
        """Verifica se tem SQL customizado"""
        if not self.custom_sql:
            return False
        
        if sql_type:
            return sql_type in self.custom_sql
        
        return len(self.custom_sql) > 0
    
    def get_custom_sql(self, sql_type: str) -> Optional[str]:
        """Obtém SQL customizado por tipo"""
        if not self.custom_sql:
            return None
        
        return self.custom_sql.get(sql_type)
    
    def should_use_smart_incremental(self) -> bool:
        """Verifica se deve usar estratégia inteligente"""
        return (self.incremental_mode == IncrementalMode.SMART and 
                not self.is_small_table())
    
    def to_table_metadata(self):
        """Converte para TableMetadata (compatibilidade com strategies)"""
        from ..strategies.base import TableMetadata
        
        return TableMetadata(
            name=self.name,
            source_table=self.source_table,
            id_field=self.id_field,
            select_fields=self.select_fields,
            custom_sql=self.custom_sql,
            incremental_field=self.incremental_fields,
            schema=self.source_schema,
            target_schema=self.target_schema,
            table_prefix=self.table_prefix
        )
    
    @classmethod
    def from_dict(cls, name: str, config_dict: Dict[str, Any]) -> 'TableConfig':
        """Cria TableConfig a partir de dicionário (compatibilidade com DAGs V3)"""
        return cls(
            name=name,
            source_table=config_dict.get('source'),
            id_field=config_dict.get('id_field'),
            select_fields=config_dict.get('select'),
            custom_sql=config_dict.get('custom_sql'),
            table_type=TableType.SMALL if not config_dict.get('id_field') else TableType.LARGE,
            incremental_mode=IncrementalMode.FULL_ONLY if not config_dict.get('id_field') else IncrementalMode.SMART
        )
    
    def to_dict(self) -> Dict[str, Any]:
        """Converte para dicionário"""
        return {
            'name': self.name,
            'table_type': self.table_type.value,
            'incremental_mode': self.incremental_mode.value,
            'source_table': self.source_table,
            'id_field': self.id_field,
            'select_fields': self.select_fields,
            'source_schema': self.source_schema,
            'target_schema': self.target_schema,
            'table_prefix': self.table_prefix,
            'incremental_fields': self.incremental_fields,
            'custom_sql': self.custom_sql,
            'chunk_size': self.chunk_size,
            'timeout_seconds': self.timeout_seconds,
            'max_gap_percentage': self.max_gap_percentage,
            'enable_validation': self.enable_validation,
            'max_abs_diff': self.max_abs_diff,
            'custom_grants': self.custom_grants,
            'description': self.description,
            'tags': self.tags
        }


class TableConfigLoader:
    """
    Carregador de configurações com herança de valores padrão
    
    Hierarquia de configurações:
    1. Valores padrão do framework
    2. Valores padrão do sistema (system_config.yaml)
    3. Valores específicos da tabela (bronze_config.yaml / silver_config.yaml)
    """
    
    # Valores padrão do framework por tipo de tabela
    FRAMEWORK_DEFAULTS = {
        'small': {
            'type': 'small',
            'mode': 'full_only',
            'timeout_seconds': 300,
            'enable_validation': True,
            'chunk_size': 50000
        },
        'large': {
            'type': 'large',
            'mode': 'smart_incremental',
            'timeout_seconds': 900,
            'enable_validation': True,
            'chunk_size': 50000,
            'seven_days_timeout_seconds': 300
        },
        'custom': {
            'type': 'custom',
            'timeout_seconds': 900,
            'enable_validation': True
        }
    }
    
    def __init__(self, system_path: str):
        """
        Inicializa carregador de configurações
        
        Args:
            system_path: Caminho para o diretório do sistema
        """
        self.system_path = Path(system_path)
        self.system_config = self._load_system_config()
        self.system_defaults = self._extract_system_defaults()
        
    def _load_system_config(self) -> Dict[str, Any]:
        """Carrega system_config.yaml"""
        config_file = self.system_path / 'system_config.yaml'
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f) or {}
        return {}
    
    def _extract_system_defaults(self) -> Dict[str, Any]:
        """Extrai valores padrão do sistema"""
        defaults = {}
        
        # Timeout padrão do sistema
        system_section = self.system_config.get('system', {})
        if 'default_timeout_seconds' in system_section:
            defaults['timeout_seconds'] = system_section['default_timeout_seconds']
            
        # Performance defaults
        performance = self.system_config.get('performance', {})
        if 'chunk_size' in performance:
            defaults['chunk_size'] = performance['chunk_size']
            
        # Validation defaults
        etl_settings = self.system_config.get('etl_settings', {})
        validation = etl_settings.get('default_validation', {})
        if validation:
            defaults.update(validation)
            
        return defaults
    
    def load_table_configs(self, config_type: str = 'bronze') -> Dict[str, TableConfig]:
        """
        Carrega todas as configurações de tabelas com herança aplicada
        
        Args:
            config_type: 'bronze' ou 'silver'
            
        Returns:
            Dicionário de TableConfig por nome de tabela
        """
        config_file = self.system_path / f'{config_type}_config.yaml'
        if not config_file.exists():
            return {}
            
        with open(config_file, 'r', encoding='utf-8') as f:
            config_data = yaml.safe_load(f) or {}
            
        tables = config_data.get('tables', {})
        table_configs = {}
        
        for table_name, table_data in tables.items():
            # Aplica herança e cria TableConfig
            merged_data = self._apply_inheritance(table_data)
            table_configs[table_name] = self._create_table_config(table_name, merged_data)
            
        return table_configs
    
    def _apply_inheritance(self, table_data: Dict[str, Any]) -> Dict[str, Any]:
        """
        Aplica herança de configurações
        
        Ordem de precedência (maior para menor):
        1. Configuração específica da tabela
        2. Valores padrão do sistema
        3. Valores padrão do framework
        """
        # Determina o tipo da tabela
        table_type = table_data.get('type', 'small')
        
        # 1. Começa com defaults do framework
        merged = self.FRAMEWORK_DEFAULTS.get(table_type, {}).copy()
        
        # 2. Aplica defaults do sistema
        merged.update(self.system_defaults)
        
        # 3. Aplica configurações específicas da tabela
        merged.update(table_data)
        
        return merged
    
    def _create_table_config(self, name: str, data: Dict[str, Any]) -> TableConfig:
        """Cria objeto TableConfig a partir dos dados com herança"""
        # Mapeia configurações YAML para TableConfig
        table_type = TableType.SMALL if data.get('type') == 'small' else TableType.LARGE
        if data.get('type') == 'custom':
            table_type = TableType.CUSTOM
            
        incremental_mode = IncrementalMode.FULL_ONLY
        if data.get('mode') == 'smart_incremental':
            incremental_mode = IncrementalMode.SMART
        elif data.get('mode') == 'incremental_only':
            incremental_mode = IncrementalMode.DAILY
            
        return TableConfig(
            name=name,
            table_type=table_type,
            incremental_mode=incremental_mode,
            source_table=data.get('source_table', name),
            id_field=data.get('id_field'),
            select_fields=data.get('select_fields'),
            chunk_size=data.get('chunk_size', 50000),
            timeout_seconds=data.get('timeout_seconds', 900),
            max_gap_percentage=data.get('max_gap_percentage', 10.0),
            max_abs_diff=data.get('max_abs_diff', 10),
            enable_validation=data.get('enable_validation', True),
            custom_sql=data.get('custom_sql'),
            source_schema=data.get('source_schema', 'public'),
            target_schema=self.system_config.get('target_database', {}).get('schema', 'dbdwcorporativo'),
            table_prefix=self.system_config.get('target_database', {}).get('table_prefix', '')
        )
